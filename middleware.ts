// middleware.ts
import { NextResponse, type NextRequest } from "next/server";
import { updateSession } from "./lib/supabase/middleware";
import { ROUTE_CONFIG } from "./lib/types/middleware";
import { initializeServerCache } from "./lib/serverInit";

let isWarmedUp = false;

// Define static paths that should be skipped
const SKIP_PATHS = ["/_next", "/api/", "/favicon.ico", "."];

export async function middleware(request: NextRequest) {
  try {
    const path = request.nextUrl.pathname;

    // Early return for static files and API routes using a more efficient check
    if (
      SKIP_PATHS.some(
        (skipPath) => path.startsWith(skipPath) || path.includes(skipPath),
      )
    ) {
      return NextResponse.next();
    }

    // Warm up cache on first request in production with timeout
    if (process.env.NODE_ENV === "production" && !isWarmedUp) {
      try {
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Cache warmup timeout")), 10000),
        );
        await Promise.race([initializeServerCache(), timeoutPromise]);
        isWarmedUp = true;
      } catch (error) {
        console.error("❌ Cache warmup failed:", error);
        // Continue execution even if cache warm-up fails
      }
    }

    // Security headers
    const securityHeaders = {
      "X-Frame-Options": "DENY",
      "X-Content-Type-Options": "nosniff",
      "Referrer-Policy": "strict-origin-when-cross-origin",
      "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
      "X-XSS-Protection": "1; mode=block",
    };

    // Check authentication requirements
    const requiresAuth = ROUTE_CONFIG.protected.some((route) =>
      path.startsWith(route),
    );
    const isAuthRoute = ROUTE_CONFIG.auth.some((route) =>
      path.startsWith(route),
    );

    let finalResponse: NextResponse;

    if (path === "/reset-password") {
      finalResponse = NextResponse.next();
    } else if (requiresAuth || isAuthRoute) {
      const result = await updateSession(request);
      finalResponse = typeof result === "function" ? result(request) : result;
    } else {
      finalResponse = NextResponse.next();
    }

    // Apply security headers
    Object.entries(securityHeaders).forEach(([key, value]) => {
      finalResponse.headers.set(key, value);
    });

    // Add pathname to headers for server-side components
    finalResponse.headers.set("x-pathname", path);

    // Cache control for specific routes
    if (path === "/homepage") {
      finalResponse.headers.set(
        "Cache-Control",
        "public, s-maxage=300, stale-while-revalidate=59",
      );
    }

    return finalResponse;
  } catch (error) {
    console.error("Middleware error:", error);
    // Return a basic response in case of errors to prevent complete failure
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico|public/|assets/|images/|static/).*)",
  ],
};
