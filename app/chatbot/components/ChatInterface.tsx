import React, { useState, useEffect, useRef } from "react";
import { Send, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import ChatMessage from "./ChatMessage";
import {
  sendMessageToClaude,
  validateAWSConfig,
  DatasetContext,
} from "../services/bedrockService";
import { AuthorData, PaperResult } from "../services/openAlexApi";

interface Message {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
  results?: AuthorData[] | PaperResult[];
  resultType?: "authors" | "papers" | "institutions";
}

interface QueryData {
  count: string;
  type: string;
  keyword: string;
  timeWindow: string;
  yearRange?: {
    start: number;
    end: number;
  };
  filters?: {
    journal?: string;
    affiliation?: string;
    author?: string;
  };
}

interface ChatInterfaceProps {
  queryData: QueryData | null;
  authors?: AuthorData[];
  papers?: PaperResult[];
}

const ChatInterface = ({ queryData, authors, papers }: ChatInterfaceProps) => {
  // Claude integration state
  const [isClaudeEnabled, setIsClaudeEnabled] = useState(true); // Default to enabled
  const [isLoadingClaude, setIsLoadingClaude] = useState(false);
  const [awsConfigValid, setAwsConfigValid] = useState(false);

  // Ref for auto-scrolling
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Ref to track if we've already added the filter results message
  const filterMessageAddedRef = useRef(false);

  // Auto-scroll to bottom when messages change
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Initialize with sample conversation based on the query data
  const [messages, setMessages] = useState<Message[]>(() => {
    if (!queryData) return [];

    let userQuery = `Give me top ${queryData.count} ${queryData.type} in ${queryData.keyword}`;
    if (queryData.yearRange) {
      userQuery += ` (${queryData.yearRange.start}-${queryData.yearRange.end})`;
    } else if (queryData.timeWindow) {
      userQuery += ` (${queryData.timeWindow} month analysis)`;
    }

    // Add filter information to the user query
    if (queryData.filters) {
      const filterParts = [];
      if (queryData.filters.affiliation) {
        filterParts.push(`institution: ${queryData.filters.affiliation}`);
      }
      if (queryData.filters.journal) {
        filterParts.push(`journal: ${queryData.filters.journal}`);
      }
      if (queryData.filters.author) {
        filterParts.push(`author: ${queryData.filters.author}`);
      }
      if (filterParts.length > 0) {
        userQuery += ` with filters: ${filterParts.join(", ")}`;
      }
    }
    const baseMessages: Message[] = [
      {
        id: "1",
        type: "user",
        content: userQuery,
        timestamp: new Date(),
      },
      {
        id: "2",
        type: "assistant",
        content: `I'm searching for the top ${queryData.count} ${
          queryData.type
        } in "${
          queryData.keyword
        }" using the OpenAlex research database. This analysis will ${
          queryData.yearRange
            ? `focus on publications and citations from ${queryData.yearRange.start} to ${queryData.yearRange.end}`
            : `include citation velocity calculations based on the last ${queryData.timeWindow} months of data`
        }.${
          queryData.filters && Object.keys(queryData.filters).length > 0
            ? ` I'm applying the following filters to narrow down the results: ${Object.entries(
                queryData.filters,
              )
                .filter(([_, value]) => value && value.trim())
                .map(([key, value]) => `${key}: "${value}"`)
                .join(", ")}.`
            : ""
        } Please check the sidebar for real-time results as they load.`,
        timestamp: new Date(),
        results: [],
        resultType:
          queryData.type === "authors"
            ? ("authors" as const)
            : ("papers" as const),
      },
    ];
    return baseMessages;
  });

  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Auto-scroll when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Check AWS configuration on mount
  useEffect(() => {
    const checkConfig = async () => {
      const isValid = await validateAWSConfig();
      setAwsConfigValid(isValid);
    };
    checkConfig();
  }, []);

  // Reset filter message flag when query changes
  useEffect(() => {
    filterMessageAddedRef.current = false;
  }, [queryData]);

  // Add a follow-up message when results are loaded with filters
  useEffect(() => {
    if (
      (authors?.length || papers?.length) &&
      queryData?.filters &&
      !filterMessageAddedRef.current
    ) {
      const hasActiveFilters = Object.values(queryData.filters).some(
        (filter) => filter && filter.trim(),
      );

      if (hasActiveFilters) {
        const resultCount = authors?.length || papers?.length || 0;
        const resultType = queryData.type;

        if (resultCount > 0) {
          const filterMessage: Message = {
            id: `filter-results-${Date.now()}`,
            type: "assistant",
            content: `Found ${resultCount} ${resultType} matching your filter criteria. The results in the sidebar have been filtered to show only ${resultType} that match your specified filters. You can see the applied filters displayed as badges at the top of the results panel.`,
            timestamp: new Date(),
          };

          setMessages((prev) => [...prev, filterMessage]);
          filterMessageAddedRef.current = true; // Mark that we've added the message
        }
      }
    }
  }, [authors, papers, queryData]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Check message limit (7 messages total)
    const userMessageCount = messages.filter(
      (msg) => msg.type === "user",
    ).length;
    if (userMessageCount >= 7) {
      alert(
        "You've reached the maximum of 7 messages. Please refresh to start a new conversation.",
      );
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);

    try {
      let assistantResponse: string;

      if (isClaudeEnabled && awsConfigValid) {
        // Use Claude Sonnet 4.0 via AWS Bedrock with streaming
        setIsLoadingClaude(true);

        const datasetContext: DatasetContext = {
          authors,
          papers,
          queryData: queryData || undefined,
        };

        const conversationHistory = messages.map((msg) => ({
          role:
            msg.type === "user" ? ("user" as const) : ("assistant" as const),
          content: msg.content,
        }));

        conversationHistory.push({ role: "user", content: inputValue });

        // Create a placeholder message for streaming
        const streamingMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: "assistant",
          content: "",
          timestamp: new Date(),
        };

        setMessages((prev) => [...prev, streamingMessage]);

        // Stream the response
        assistantResponse = await sendMessageToClaude(
          conversationHistory,
          datasetContext,
          (chunk: string) => {
            // Update the streaming message with each chunk
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === streamingMessage.id
                  ? { ...msg, content: msg.content + chunk }
                  : msg,
              ),
            );
            // Auto-scroll during streaming
            setTimeout(scrollToBottom, 50);
          },
        );
      } else {
        // Fallback to mock response
        assistantResponse = generateResponse(inputValue).content;

        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: "assistant",
          content: assistantResponse,
          timestamp: new Date(),
        };

        setMessages((prev) => [...prev, assistantMessage]);
      }

      // For Claude streaming, the message is already added and updated in real-time
      // No need to add another message here
    } catch (error) {
      console.error("Error sending message:", error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content:
          "Sorry, I encountered an error. Please try again or check your AWS configuration.",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setIsLoadingClaude(false);
    }
  };

  const generateResponse = (query: string): Message => {
    const lowerQuery = query.toLowerCase();
    if (lowerQuery.includes("author") || lowerQuery.includes("researcher")) {
      return {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content:
          "Here are the top AI researchers I found. Check the sidebar for detailed tabulated results.",
        timestamp: new Date(),
        results: [],
        resultType: "authors" as const,
      };
    } else if (lowerQuery.includes("paper")) {
      return {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content:
          "Here are the top research papers matching your query. Results are displayed in the sidebar table.",
        timestamp: new Date(),
        results: [],
        resultType: "papers" as const,
      };
    } else {
      return {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content:
          "I can help you find research papers, authors, and institutions. Try asking me something like 'Show me top AI researchers' or 'Find papers on machine learning'.",
        timestamp: new Date(),
      };
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-screen relative">
      {/* Messages area with bottom padding for fixed input */}
      <div className="flex-1 overflow-y-auto px-8 py-8 pb-32">
        <div className="max-w-3xl mx-auto space-y-6">
          {messages.map((message) => (
            <ChatMessage
              key={message.id}
              message={message}
              showResults={true}
            />
          ))}

          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="w-6 h-6 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
              <span className="ml-3 text-gray-500 font-light">
                Searching...
              </span>
            </div>
          )}

          {/* Invisible div for auto-scrolling */}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Chat input fixed at bottom and centered */}
      <div className="fixed bottom-0 left-0 right-0 border-t border-gray-200 bg-white z-10">
        <div className="max-w-3xl mx-auto px-8 py-6">
          {/* Claude Controls */}
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={isClaudeEnabled}
                  onChange={(e) => setIsClaudeEnabled(e.target.checked)}
                  disabled={!awsConfigValid}
                  className="rounded border-gray-300"
                />
                <span className="text-sm text-gray-700">
                  Use Claude Sonnet 4.0 {isLoadingClaude && "(Loading...)"}
                </span>
              </label>
              {!awsConfigValid && (
                <div className="flex items-center space-x-1 text-amber-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-xs">AWS config required</span>
                </div>
              )}
            </div>
            <span className="text-xs text-gray-500">
              Messages: {messages.filter((msg) => msg.type === "user").length}/7
            </span>
          </div>

          <div className="flex items-end space-x-4">
            <Textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Ask me about research papers, authors, or institutions..."
              className="flex-1 min-h-[48px] resize-none border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:border-gray-400 focus:ring-0 transition-all font-light text-gray-900 placeholder:text-gray-500"
              rows={1}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className="bg-black hover:bg-gray-800 text-white border-0 rounded-full w-12 h-12 p-0 transition-all"
            >
              <Send className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
