import { useState, useEffect, useCallback, useRef } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  fetchAuthorsWithRetry,
  fetchPapersWithRetry,
  AuthorData,
  PaperResult,
  SortOption,
  ResearcherDataCache,
  PaperDataCache,
  PaperSortOption,
} from "..//services/openAlexApi";
import { Button } from "@/components/ui/button";
import { Loader2, ChevronDown, ChevronRight } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "./ui/tooltip";

interface QueryData {
  count: string;
  type: string;
  keyword: string;
  timeWindow: string;
  yearRange?: {
    start: number;
    end: number;
  };
  filters?: {
    journal?: string;
    affiliation?: string;
    author?: string;
  };
}

interface ResultsSidebarProps {
  queryData: QueryData | null;
  onDataUpdate?: (authors: AuthorData[], papers: PaperResult[]) => void;
}

const ResultsSidebar = ({ queryData, onDataUpdate }: ResultsSidebarProps) => {
  const [rawAuthors, setRawAuthors] = useState<AuthorData[]>([]); // Store unsorted data
  const [authors, setAuthors] = useState<AuthorData[]>([]);
  const [rawPapers, setRawPapers] = useState<PaperResult[]>([]); // Store unsorted papers
  const [papers, setPapers] = useState<PaperResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchInProgressRef = useRef(false);
  const [progress, setProgress] = useState({
    current: 0,
    total: 100,
    stage: "",
  });
  const [sortBy, setSortBy] = useState<SortOption>("citation_velocity");
  const [paperSortBy, setPaperSortBy] =
    useState<PaperSortOption>("citation_velocity");
  const [cacheData, setCacheData] = useState<ResearcherDataCache | null>(null);
  const [paperCacheData, setPaperCacheData] = useState<PaperDataCache | null>(
    null,
  );
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Toggle row expansion
  const toggleRowExpansion = (paperId: string) => {
    setExpandedRows((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(paperId)) {
        newSet.delete(paperId);
      } else {
        newSet.add(paperId);
      }
      return newSet;
    });
  };

  // Local sorting function
  const sortAuthors = useCallback(
    (authorsToSort: AuthorData[], sortOption: SortOption): AuthorData[] => {
      return [...authorsToSort].sort((a, b) => {
        switch (sortOption) {
          case "top_author_score":
            return b.top_author_score - a.top_author_score;
          case "trending_author_score":
            return b.trending_author_score - a.trending_author_score;
          case "citation_velocity":
          default:
            return b.avg_citation_velocity - a.avg_citation_velocity;
        }
      });
    },
    [],
  );

  // Local sorting function for papers
  const sortPapers = useCallback(
    (
      papersToSort: PaperResult[],
      sortOption: PaperSortOption,
    ): PaperResult[] => {
      return [...papersToSort].sort((a, b) => {
        switch (sortOption) {
          case "top_paper_score":
            return b.top_paper_score - a.top_paper_score;
          case "trending_paper_score":
            return b.trending_paper_score - a.trending_paper_score;
          case "citation_velocity":
          default:
            return b.citation_velocity - a.citation_velocity;
        }
      });
    },
    [],
  );

  // Effect to handle local sorting when sortBy changes
  useEffect(() => {
    if (rawAuthors.length > 0 && queryData) {
      const topK = parseInt(queryData.count);
      const sorted = sortAuthors(rawAuthors, sortBy);
      const sliced = sorted.slice(0, topK);
      setAuthors(sliced); // Apply topK limit

      // Update parent component with sorted data
      onDataUpdate?.(sliced, papers);
    }
  }, [rawAuthors, sortBy, sortAuthors, queryData, onDataUpdate, papers]);

  // Effect to handle local paper sorting when paperSortBy changes
  useEffect(() => {
    if (rawPapers.length > 0 && queryData) {
      const topK = parseInt(queryData.count);
      const sorted = sortPapers(rawPapers, paperSortBy);
      const sliced = sorted.slice(0, topK);
      setPapers(sliced);

      // Update parent component with sorted data
      onDataUpdate?.(authors, sliced);
    }
  }, [rawPapers, paperSortBy, sortPapers, queryData, onDataUpdate, authors]);

  const fetchData = useCallback(async () => {
    if (!queryData) return;

    // Prevent multiple concurrent searches using ref
    if (searchInProgressRef.current) {
      console.log("🚫 ResultsSidebar - Search already in progress, skipping");
      return;
    }

    // DEBUG: Log received query data and filters
    console.log("🔍 ResultsSidebar - Received queryData:", queryData);
    console.log("🔍 ResultsSidebar - Filters received:", queryData.filters);

    searchInProgressRef.current = true;
    setLoading(true);
    setError(null);
    setAuthors([]);
    setPapers([]);
    setRawAuthors([]);
    setRawPapers([]);

    try {
      const topK = parseInt(queryData.count);

      if (queryData.type === "authors") {
        // Check if we can use cached data for the same keyword
        const useCache = Boolean(
          cacheData && cacheData.keyword === queryData.keyword,
        );

        const response = await fetchAuthorsWithRetry(
          queryData.keyword,
          topK,
          "citation_velocity", // Default sorting, will be overridden locally
          queryData.timeWindow ? parseInt(queryData.timeWindow) : 12, // Time window in months
          setProgress,
          useCache,
          cacheData || undefined,
          queryData.filters, // Pass filters from query
          queryData.yearRange,
        );

        // Store the cache data and all authors
        setCacheData(response.cacheData);
        setRawAuthors(response.cacheData.allAuthors); // Store all authors for local sorting
        // Note: setAuthors will be handled by the useEffect that watches rawAuthors and sortBy

        // Update parent component with data - will be called again by the sorting effect
        // onDataUpdate will be called by the sorting useEffect
      } else if (queryData.type === "papers") {
        // Check if we can use cached data for the same keyword
        const useCache = Boolean(
          paperCacheData && paperCacheData.keyword === queryData.keyword,
        );

        const response = await fetchPapersWithRetry(
          queryData.keyword,
          topK,
          queryData.timeWindow ? parseInt(queryData.timeWindow) : 12, // Time window in months
          setProgress,
          useCache,
          paperCacheData || undefined,
          queryData.filters, // Pass filters from query
          queryData.yearRange,
        );

        // Store the cache data and all papers
        setPaperCacheData(response.cacheData);
        setRawPapers(response.cacheData.allPapers); // Store all papers for local sorting
        // Note: setPapers will be handled by the useEffect that watches rawPapers and paperSortBy

        // Update parent component with data - will be called again by the sorting effect
        // onDataUpdate will be called by the sorting useEffect
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
      searchInProgressRef.current = false;
    }
  }, [queryData, cacheData, paperCacheData, onDataUpdate]);

  useEffect(() => {
    if (
      queryData &&
      (queryData.type === "authors" || queryData.type === "papers")
    ) {
      fetchData();
    }
  }, [queryData, fetchData]);

  return (
    <TooltipProvider>
      <div className="h-full flex flex-col">
        <div className="border-b p-4">
          <h2 className="text-lg font-semibold">Research Results</h2>
          {queryData && (
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                Top {queryData.count} {queryData.type} in "{queryData.keyword}"
                (
                {queryData.yearRange
                  ? `${queryData.yearRange.start}-${queryData.yearRange.end} period analysis`
                  : `${queryData.timeWindow} month analysis`}
                )
              </p>
              {queryData.filters && (
                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="text-xs text-gray-500">
                    Filters applied:
                  </span>
                  {queryData.filters.affiliation && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      Institution: {queryData.filters.affiliation}
                    </span>
                  )}
                  {queryData.filters.journal && (
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                      Journal: {queryData.filters.journal}
                    </span>
                  )}
                  {queryData.filters.author && (
                    <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
                      Author: {queryData.filters.author}
                    </span>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {loading && (
            <div className="flex flex-col items-center justify-center py-8 space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <div className="text-center">
                <p className="text-sm font-medium">{progress.stage}</p>
                <div className="w-64 bg-gray-200 rounded-full h-2 mt-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress.current}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {Math.round(progress.current)}% complete
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-600 mb-2">Error loading results</p>
              <p className="text-sm text-gray-500">{error}</p>
              <button
                onClick={fetchData}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          )}

          {!loading &&
            !error &&
            authors.length > 0 &&
            queryData?.type === "authors" && (
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-sm font-medium text-gray-700">
                      Top Researchers - {queryData?.keyword}
                    </h3>
                    <div className="flex gap-2">
                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant={
                              sortBy === "top_author_score"
                                ? "default"
                                : "outline"
                            }
                            onClick={() => setSortBy("top_author_score")}
                            className="text-xs"
                          >
                            Top Author Score
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          sideOffset={10}
                          className="bg-gray-900 text-white border-gray-700 shadow-xl z-50"
                        >
                          <div className="max-w-xs p-2">
                            <p className="font-semibold mb-2 text-yellow-300">
                              Top Author Score
                            </p>
                            <p className="text-sm leading-relaxed">
                              Composite score based on: H-index (40%), recent
                              citations (30%), publication velocity (20%), and
                              venue quality (10%). Measures overall research
                              impact and productivity.
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant={
                              sortBy === "trending_author_score"
                                ? "default"
                                : "outline"
                            }
                            onClick={() => setSortBy("trending_author_score")}
                            className="text-xs"
                          >
                            Trending Score
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          sideOffset={10}
                          className="bg-gray-900 text-white border-gray-700 shadow-xl z-50"
                        >
                          <div className="max-w-xs p-2">
                            <p className="font-semibold mb-2 text-green-300">
                              Trending Score
                            </p>
                            <p className="text-sm leading-relaxed">
                              Measures recent momentum: Recent citations (50%),
                              publication frequency in last 2 years (30%), and
                              citation acceleration (20%). Identifies
                              researchers gaining traction.
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                  <div className="border rounded-lg overflow-hidden">
                    <div className="max-h-[600px] overflow-y-auto">
                      <Table>
                        <TableHeader className="sticky top-0 bg-white z-10">
                          <TableRow>
                            <TableHead className="text-xs">Author</TableHead>
                            <TableHead className="text-xs">
                              Institution
                            </TableHead>
                            <TableHead className="text-xs">H-Index</TableHead>
                            <TableHead className="text-xs">i10-Index</TableHead>
                            <TableHead className="text-xs">
                              Recent Papers (2y)
                            </TableHead>
                            <TableHead className="text-xs">Top Score</TableHead>
                            <TableHead className="text-xs">
                              Trending Score
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {authors.map((author) => (
                            <TableRow
                              key={author.author_id}
                              className="hover:bg-gray-50"
                            >
                              <TableCell className="text-xs font-medium max-w-[150px]">
                                <div
                                  className="truncate"
                                  title={author.author_name}
                                >
                                  <a
                                    href={author.author_id}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:text-blue-800 hover:underline"
                                  >
                                    {author.author_name}
                                  </a>
                                </div>
                              </TableCell>
                              <TableCell className="text-xs text-gray-600 max-w-[120px]">
                                <div
                                  className="truncate"
                                  title={author.institutions}
                                >
                                  {author.institutions}
                                </div>
                              </TableCell>
                              <TableCell className="text-xs">
                                {author.h_index}
                              </TableCell>
                              <TableCell className="text-xs">
                                {author.i10_index}
                              </TableCell>
                              <TableCell className="text-xs">
                                {author.papers_last_2_years}
                              </TableCell>
                              <TableCell className="text-xs">
                                <span className="font-medium text-blue-600">
                                  {author.top_author_score.toLocaleString()}
                                </span>
                              </TableCell>
                              <TableCell className="text-xs">
                                <span className="font-medium text-green-600">
                                  {author.trending_author_score.toFixed(3)}
                                </span>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-500 space-y-1">
                  <p>
                    <strong>Total Results:</strong> {authors.length}
                  </p>
                  <p>
                    <strong>Last Updated:</strong> Just now
                  </p>
                  <p>
                    <strong>Data Source:</strong> OpenAlex API
                  </p>
                </div>
              </div>
            )}

          {!loading &&
            !error &&
            papers.length > 0 &&
            queryData?.type === "papers" && (
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-sm font-medium text-gray-700">
                      Top Research Papers - {queryData?.keyword}
                    </h3>
                    <div className="flex gap-2">
                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant={
                              paperSortBy === "top_paper_score"
                                ? "default"
                                : "outline"
                            }
                            onClick={() => setPaperSortBy("top_paper_score")}
                            className="text-xs"
                          >
                            Top Paper Score
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          sideOffset={10}
                          className="bg-gray-900 text-white border-gray-700 shadow-xl z-50"
                        >
                          <div className="max-w-xs p-2">
                            <p className="font-semibold mb-2 text-blue-300">
                              Top Paper Score
                            </p>
                            <p className="text-sm leading-relaxed">
                              Composite score based on: Total citations (40%),
                              venue prestige/H-index (30%), recent citation
                              velocity (20%), and author reputation (10%).
                              Measures overall paper impact.
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant={
                              paperSortBy === "trending_paper_score"
                                ? "default"
                                : "outline"
                            }
                            onClick={() =>
                              setPaperSortBy("trending_paper_score")
                            }
                            className="text-xs"
                          >
                            Trending Score
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          sideOffset={10}
                          className="bg-gray-900 text-white border-gray-700 shadow-xl z-50"
                        >
                          <div className="max-w-xs p-2">
                            <p className="font-semibold mb-2 text-green-300">
                              Trending Score
                            </p>
                            <p className="text-sm leading-relaxed">
                              Measures recent momentum: Citations in last 2
                              years (60%), citation acceleration rate (25%), and
                              recency bonus (15%). Identifies papers gaining
                              attention.
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant={
                              paperSortBy === "citation_velocity"
                                ? "default"
                                : "outline"
                            }
                            onClick={() => setPaperSortBy("citation_velocity")}
                            className="text-xs"
                          >
                            Citation Velocity
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent
                          side="top"
                          align="center"
                          sideOffset={10}
                          className="bg-gray-900 text-white border-gray-700 shadow-xl z-50"
                        >
                          <div className="max-w-xs p-2">
                            <p className="font-semibold mb-2 text-purple-300">
                              Citation Velocity
                            </p>
                            <p className="text-sm leading-relaxed">
                              Average citations per year since publication.
                              Calculated as total citations divided by years
                              since publication. Shows sustained citation rate
                              over time.
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                  <div className="border rounded-lg overflow-hidden">
                    <div className="max-h-[600px] overflow-y-auto">
                      <Table>
                        <TableHeader className="sticky top-0 bg-white z-10">
                          <TableRow>
                            <TableHead className="text-xs">Title</TableHead>
                            <TableHead className="text-xs">Authors</TableHead>
                            <TableHead className="text-xs">
                              Institution
                            </TableHead>
                            <TableHead className="text-xs">
                              Journal/Venue
                            </TableHead>
                            <TableHead className="text-xs">Year</TableHead>
                            <TableHead className="text-xs">
                              Citations (2y)
                            </TableHead>
                            <TableHead className="text-xs">
                              Venue H-Index
                            </TableHead>
                            <TableHead className="text-xs">Top Score</TableHead>
                            <TableHead className="text-xs">
                              Trending Score
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {papers.map((paper) => {
                            const isExpanded = expandedRows.has(paper.id);
                            return (
                              <>
                                <TableRow
                                  key={paper.id}
                                  className="hover:bg-gray-50 cursor-pointer"
                                  onClick={() => toggleRowExpansion(paper.id)}
                                >
                                  <TableCell className="text-xs font-medium max-w-[200px]">
                                    <div className="flex items-center gap-2">
                                      {isExpanded ? (
                                        <ChevronDown className="h-3 w-3 text-gray-400" />
                                      ) : (
                                        <ChevronRight className="h-3 w-3 text-gray-400" />
                                      )}
                                      <div
                                        className="truncate"
                                        title={paper.title}
                                      >
                                        <a
                                          href={paper.paper_url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 hover:underline"
                                          onClick={(e) => e.stopPropagation()}
                                        >
                                          {paper.title}
                                        </a>
                                      </div>
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-xs text-gray-600 max-w-[150px]">
                                    <div
                                      className="truncate"
                                      title={paper.authors}
                                    >
                                      <a
                                        href={paper.openalex_url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-gray-600 hover:text-gray-800 hover:underline"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        {paper.authors}
                                      </a>
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-xs text-gray-600 max-w-[120px]">
                                    <div
                                      className="truncate"
                                      title={paper.institution || "N/A"}
                                    >
                                      {paper.institution || "N/A"}
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-xs text-gray-600 max-w-[120px]">
                                    <div
                                      className="truncate"
                                      title={paper.venue}
                                    >
                                      {paper.venue}
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    {new Date(
                                      paper.publication_date,
                                    ).getFullYear()}
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    {paper.citations_last_2_years.toLocaleString()}
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    {paper.venue_h_index}
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    <span className="font-medium text-blue-600">
                                      {paper.top_paper_score.toLocaleString()}
                                    </span>
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    <span className="font-medium text-green-600">
                                      {paper.trending_paper_score.toFixed(1)}
                                    </span>
                                  </TableCell>
                                </TableRow>
                                {isExpanded && paper.abstract && (
                                  <TableRow key={`${paper.id}-abstract`}>
                                    <TableCell
                                      colSpan={9}
                                      className="bg-gray-50 p-4"
                                    >
                                      <div className="space-y-2">
                                        <h4 className="text-sm font-medium text-gray-700">
                                          Abstract
                                        </h4>
                                        <p className="text-xs text-gray-600 leading-relaxed">
                                          {paper.abstract}
                                        </p>
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                )}
                                {isExpanded && !paper.abstract && (
                                  <TableRow key={`${paper.id}-no-abstract`}>
                                    <TableCell
                                      colSpan={7}
                                      className="bg-gray-50 p-4"
                                    >
                                      <div className="text-xs text-gray-500 italic">
                                        Abstract not available for this paper.
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                )}
                              </>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div>

                <div className="text-xs text-gray-500 space-y-1">
                  <p>
                    <strong>Total Results:</strong> {papers.length}
                  </p>
                  <p>
                    <strong>Last Updated:</strong> Just now
                  </p>
                  <p>
                    <strong>Data Source:</strong> OpenAlex API
                  </p>
                </div>
              </div>
            )}

          {!loading &&
            !error &&
            queryData &&
            queryData.type !== "authors" &&
            queryData.type !== "papers" && (
              <div className="text-center py-8 text-gray-500">
                <p>Select "Researchers" or "Research Papers" to see results</p>
              </div>
            )}

          {!loading && !error && !queryData && (
            <div className="text-center py-8 text-gray-500">
              <p>Use the query builder to search for researchers or papers</p>
            </div>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default ResultsSidebar;
