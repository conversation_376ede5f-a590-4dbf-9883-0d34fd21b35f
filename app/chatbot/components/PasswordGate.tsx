"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Lock, Eye, EyeOff } from "lucide-react";

interface PasswordGateProps {
  children: React.ReactNode;
}

const CORRECT_PASSWORD = "Curie2";
const STORAGE_KEY = "chatbot_access_granted";
const STORAGE_EXPIRY_HOURS = 24; // Password access expires after 24 hours

// Utility functions for localStorage with expiry
const setPasswordAccess = () => {
  const now = new Date();
  const item = {
    value: true,
    expiry: now.getTime() + (STORAGE_EXPIRY_HOURS * 60 * 60 * 1000), // 24 hours from now
  };
  localStorage.setItem(STORAGE_KEY, JSON.stringify(item));
};

const getPasswordAccess = (): boolean => {
  if (typeof window === "undefined") return false;
  
  const itemStr = localStorage.getItem(STORAGE_KEY);
  if (!itemStr) return false;

  try {
    const item = JSON.parse(itemStr);
    const now = new Date();

    if (now.getTime() > item.expiry) {
      localStorage.removeItem(STORAGE_KEY);
      return false;
    }

    return item.value === true;
  } catch (error) {
    localStorage.removeItem(STORAGE_KEY);
    return false;
  }
};

const PasswordGate: React.FC<PasswordGateProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  // Check localStorage on component mount
  useEffect(() => {
    const hasAccess = getPasswordAccess();
    setIsAuthenticated(hasAccess);
    setIsLoading(false);
  }, []);

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (password === CORRECT_PASSWORD) {
      setPasswordAccess();
      setIsAuthenticated(true);
    } else {
      setError("Incorrect password. Please try again.");
      setPassword("");
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Show loading state while checking localStorage
  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Show password form if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="w-full max-w-md p-8 bg-white border border-gray-200 rounded-lg shadow-lg">
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Lock className="w-8 h-8 text-gray-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Chatbot Access
            </h1>
            <p className="text-gray-600">
              Please enter the password to access the chatbot
            </p>
          </div>

          <form onSubmit={handlePasswordSubmit} className="space-y-4">
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password"
                className="pr-10"
                autoFocus
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                {showPassword ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>

            {error && (
              <div className="text-red-600 text-sm text-center">
                {error}
              </div>
            )}

            <Button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              disabled={!password.trim()}
            >
              Access Chatbot
            </Button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-500">
            Access will be remembered for 24 hours
          </div>
        </div>
      </div>
    );
  }

  // Render children if authenticated
  return <>{children}</>;
};

export default PasswordGate;
