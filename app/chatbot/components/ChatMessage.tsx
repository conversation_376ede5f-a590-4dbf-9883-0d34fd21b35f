import React from "react";
import { Bot } from "lucide-react";

interface Message {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
  results?: any[];
  resultType?: "authors" | "papers" | "institutions";
}

interface ChatMessageProps {
  message: Message;
  showResults?: boolean;
}

const ChatMessage = ({ message, showResults = false }: ChatMessageProps) => {
  const isUser = message.type === "user";

  // Function to format the message content
  const formatContent = (content: string) => {
    // Split by double newlines to get paragraphs
    const paragraphs = content.split("\n\n");

    return paragraphs.map((paragraph, index) => {
      // Check if it's a numbered list item
      if (paragraph.match(/^\d+\.\s\*\*.*\*\*/)) {
        const parts = paragraph.split(" - ");
        const titlePart = parts[0];
        const descriptionPart = parts[1];

        // Extract number and title
        const match = titlePart.match(/^(\d+)\.\s\*\*(.*?)\*\*/);
        if (match) {
          const [, number, title] = match;
          return (
            <div key={index} className="mb-4 last:mb-0">
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-6 h-6 bg-gray-800 text-white text-xs rounded-full flex items-center justify-center font-medium mt-0.5">
                  {number}
                </span>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">{title}</h4>
                  {descriptionPart && (
                    <p className="text-gray-700 text-sm leading-relaxed">
                      {descriptionPart}
                    </p>
                  )}
                </div>
              </div>
            </div>
          );
        }
      }

      // Regular paragraph
      return (
        <p key={index} className="mb-3 last:mb-0 leading-relaxed">
          {paragraph}
        </p>
      );
    });
  };

  return (
    <div className={`flex ${isUser ? "justify-end" : "justify-start"} mb-8`}>
      <div
        className={`flex max-w-[80%] ${isUser ? "flex-row-reverse" : "flex-row"} items-start space-x-3`}
      >
        {!isUser && (
          <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-gray-100 mr-3">
            <Bot className="h-4 w-4 text-gray-600" />
          </div>
        )}

        <div
          className={`rounded-2xl px-6 py-4 ${
            isUser
              ? "bg-black text-white"
              : "bg-gray-50 text-gray-900 border border-gray-200"
          }`}
        >
          <div className="text-sm font-light">
            {isUser ? (
              <div className="leading-relaxed">{message.content}</div>
            ) : (
              <div className="space-y-1">{formatContent(message.content)}</div>
            )}
          </div>

          {showResults && message.results && (
            <div className="mt-3 text-xs text-gray-500 border-t border-gray-200 pt-2">
              Results displayed in sidebar →
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
