import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const FilterPanel = () => {
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);

  const addFilter = (filter: string) => {
    if (!selectedFilters.includes(filter)) {
      setSelectedFilters([...selectedFilters, filter]);
    }
  };

  const removeFilter = (filter: string) => {
    setSelectedFilters(selectedFilters.filter((f) => f !== filter));
  };

  return (
    <Card className="w-full lg:w-80 h-fit">
      <CardHeader>
        <CardTitle className="text-lg">Filters</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Topic
          </label>
          <Select onValueChange={(value) => addFilter(`Topic: ${value}`)}>
            <SelectTrigger>
              <SelectValue placeholder="Select topic" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="artificial-intelligence">
                Artificial Intelligence
              </SelectItem>
              <SelectItem value="machine-learning">Machine Learning</SelectItem>
              <SelectItem value="computer-vision">Computer Vision</SelectItem>
              <SelectItem value="natural-language">
                Natural Language Processing
              </SelectItem>
              <SelectItem value="robotics">Robotics</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Region
          </label>
          <Select onValueChange={(value) => addFilter(`Region: ${value}`)}>
            <SelectTrigger>
              <SelectValue placeholder="Select region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="united-states">United States</SelectItem>
              <SelectItem value="europe">Europe</SelectItem>
              <SelectItem value="china">China</SelectItem>
              <SelectItem value="canada">Canada</SelectItem>
              <SelectItem value="asia-pacific">Asia Pacific</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Publication Year
          </label>
          <Select onValueChange={(value) => addFilter(`Year: ${value}`)}>
            <SelectTrigger>
              <SelectValue placeholder="Select year range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024">2024</SelectItem>
              <SelectItem value="2023">2023</SelectItem>
              <SelectItem value="2022-2024">2022-2024</SelectItem>
              <SelectItem value="2020-2024">2020-2024</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Impact Metric
          </label>
          <Select onValueChange={(value) => addFilter(`Impact: ${value}`)}>
            <SelectTrigger>
              <SelectValue placeholder="Sort by impact" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="citations">Most Cited</SelectItem>
              <SelectItem value="h-index">H-Index</SelectItem>
              <SelectItem value="recent">Most Recent</SelectItem>
              <SelectItem value="trending">Trending</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {selectedFilters.length > 0 && (
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Active Filters
            </label>
            <div className="flex flex-wrap gap-2">
              {selectedFilters.map((filter, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="cursor-pointer hover:bg-red-100"
                  onClick={() => removeFilter(filter)}
                >
                  {filter} ×
                </Badge>
              ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 w-full"
              onClick={() => setSelectedFilters([])}
            >
              Clear All
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FilterPanel;
