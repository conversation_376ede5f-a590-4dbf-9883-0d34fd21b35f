import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { X, Refresh<PERSON><PERSON>, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { sendMessageToClaude } from "../services/bedrockService";

interface DebugResult {
  step: string;
  status: "success" | "error" | "pending";
  message: string;
  details?: any;
}

interface DebugModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DebugModal = ({ isOpen, onClose }: DebugModalProps) => {
  const [debugResults, setDebugResults] = useState<DebugResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  if (!isOpen) return null;

  const addResult = (result: DebugResult) => {
    setDebugResults((prev) => [...prev, result]);
  };

  const clearResults = () => {
    setDebugResults([]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    clearResults();

    // Step 1: Check environment variables (client-side check)
    addResult({
      step: "Environment Check",
      status: "pending",
      message: "Checking environment setup...",
    });

    // Check server-side AWS configuration
    setTimeout(async () => {
      try {
        const response = await fetch("/api/chatbot", {
          method: "GET",
        });

        if (response.ok) {
          const data = await response.json();
          if (data.configured) {
            addResult({
              step: "Environment Check",
              status: "success",
              message: "AWS configuration is properly set on server",
              details: `Region: ${data.region}, Server-side credentials: Configured`,
            });
          } else {
            addResult({
              step: "Environment Check",
              status: "error",
              message: "AWS configuration missing on server",
              details:
                "Please add AWS_REGION, AWS_ACCESS, and AWS_SECRET to your .env.local file on the server.",
            });
          }
        } else {
          addResult({
            step: "Environment Check",
            status: "error",
            message: "Failed to check server configuration",
            details: "Could not connect to the chatbot API endpoint.",
          });
        }
      } catch (error) {
        addResult({
          step: "Environment Check",
          status: "error",
          message: "Error checking server configuration",
          details: `Error: ${error instanceof Error ? error.message : String(error)}`,
        });
      }
    }, 500);

    // Step 2: Test Claude API
    setTimeout(async () => {
      addResult({
        step: "Claude API Test",
        status: "pending",
        message: "Testing Claude Sonnet 4.0 API...",
      });

      try {
        let streamedResponse = "";
        const response = await sendMessageToClaude(
          [
            {
              role: "user",
              content: 'Hello, can you respond with just "Working"?',
            },
          ],
          {
            queryData: {
              count: "10",
              type: "authors",
              keyword: "test",
              timeWindow: "12",
            },
          },
          (chunk: string) => {
            streamedResponse += chunk;
            // Update the result in real-time
            addResult({
              step: "Claude API Test",
              status: "pending",
              message: "Streaming response...",
              details: `Response so far: ${streamedResponse}`,
            });
          },
        );

        addResult({
          step: "Claude API Test",
          status: "success",
          message: "Claude API is working with streaming!",
          details: `Complete response: ${response}`,
        });
      } catch (error) {
        addResult({
          step: "Claude API Test",
          status: "error",
          message: "Error calling Claude API",
          details: error instanceof Error ? error.message : String(error),
        });
      }

      setIsRunning(false);
    }, 1000);
  };

  const getStatusIcon = (status: DebugResult["status"]) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "pending":
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">AWS Bedrock Debug Console</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="mb-4">
            <Button
              onClick={runDiagnostics}
              disabled={isRunning}
              className="mr-4"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Running...
                </>
              ) : (
                "Run Diagnostics"
              )}
            </Button>
            <Button
              onClick={clearResults}
              variant="outline"
              disabled={isRunning}
            >
              Clear
            </Button>
          </div>

          <div className="space-y-4">
            {debugResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(result.status)}
                    <span className="font-medium">{result.step}</span>
                  </div>
                  <span
                    className={`text-xs px-2 py-1 rounded ${
                      result.status === "success"
                        ? "bg-green-100 text-green-800"
                        : result.status === "error"
                          ? "bg-red-100 text-red-800"
                          : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    {result.status}
                  </span>
                </div>
                <p className="text-gray-700 text-sm mb-2">{result.message}</p>
                {result.details && (
                  <div className="bg-gray-100 p-2 rounded text-xs">
                    <strong>Details:</strong>
                    <pre className="mt-1 whitespace-pre-wrap">
                      {typeof result.details === "string"
                        ? result.details
                        : JSON.stringify(result.details, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>

          {debugResults.length === 0 && (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">
                Click "Run Diagnostics" to start debugging
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DebugModal;
