import Link from "next/link";

interface StatCardProps {
  title: string;
  data: any;
  type: "list" | "number";
  increase?: number;
  showIncrease?: boolean;
  additionalContent?: React.ReactNode;
}

export default function StatCard({
  title,
  data,
  type,
  increase,
  showIncrease,
  additionalContent,
}: StatCardProps) {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4 text-black">{title}</h2>
      {type === "list" ? (
        <ul className="space-y-2 text-black">
          {data.map((item: any, index: number) => (
            <li key={index} className="flex justify-between items-center">
              {item.link ? (
                <Link href={item.link} className="hover:underline">
                  <span>{item.title || item.name || item.username}</span>
                </Link>
              ) : (
                <span>{item.title || item.name || item.username}</span>
              )}
              <div className="flex items-center">
                {showIncrease && item.increase !== undefined && (
                  <span className="mr-2 text-sm font-medium text-green-600">
                    +{item.increase}
                  </span>
                )}
                <span>
                  {item.rightText ||
                    item.views ||
                    (item.createdAt &&
                      new Date(item.createdAt).toLocaleDateString())}
                </span>
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <div className="flex items-center justify-between">
          <p className="text-3xl font-bold text-black">{data}</p>
          {showIncrease && increase !== undefined && (
            <span className="text-sm font-medium text-green-600">
              +{increase}
            </span>
          )}
        </div>
      )}
      {additionalContent && <div className="mt-4">{additionalContent}</div>}
    </div>
  );
}
