"use client";

import { useState, useEffect } from "react";
import StatCard from "./StatCard";
import { ChevronDown, ChevronUp } from "lucide-react";

interface DashboardStats {
  topArticles: { title: string; views: number; slug: string }[];
  topPlaylists: { name: string; views: number }[];
  recentUsers: { username: string; createdAt: string }[];
  recentArticles: {
    title: string;
    createdAt: string;
    views: number;
    slug: string;
  }[];
  totalNewsletterUsers: number;
  totalPaidUsers: number;
  totalUsers: number;
  newUsersLast24Hours: number;
  newNewsletterUsersLast24Hours: number;
  newPaidUsersLast24Hours: number;
  latestNewsletterSubscribers: { email: string; createdAt: string }[];
  latestPaidUsers: {
    id: string;
    username: string;
    email: string;
    createdAt: string;
  }[];
  latestUsers: {
    id: string;
    username: string;
    email: string;
    createdAt: string;
    isWebsite: boolean;
  }[];
  recentSearches: {
    query: string;
    user: { username: string; email: string } | null;
    createdAt: string;
  }[];
  recentSearchesLast24Hours: number;
  totalSearches: number;
  recentTrendAnalyses: {
    query: string;
    user: { username: string; email: string };
    response: any;
    createdAt: string;
  }[];
  recentTrendAnalysesLast24Hours: number;
  totalTrendAnalyses: number;
  topTrendAnalysisUsers: {
    username: string;
    trendAnalysisResponses: {
      query: string;
      createdAt: string;
      user: { username: string; email: string };
    }[];
  }[];
}

export const dynamic = "force-dynamic";

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [expandedUsers, setExpandedUsers] = useState<{
    [key: string]: boolean;
  }>({});

  const toggleUserExpansion = (username: string) => {
    setExpandedUsers((prev) => ({ ...prev, [username]: !prev[username] }));
  };

  useEffect(() => {
    const fetchStats = async () => {
      const response = await fetch("/api/admin/dashboard-stats");
      if (response.ok) {
        const data = await response.json();
        console.log(data.latestPaidUsers);

        setStats(data);
      } else {
        console.error("Failed to fetch dashboard stats");
      }
    };

    fetchStats();
  }, []);

  if (!stats) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6 text-black">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          title="Top 10 Articles"
          data={stats.topArticles.map((article) => ({
            ...article,
            rightText: article.views.toString(),
            link: `/article/${article.slug}`,
          }))}
          type="list"
        />
        <StatCard
          title="Top 5 Playlists"
          data={stats.topPlaylists.map((playlist) => ({
            ...playlist,
            rightText: playlist.views.toString(),
          }))}
          type="list"
        />
        <StatCard
          title="Recent Articles"
          data={stats.recentArticles.map((article) => ({
            ...article,
            rightText: article.views.toString(),
            link: `/article/${article.slug}`,
            tooltip: `Added on ${new Date(article.createdAt).toLocaleDateString()}`,
          }))}
          type="list"
        />
        <StatCard
          title="Newsletter Users"
          data={stats.totalNewsletterUsers || 0}
          type="number"
          increase={stats.newNewsletterUsersLast24Hours || 0}
          showIncrease={true}
          additionalContent={
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Latest Subscribers</h3>
              <ul className="text-sm">
                {Array.isArray(stats.latestNewsletterSubscribers) &&
                  stats.latestNewsletterSubscribers.map((subscriber, index) => (
                    <li key={index} className="mb-1">
                      {subscriber.email} -{" "}
                      {new Date(subscriber.createdAt).toLocaleDateString()}
                    </li>
                  ))}
              </ul>
            </div>
          }
        />
        <StatCard
          title="Paid Users"
          data={stats.totalPaidUsers || 0}
          type="number"
          increase={stats.newPaidUsersLast24Hours || 0}
          showIncrease={true}
          additionalContent={
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Latest Paid Users</h3>
              <ul className="text-sm">
                {Array.isArray(stats.latestPaidUsers) &&
                  stats.latestPaidUsers.map((user) => (
                    <li key={user.id} className="mb-1">
                      {user.email || "N/A"} -{" "}
                    </li>
                  ))}
              </ul>
            </div>
          }
        />
        <StatCard
          title="Total Users"
          data={stats.totalUsers || 0}
          type="number"
          increase={stats.newUsersLast24Hours || 0}
          showIncrease={true}
          additionalContent={
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Latest Users</h3>
              <ul className="text-sm">
                {Array.isArray(stats.latestUsers) &&
                  stats.latestUsers.map((user) => (
                    <li
                      key={user.id}
                      className="flex justify-between items-center"
                    >
                      <div>{user.email || "N/A"} -</div>
                      {`Website user : ${user.isWebsite ? "Yes" : "No"}`}
                    </li>
                  ))}
              </ul>
            </div>
          }
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Searches</h2>
          <div className="flex justify-between items-center mb-4">
            <div>
              <p className="text-2xl font-bold">{stats.totalSearches}</p>
              <p className="text-sm text-gray-500">Total Searches</p>
            </div>
            <div>
              <p className="text-2xl font-bold">
                {stats.recentSearchesLast24Hours}
              </p>
              <p className="text-sm text-gray-500">Last 24 Hours</p>
            </div>
          </div>
          <h3 className="font-semibold mb-2">10 Most Recent Searches</h3>
          <ul className="space-y-2">
            {stats.recentSearches.slice(0, 10).map((search, index) => (
              <li key={index} className="flex justify-between items-center">
                <span>{search.query}</span>
                <span className="text-sm text-gray-500">
                  {search.user?.email || "Anonymous"} -{" "}
                  {new Date(search.createdAt).toLocaleDateString()}
                </span>
              </li>
            ))}
          </ul>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">DarwinAI</h2>
          <div className="flex justify-between items-center mb-4">
            <div>
              <p className="text-2xl font-bold">{stats.totalTrendAnalyses}</p>
              <p className="text-sm text-gray-500">DarwinAI Searchs</p>
            </div>
            <div>
              <p className="text-2xl font-bold">
                {stats.recentTrendAnalysesLast24Hours}
              </p>
              <p className="text-sm text-gray-500">Last 24 Hours</p>
            </div>
          </div>
          <h3 className="font-semibold mb-2">
            10 Most Recent DarwinAI searches
          </h3>
          <ul className="space-y-2">
            {stats.recentTrendAnalyses.slice(0, 10).map((analysis, index) => (
              <li key={index} className="flex justify-between items-center">
                <span>{analysis.query}</span>
                <span className="text-sm text-gray-500">
                  {analysis.user.email} -{" "}
                  {new Date(analysis.createdAt).toLocaleDateString()}
                </span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">
          Top 5 Users with Most Trend Analyses
        </h2>
        <ul className="space-y-4">
          {stats.topTrendAnalysisUsers.slice(0, 5).map((user, index) => (
            <li key={index} className="border-b pb-2 last:border-b-0">
              <div
                className="flex justify-between items-center cursor-pointer"
                onClick={() => toggleUserExpansion(user.username)}
              >
                <h3 className="font-semibold">{user.username}</h3>
                <div className="flex items-center">
                  <span className="mr-2">
                    {user.trendAnalysisResponses.length} queries
                  </span>
                  {expandedUsers[user.username] ? (
                    <ChevronUp className="h-5 w-5" />
                  ) : (
                    <ChevronDown className="h-5 w-5" />
                  )}
                </div>
              </div>
              {expandedUsers[user.username] && (
                <ul className="ml-4 mt-2 space-y-1">
                  {user.trendAnalysisResponses.map(
                    (response, responseIndex) => (
                      <li
                        key={responseIndex}
                        className="flex justify-between items-center"
                      >
                        <span>{response.query}</span>
                        <span className="text-sm text-gray-500">
                          {new Date(response.createdAt).toLocaleDateString()}
                        </span>
                      </li>
                    ),
                  )}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}