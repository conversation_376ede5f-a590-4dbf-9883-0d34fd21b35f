"use client";

import Image, { StaticImageData } from "next/image";
import feature1 from "@/public/Feature1.png";
import feature2 from "@/public/Feature2.png";
import feature3 from "@/public/Feature3.png";
import feature4 from "@/public/Feature4.png";
interface Feature {
  title: string;
  description: string;
  image: StaticImageData;
}

const features: Feature[] = [
  {
    title: "Novelty Score",
    description:
      "Surface groundbreaking research using our proprietary novelty score, calculated using citation velocity, altmetrics, H10/i10 indexes, and more. Designed to spotlight papers with the highest potential impact.",
    image: feature1,
  },
  {
    title: "Identify Emerging Trends",
    description:
      "Pinpoint rising research areas across 300M+ papers. Detect momentum early, uncover novel topics, and stay ahead of what's coming next.",
    image: feature2,
  },
  {
    title: "Download Executive Reports",
    description:
      "Export curated insights, citations, and summaries into polished, presentation-ready reports, ideal for clients, stakeholders, or internal teams.",
    image: feature3,
  },
  {
    title: "Ask Complex Questions, Get Peer-Reviewed Answers",
    description:
      "Ask complex, domain-specific questions and get precise, peer-reviewed answers in seconds, grounded in real research, not surface-level web content.",
    image: feature4,
  },
];

export default function FeatureSection() {
  return (
    <section className="w-full px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-2xl lg:text-4xl font-medium text-center text-[#27394F] mb-16">
          Features built for speed, depth, and clarity
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {features.map((feature, index) => (
            <div key={index}>
              {/* Image with gradient background */}
              <div className="rounded-xl overflow-hidden">
                <Image
                  src={feature.image}
                  alt={feature.title}
                  width={600}
                  height={400}
                  className="w-full h-auto rounded-md"
                />
              </div>

              {/* Title & description */}
              <h3 className="mt-6 mb-2 lg:text-3xl  text-xl font-medium text-[#27394F]">
                {feature.title}
              </h3>
              <p className="lg:text-lg text-sm text-[#333]">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
