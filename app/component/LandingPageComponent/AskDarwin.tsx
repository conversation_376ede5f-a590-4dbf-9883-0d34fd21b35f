"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef } from "react";

interface Question {
  emoji: string;
  text: string;
}

interface Questions {
  row1: Question[];
  row2: Question[];
  row3: Question[];
}

interface QuestionRowProps {
  questions: Question[];
  centerOffset: number;
}

interface QuestionCardProps {
  emoji: string;
  text: string;
}

const questions: Questions = {
  row1: [
    {
      emoji: "🌌",
      text: "How do black holes form & behave?",
    },
    {
      emoji: "🦠",
      text: "Does gut microbiome affect depression?",
    },
    {
      emoji: "💊",
      text: "What are the benefits & risks of nootropics?",
    },
  ],
  row2: [
    {
      emoji: "😊",
      text: "What makes people happy?",
    },
    {
      emoji: "🖼️",
      text: "Compare supervised vs. unsupervised learning for image recognition",
    },
    {
      emoji: "🌳",
      text: "Can planting trees combat climate change?",
    },
  ],
  row3: [
    {
      emoji: "🚀",
      text: "What is the potential of space mining for Earth's resources?",
    },
    {
      emoji: "🤖",
      text: "What are the ethical implications of AI in warfare?",
    },
    {
      emoji: "🐝",
      text: "Why are bees essential for ecosystems?",
    },
  ],
};

const QuestionCard: React.FC<QuestionCardProps> = ({ emoji, text }) => (
  <Link
    href="/darwinai"
    className="bg-white border border-[#27394F] rounded-3xl p-3 lg:p-4 flex items-center justify-center hover:border-blue-500 transition-colors cursor-pointer min-w-max"
  >
    <span className="mr-2 text-xl">{emoji}</span>
    <p className="text-sm text-[#27394F] lg:text-lg whitespace-nowrap">
      {text}
    </p>
  </Link>
);

const QuestionRow: React.FC<QuestionRowProps> = ({
  questions,
  centerOffset,
}) => (
  <div
    className="flex gap-2 lg:gap-4"
    style={{ transform: `translateX(${centerOffset}px)` }}
  >
    {questions.map((question, index) => (
      <QuestionCard key={index} emoji={question.emoji} text={question.text} />
    ))}
  </div>
);

const AskDarwin: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [centerOffset, setCenterOffset] = React.useState<number>(0);
  const router = useRouter();

  return (
    <section className="w-full  bg-white px-4">
      <div className="mx-auto">
        <h2 className="text-2xl lg:text-4xl font-medium text-center mb-12 text-[#27394F]">
          Ask DarwinAI a question
        </h2>

        <div
          ref={containerRef}
          className="relative overflow-x-auto no-scrollbar"
        >
          <div ref={contentRef} className="flex flex-col gap-4 items-center">
            <QuestionRow
              questions={questions.row2}
              centerOffset={centerOffset}
            />
            <QuestionRow
              questions={questions.row1}
              centerOffset={centerOffset}
            />
            <QuestionRow
              questions={questions.row3}
              centerOffset={centerOffset}
            />
          </div>
        </div>
        <div className="flex w-full items-center justify-center mt-8">
          <div
            className="bg-[#88D84D] text-black rounded-3xl text-xl px-16 py-4 cursor-pointer hover:bg-[#7BC43D] transition-colors inline-flex items-center justify-center font-medium"
            onClick={() => router.push("/signin")}
          >
            GET STARTED
          </div>
        </div>
      </div>
    </section>
  );
};

export default AskDarwin;
