"use client";

import Image from "next/image";
import React from "react";

const roles = [
  {
    icon: "/Icon - Consulting & Strategy Teams.png",
    title: "Consulting & Strategy Teams",
    description:
      "Accelerate research workflows across domains, from policy to biotech. Save hours of manual review and surface trends before they hit the mainstream.",
  },
  {
    icon: "/Icon - R&D & Innovation.png",
    title: "R&D & Innovation Units",
    description:
      "Track fast-moving developments across 200M+ papers. Validate ideas, scout breakthroughs, and stay ahead in your domain.",
  },
  {
    icon: "/Icon - Research Analysts & Policy Think Tanks.png",
    title: "Research Analysts & Policy Think Tanks",
    description:
      "Get structured, evidence-backed answers to complex questions. Monitor shifts in public funding, research focus, and high-impact findings.",
  },
  {
    icon: "/Icon - Universities & Schools.png",
    title: "Universities & Schools",
    description:
      "Trusted by thousands of students, researchers and universities. We support curriculum innovation, literature reviews, and research discovery.",
  },
  {
    icon: "/Icon - Clinicians & Doctors.png",
    title: "Clinicians & Doctors",
    description:
      "Find peer-reviewed answers fast, with citations and summaries patients can understand. Stay current on clinical trials and treatments.",
  },
  {
    icon: "/Icon - Journalists & Writers.png",
    title: "Journalists & Writers",
    description:
      "Access reliable research across disciplines. Validate your stories with evidence-based insights and suggested follow-up leads.",
  },
];

export const WhoWeHelp = () => {
  return (
    <div className="w-full bg-[#0F172A] py-20 px-4 text-white">
      <div className="max-w-7xl mx-auto text-center">
        <h2 className="text-2xl lg:text-4xl font-medium mb-16">
          Who Outread Helps Most
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          {roles.map((role, index) => (
            <div key={index} className="flex flex-col items-center text-center">
              <div className="w-14 h-14 rounded-full bg-[#1E293B] flex items-center justify-center mb-4">
                <Image
                  src={role.icon}
                  alt={role.title}
                  width={28}
                  height={28}
                  className="object-contain w-14 h-14"
                />
              </div>
              <h3 className="lg:text-2xl text-lg font-medium mb-2">
                {role.title}
              </h3>
              <p className="text-sm text-[#E2E8F0]">{role.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
