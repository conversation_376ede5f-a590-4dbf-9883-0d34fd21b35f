"use client";

import React, { useRef, useState } from "react";
import {
  Rocket,
  Brain,
  Atom,
  CloudSun,
  Lightbulb,
  Heart,
  Laptop,
  FlaskConical,
  Leaf,
  TreePine,
  Briefcase,
  GraduationCap,
  BarChart,
  Microscope,
  Users,
  ScrollText,
  Target,
  CircleEllipsis,
  LucideIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";

interface Category {
  name: string;
  icon: LucideIcon;
}

interface CategoryRowProps {
  categories: Category[];
  centerOffset: number;
}

interface CategoryButtonProps {
  name: string;
  Icon: LucideIcon;
}

// ➤ Divide into 3 rows like your original setup
const categoryRows: Category[][] = [
  [
    { name: "Space", icon: Rocket },
    { name: "Artificial Intelligence", icon: Brain },
    { name: "Physics", icon: Atom },
    { name: "Climate Change", icon: CloudSun },
    { name: "Startups", icon: Lightbulb },
    { name: "Health", icon: Heart },
  ],
  [
    { name: "Technology", icon: Laptop },
    { name: "Chemical Science", icon: FlaskConical },
    { name: "Environment", icon: Leaf },
    { name: "Psychology", icon: TreePine },
    { name: "Business", icon: Briefcase },
    { name: "Humanities", icon: GraduationCap },
  ],
  [
    { name: "Economics", icon: BarChart },
    { name: "Life Sciences", icon: Microscope },
    { name: "Social Science", icon: Users },
    { name: "Philosophy", icon: ScrollText },
    { name: "Sports", icon: Target },
    { name: "Other", icon: CircleEllipsis },
  ],
];

const CategoryButton: React.FC<CategoryButtonProps> = ({ name, Icon }) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/search?q=${encodeURIComponent(name)}`);
  };

  return (
    <div
      className="flex items-center justify-center px-4 py-2 lg:px-6 lg:py-3 rounded-full border text-sm lg:text-base font-medium border-[#27394F] bg-transparent hover:border-[#27394F] transition-colors min-w-max cursor-pointer backdrop-blur-md"
      onClick={handleClick}
    >
      <Icon
        className="w-4 h-4 lg:w-5 lg:h-5 mr-2 text-[#3B4A3F]"
        strokeWidth={1.5}
      />
      <span className="text-[#27394F]">{name}</span>
    </div>
  );
};

const CategoryRow: React.FC<CategoryRowProps> = ({
  categories,
  centerOffset,
}) => (
  <div
    className="flex gap-2 lg:gap-4"
    style={{ transform: `translateX(${centerOffset}px)` }}
  >
    {categories.map((category, index) => (
      <CategoryButton key={index} name={category.name} Icon={category.icon} />
    ))}
  </div>
);

const CategorySection: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [centerOffset, setCenterOffset] = useState<number>(0); // Set to 0 for now

  return (
    <section className="w-full-16 lg:h-[600px] h-[400px] flex flex-col justify-center py-4 px-4 bg-[linear-gradient(to_bottom,_white_0%,_rgba(136,216,77,0.575)_25%,_rgba(136,216,77,0.4)_35%,_white_100%)]">
      <div className="lg:max-w-7xl w-full  mx-auto">
        <h2 className="text-2xl lg:text-4xl font-medium text-center mb-12 text-[#27394F]">
          Search Keywords on CurieAI
        </h2>

        {/* Shared scrollable container */}
        <div
          ref={containerRef}
          className="relative overflow-x-auto no-scrollbar"
        >
          <div
            ref={contentRef}
            className="flex  flex-col gap-4 lg:items-center items-start min-w-max"
          >
            {categoryRows.map((row, i) => (
              <CategoryRow
                key={i}
                categories={row}
                centerOffset={centerOffset}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CategorySection;
