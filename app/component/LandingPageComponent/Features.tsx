"use client";

import React from "react";
import Image from "next/image";
import stayOnTheCuttingEdge1 from "@/public/StayOnTheCuttingEdge1.png";
import stayOnTheCuttingEdge2 from "@/public/StayOnTheCuttingEdge2.png";
import stayOnTheCuttingEdge3 from "@/public/StayOnTheCuttingEdge3.png";

import type { StaticImageData } from "next/image";

const ContentItem = ({
  imageSrc,
  imageAlt,
  title,
  description,
}: {
  imageSrc: StaticImageData;
  imageAlt: string;
  title: string;
  description: string;
}) => {
  // white to gray gradient linear
  return (
    <div className="flex flex-col items-center text-center rounded-xl shadow-lg lg:px-6 px-4 min-h-72 py-8 w-full max-w-sm my-4 bg-[linear-gradient(to_bottom,#FFFFFF_0%,#FBFEFA_100%)]">
      {/* Icon in a circular background */}
      <div className="w-full items-start justify-start text-start">
        <div className="w-16 h-16 flex items-start justify-start rounded-full bg-[#E6F4EA] mb-4">
          <Image src={imageSrc} alt={imageAlt} className="w-16 h-16" />
        </div>

        {/* Title */}
        <div className="flex h-16  ">
          <h3 className="font-medium text-lg text-[#1D1E2C] mb-2  ">{title}</h3>
        </div>

        {/* Description */}
        <p className="text-sm text-[#333] ">{description}</p>
      </div>
    </div>
  );
};

export default function App() {
  const contents = [
    {
      imageSrc: stayOnTheCuttingEdge1,
      imageAlt: "Papers",
      title: "Clarity from over 300 million research papers",
      description:
        "We surface the most novel, cited, and relevant insights from a massive dataset—so you never waste time searching or second-guessing sources.",
    },
    {
      imageSrc: stayOnTheCuttingEdge2,
      imageAlt: "Idea",
      title: "Expert-level answers, without expert-level effort",
      description:
        "Our system turns complex science into clear, contextual insights tailored to your domain—no more relying on expensive analysts or guesswork.",
    },
    {
      imageSrc: stayOnTheCuttingEdge3,
      imageAlt: "Path",
      title: "A faster path to what’s next",
      description:
        "We help strategy, consulting, and R&D teams stay ahead of emerging trends and breakthroughs before they hit the mainstream.",
    },
  ];

  return (
    <div className="flex flex-col items-center justify-center w-full bg-white ">
      <h2 className="text-2xl lg:text-4xl font-medium text-center mb-10 text-[#27394F]">
        Stay on the cutting-edge
      </h2>

      <div className="flex flex-col md:flex-row items-start justify-start h-full max-w-6xl gap-x-2 w-full">
        {contents.map((content, index) => (
          <ContentItem
            key={index}
            imageSrc={content.imageSrc}
            imageAlt={content.imageAlt}
            title={content.title}
            description={content.description}
          />
        ))}
      </div>
    </div>
  );
}
