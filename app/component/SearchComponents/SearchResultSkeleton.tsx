import React from "react";

const SearchResultSkeleton: React.FC = () => {
  return (
    <div className="flex bg-[#27394F] rounded-lg overflow-hidden shadow-lg w-full h-auto min-h-[350px] animate-pulse">
      <div className="w-1/3 p-4 flex items-center justify-center">
        <div className="w-full h-full bg-gray-300 rounded-lg"></div>
      </div>
      <div className="p-6 w-2/3 relative flex flex-col justify-between">
        <div>
          <div className="h-8 bg-gray-300 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-full mb-4"></div>
          <div className="flex flex-wrap mb-4">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="h-6 w-20 bg-gray-300 rounded-full mr-2 mb-2"
              ></div>
            ))}
          </div>
          <div className="flex items-center mb-4">
            <div className="h-4 w-24 bg-gray-300 rounded mr-4"></div>
            <div className="h-4 w-32 bg-gray-300 rounded"></div>
          </div>
          <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
        </div>
        <div className="absolute top-4 right-4">
          <div className="h-8 w-8 bg-gray-300 rounded-full"></div>
        </div>
      </div>
    </div>
  );
};

export default SearchResultSkeleton;
