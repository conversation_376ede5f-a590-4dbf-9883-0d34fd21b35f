import NavigationBar from "./NavbarComponents/NavigationBar";
import AlternativeNavbar from "./NavbarComponents/AlternativeNavbar";
import { getUser } from "@/lib/cache/getUser";
import { headers } from "next/headers";
import ConditionalNavbar from "./ConditionalNavbar";

export default async function NavbarWrapper() {
  const user = await getUser();

  // Get the current pathname from headers
  const headersList = headers();
  const pathname = headersList.get("x-pathname") || "";

  // Hide navbar on chatbot page (server-side check)
  if (pathname === "/chatbot") {
    return null;
  }

  const navbarContent = user ? (
    <div className="bg-[#132435] text-white">
      <AlternativeNavbar isPaidUser={user.role === "PAID_USER"} />
    </div>
  ) : (
    <NavigationBar />
  );

  // Wrap with client-side conditional rendering as fallback
  return <ConditionalNavbar>{navbarContent}</ConditionalNavbar>;
}
