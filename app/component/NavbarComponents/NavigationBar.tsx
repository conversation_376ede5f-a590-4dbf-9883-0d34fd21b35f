"use client";

import React, { useState } from "react";
import { Image } from "@nextui-org/react";
import { useClientMediaQuery } from "../../../hooks/useClientMediaQuery";
import { useAuthStore } from "../../../lib/zustand/zustand";

export default function App() {
  const isMobile = useClientMediaQuery("(max-width: 768px)");
  const size = isMobile ? "md" : "lg";
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isLoading, user } = useAuthStore((state) => state);
  const isAuthenticated = user !== null && !isLoading;

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const NavButtons = ({
    textColour = "white",
  }: {
    textColour: "white" | "black"; // limit to known values
  }) => {
    const buttonSize = isMobile ? "px-3 py-2 text-sm" : "px-4 py-2 text-base";

    return (
      <>
        <a href="/">
          <div
            className={`text-[#88D84D] border-none rounded-md mx-1 cursor-pointer hover:bg-gray-700 transition-colors inline-flex items-center justify-center font-medium ${buttonSize}`}
          >
            Home
          </div>
        </a>

        <a href="/contact-us">
          <div
            className={`${
              textColour === "black"
                ? "text-black hover:bg-gray-200"
                : "text-white hover:bg-gray-700"
            } border-none rounded-md mx-1 cursor-pointer transition-colors inline-flex items-center justify-center font-medium ${buttonSize}`}
          >
            Contact us
          </div>
        </a>

        <a href="/newsletter">
          <div
            className={`${
              textColour === "black"
                ? "text-black hover:bg-gray-200"
                : "text-white hover:bg-gray-700"
            } border-none rounded-md mx-1 cursor-pointer transition-colors inline-flex items-center justify-center font-medium ${buttonSize}`}
          >
            Newsletter
          </div>
        </a>
      </>
    );
  };

  if (!isAuthenticated) {
    return (
      <>
        <div className="flex items-center w-full shadow-md h-[96px] bg-[#132435] relative p-4 z-20">
          <a className="" href="/">
            {isMobile ? (
              <Image
                src="/favi.png"
                className="mr-2 flex items-center justify-center"
                height={40}
                alt="outread logo"
              />
            ) : (
              <Image
                src="/white_logo.png"
                className="w-[150px] h-auto object-contain"
                width={150}
                height={60}
                alt="outread logo"
              />
            )}
          </a>
          <div className="mx-auto"></div>
          {isMobile ? (
            <div className="flex items-center">
              <div
                onClick={toggleMobileMenu}
                className="p-2 cursor-pointer hover:bg-gray-700 rounded transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16m-7 6h7"
                  />
                </svg>
              </div>
              {isMobileMenuOpen && (
                <div className="fixed top-[75px] right-0  text-[white] bg-[#132435] shadow-md p-4 flex flex-col items-center z-">
                  {" "}
                  <NavButtons textColour={"white"} />
                </div>
              )}
            </div>
          ) : (
            <div className="w-full flex items-end justify-end">
              <NavButtons textColour={"white"} />
              <div
                onClick={() => (window.location.href = "/signin")}
                className="bg-[#88D84D] text-white rounded-3xl text-[16px] px-6 py-2 h-12 cursor-pointer hover:bg-[#7BC43D] transition-colors inline-flex items-center justify-center font-medium"
              >
                GET STARTED
              </div>
            </div>
          )}
        </div>
      </>
    );
  }
}
