"use client";

import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

interface ConditionalNavbarProps {
  children: React.ReactNode;
}

export default function ConditionalNavbar({ children }: ConditionalNavbarProps) {
  const pathname = usePathname();
  const [shouldShow, setShouldShow] = useState(true);

  useEffect(() => {
    // Hide navbar on chatbot page
    setShouldShow(pathname !== "/chatbot");
  }, [pathname]);

  // Don't render anything on chatbot page
  if (!shouldShow) {
    return null;
  }

  return <>{children}</>;
}
