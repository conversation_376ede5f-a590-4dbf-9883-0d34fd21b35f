"use client";
import React, { useEffect, useState } from "react";
import { useClientMediaQuery } from "@/hooks/useClientMediaQuery";

interface AltmetricsBadgeProps {
  doi: string;
}

const AltmetricsBadge: React.FC<AltmetricsBadgeProps> = ({ doi }) => {
  const [altmetricData, setAltmetricData] = useState<any>(null);

  const isMobile = useClientMediaQuery("(max-width: 768px)");

  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://d1bxh8uas1mnw7.cloudfront.net/assets/embed.js";
    script.async = true;
    document.body.appendChild(script);

    const fetchAltmetricData = async () => {
      try {
        const response = await fetch(`https://api.altmetric.com/v1/doi/${doi}`);
        const data = await response.json();
        setAltmetricData(data);
      } catch (error) {
        console.error("Error fetching Altmetric data:", error);
      }
    };

    fetchAltmetricData();

    return () => {
      document.body.removeChild(script);
    };
  }, [doi]);

  return (
    <div className="w-full flex justify-center items-center flex-col ">
      {altmetricData && (
        <h2 className="w-full text-left text-lg lg:text-2xl  font-semibold  mb-3 first:lg:mb-4">
          Altmetric Badge
        </h2>
      )}
      <div
        className="altmetrics-container p-2 justify-center w-[200px] lg:w-[400px]"
        style={{
          display: "flex ",
          alignItems: "center",
          marginTop: "20px",
        }}
      >
        <div
          data-badge-popover="right"
          data-badge-type={`${isMobile ? "medium" : "large"}-donut`}
          data-doi={doi}
          data-hide-no-mentions="true"
          data-badge-details="right"
          className="altmetric-embed"
          style={{ marginRight: "15px" }}
        ></div>
        <div
          className="altmetric-details"
          style={{ flexGrow: 1, maxWidth: "70%" }}
        ></div>
        <div className="flex items-center space-x-4">
          <div className="altmetric-badge" data-doi={doi}></div>
          {altmetricData && (
            <div className="text-sm">
              <p className="font-medium">Score: {altmetricData.score}</p>
              <p>Mentioned by:</p>
              <ul className="list-disc ml-4">
                {altmetricData.cited_by_posts_count > 0 && (
                  <li>Blog Posts: {altmetricData.cited_by_posts_count}</li>
                )}
                {altmetricData.cited_by_tweeters_count > 0 && (
                  <li>
                    Twitter: {altmetricData.cited_by_tweeters_count} users
                  </li>
                )}
                {altmetricData.cited_by_accounts_count > 0 && (
                  <li>
                    Total Mentions: {altmetricData.cited_by_accounts_count}
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AltmetricsBadge;
