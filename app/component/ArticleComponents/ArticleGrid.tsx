"use client";
import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { BookmarkButton } from "@/app/catalogue/components/BookmarkButton";
import { Headphones } from "lucide-react";

interface Article {
  id: string;
  title: string;
  slug: string;
  imageUrl: string;
  imageAlt: string;
  audioId: string | null;
  category: string;
  estimatedReadingTime: number;
  initialIsBookmarked: boolean;
}

interface ArticleGridProps {
  articles: Article[];
  columns: number;
}

const ArticleGrid: React.FC<ArticleGridProps> = ({ articles, columns }) => {
  const [validArticles, setValidArticles] = useState<Article[]>(articles);

  const handleImageError = (articleId: string) => {
    setValidArticles((prevArticles) =>
      prevArticles.filter((article) => article.id !== articleId),
    );
  };

  return (
    <div
      className={`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-${columns} gap-8`}
    >
      {validArticles.map((article) => (
        <div key={article.id} className="overflow-hidden flex flex-col">
          <Link
            className="w-[150px] lg:w-[178px] flex flex-col"
            href={`/article/${article.slug}`}
          >
            <div className="h-220 lg:h-[237px] relative flex items-center justify-start overflow-hidden mb-2">
              <Image
                src={article.imageUrl}
                alt={article.imageAlt}
                width={178}
                height={237}
                style={{
                  width: "100%",
                  height: "auto",
                }}
                className="w-full rounded-2xl"
                onError={() => handleImageError(article.id)}
              />
            </div>
            <div className="flex flex-col justify-between items-start w-full">
              <div className="flex flex-row justify-start items-start">
                <h2 className="text-md w-[90%] mb-2 line-clamp-4">
                  {article.title}
                </h2>
                <BookmarkButton
                  articleId={article.id}
                  initialIsBookmarked={article.initialIsBookmarked}
                />
              </div>
              <div className="flex justify-start items-center">
                <div className="text-[10px] w-auto text-center mr-2 bg-gray-500 p-1 text-white">
                  {article.estimatedReadingTime} min{" "}
                </div>
                <p className="text-[10px] text-center bg-gray-500 text-white p-1 w-auto">
                  {article.category}
                </p>
                {article.audioId && (
                  <div className="text-[10px] w-auto text-center mx-2 bg-gray-500 text-white p-[4px]">
                    <Headphones size={16} />
                  </div>
                )}
              </div>
            </div>
          </Link>
        </div>
      ))}
    </div>
  );
};

export default ArticleGrid;
