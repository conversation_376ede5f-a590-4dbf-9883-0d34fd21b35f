import React, { useState, useEffect, useRef } from "react";
import { Play, Pause, SkipBack, SkipForward, X } from "lucide-react";

interface AudioPlayerProps {
  audioId: string;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ audioId }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.addEventListener("timeupdate", updateProgress);
      audioRef.current.addEventListener("loadedmetadata", () => {
        setDuration(audioRef.current!.duration);
      });
    }
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("timeupdate", updateProgress);
      }
    };
  }, []);

  const updateProgress = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const togglePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTime = parseFloat(e.target.value);
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  return (
    <div
      className={`fixed bottom-0 left-0 right-0 bg-white text-black p-4 shadow-lg z-20 ${
        isMobile ? "h-24" : ""
      }`}
      onClick={isMobile ? togglePlayPause : undefined}
    >
      <audio ref={audioRef} src={audioId} />
      <div
        className={`flex items-center ${
          isMobile ? "justify-between" : "justify-between flex-wrap"
        }`}
      >
        <div className="flex items-center">
          {!isMobile && (
            <button
              onClick={togglePlayPause}
              className="bg-[#88D84D] text-white rounded-full p-2 mr-4"
            >
              {isPlaying ? <Pause size={24} /> : <Play size={24} />}
            </button>
          )}
          <div>
            <p className="font-semibold">Audio Version</p>
            <p className="text-sm text-gray-600">
              {formatTime(currentTime)} / {formatTime(duration)}
            </p>
          </div>
        </div>
        {isMobile && (
          <div className="text-[#88D84D]">
            {isPlaying ? <Pause size={24} /> : <Play size={24} />}
          </div>
        )}
        {!isMobile && (
          <div className="flex items-center">
            <button
              onClick={() => {
                if (audioRef.current) {
                  audioRef.current.currentTime -= 10;
                }
              }}
              className="text-gray-600 mr-4"
            >
              <SkipBack size={20} />
            </button>
            <button
              onClick={() => {
                if (audioRef.current) {
                  audioRef.current.currentTime += 10;
                }
              }}
              className="text-gray-600 mr-4"
            >
              <SkipForward size={20} />
            </button>
            <select
              onChange={(e) => {
                if (audioRef.current) {
                  audioRef.current.playbackRate = parseFloat(e.target.value);
                }
              }}
              className="bg-gray-200 text-gray-800 p-1 rounded"
            >
              <option value="0.5">0.5x</option>
              <option value="1" selected>
                1x
              </option>
              <option value="1.5">1.5x</option>
              <option value="2">2x</option>
            </select>
          </div>
        )}
      </div>
      <input
        type="range"
        min="0"
        max={duration}
        value={currentTime}
        onChange={handleSeek}
        className={`w-full ${isMobile ? "mt-2" : "mt-4"}`}
      />
    </div>
  );
};

export default AudioPlayer;
