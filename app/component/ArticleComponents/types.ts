export interface Summary {
  heading: string;
  content: string;
}

export interface Article {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  coverImage: string;
  originalPaperTitle: string;
  authorName: string;
  audioId: string | null;
  doi: string;
  oneCardSummary: any;
  simpleSummary: Summary[];
  publishDate: Date | null;
  defaultSummary: Summary[];
  estimatedReadingTime: number;
  categories: { name: string }[];
}
