"use client";

import { useState } from "react";

interface RegenerateImageButtonProps {
  articleId: string;
  doi: string;
  title: string;
  subtitle: string;
}

export default function RegenerateImageButton({
  articleId,
  doi,
  title,
  subtitle,
}: RegenerateImageButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleRegenerateImage = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/regenerateImage", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          doi,
          title,
          subtitle,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to regenerate image");
      }

      const result = await response.json();
      if (result.success) {
        alert("Image regeneration request sent successfully");
      } else {
        throw new Error(result.error || "Failed to regenerate image");
      }
    } catch (error) {
      console.error("Error regenerating image:", error);
      alert("Failed to regenerate image. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleRegenerateImage}
      disabled={isLoading}
      className="mt-4 px-4 py-2 bg-blue-500 w-[300px] h-[50px] text-white rounded hover:bg-blue-600 disabled:bg-blue-300"
    >
      {isLoading ? "Regenerating..." : "Regenerate Article Image"}
    </button>
  );
}
