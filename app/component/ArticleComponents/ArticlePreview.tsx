import React from "react";
import Image from "next/image";
import { Button } from "@nextui-org/react";
import { Divider } from "@nextui-org/divider";
import Link from "next/link";
import { Article } from "./types";
import RegenerateImageButton from "./RegenerateImageButton";
import { trackArticleView } from "@/app/actions/trackArticleView";
import { isFloat32Array } from "util/types";

interface ArticlePreviewProps {
  article: Article;
  isPaid: boolean;
  isAdmin: boolean;
  onReadNow: () => void;
}

const ArticlePreview: React.FC<ArticlePreviewProps> = ({
  article,
  isPaid,
  isAdmin,
  onReadNow,
}) => {
  const isFeatured = article.categories.some(
    (catergory) => catergory.name == "Featured",
  );

  const renderActionButtons = () => {
    if (isAdmin) {
      return (
        <>
          {article.audioId ? (
            <Button
              onPress={() => {
                trackArticleView(article.id);
                onReadNow();
              }}
              className="w-[220px] md:w-[300px] h-[50px] rounded-lg text-lg bg-[#88D84D] text-[#132435]"
            >
              Read or Listen now
            </Button>
          ) : (
            <Button
              onPress={() => {
                trackArticleView(article.id);
                onReadNow();
              }}
              className="w-[220px] md:w-[300px] h-[50px] rounded-lg text-lg bg-[#88D84D] text-[#132435]"
            >
              Read Now
            </Button>
          )}

          <div className="mt-4">
            <RegenerateImageButton
              doi={article.doi}
              articleId={article.id}
              title={article.title}
              subtitle={article.subtitle}
            />
          </div>
        </>
      );
    }

    console.log({ isFeatured });

    if (isPaid || isFeatured) {
      return (
        <div>
          {article.audioId ? (
            <Button
              onPress={() => {
                trackArticleView(article.id);
                onReadNow();
              }}
              className="w-[220px] md:w-[300px] h-[50px] rounded-lg text-lg bg-[#88D84D] text-[#132435]"
            >
              Read or Listen now
            </Button>
          ) : (
            <Button
              onPress={() => {
                trackArticleView(article.id);
                onReadNow();
              }}
              className="w-[220px] md:w-[300px] h-[50px] rounded-lg text-lg bg-[#88D84D] text-[#132435]"
            >
              Read Now
            </Button>
          )}
        </div>
      );
    }

    return (
      <Link href={`/subscription`} passHref>
        <Button className="w-[220px] md:w-[300px] h-[50px] rounded-lg text-lg bg-[#88D84D] text-[#132435]">
          Start Trial
        </Button>
      </Link>
    );
  };

  return (
    <div className="w-full max-w-[393px] md:max-w-[756px] lg:max-w-[1040px] mx-auto p-5 text-white">
      <div className="flex flex-col md:flex-row w-full items-start justify-between gap-8">
        <div className="w-full md:w-2/3 flex flex-col min-h-[500px] order-2 md:order-1">
          <div className="flex flex-col max-w-[700px] mb-2">
            <h1 className="text-xl lg:text-3xl font-semibold mb-2">
              {article.title}
            </h1>
            <h2 className="text-sm md:text-lg font-thin mb-2">
              {article.subtitle}
            </h2>
            <Divider className="bg-gray-400 my-2" />

            <div className="flex justify-between text-[12px] md:text-base">
              <p>Reading Time: {article.estimatedReadingTime} minutes</p>
              <div className="flex flex-col text-right w-full">
                <p>Author: {article.authorName}</p>
                <p className="text-right">Doi: {article.doi}</p>
                <p>
                  Publish Date:{" "}
                  {article.publishDate
                    ? article.publishDate.toLocaleDateString()
                    : "N/A"}
                </p>
              </div>
            </div>
            <Divider className="bg-gray-400 my-2" />
          </div>

          <div className="flex flex-wrap">
            {article.categories.map(
              (category: { name: string }, key: number) => (
                <div key={key} className="flex items-center">
                  <button
                    onClick={() =>
                      (window.location.href = `/category/${category.name}`)
                    }
                    className="bg-gray-500 p-2 text-white mr-2 mb-2"
                  >
                    {category.name}
                  </button>
                </div>
              ),
            )}
          </div>

          <div className="w-full mt-2 flex justify-center flex-col">
            {article.oneCardSummary.heading ? (
              <div>
                <p className="leading-loose">
                  {article.oneCardSummary.content
                    .split("•")
                    .map((line: any, index: any) => (
                      <div key={index}>{line + "\n"}</div>
                    ))}
                </p>
              </div>
            ) : article.oneCardSummary.content ? (
              <p className="leading-loose">{article.oneCardSummary.content}</p>
            ) : (
              <p className="leading-loose">{article.oneCardSummary}</p>
            )}
          </div>
        </div>

        <div className="w-full md:w-1/3 flex flex-col items-center order-1 md:order-2">
          <Image
            src={article.coverImage}
            alt={article.title}
            width={220}
            height={220}
            className="mb-4 w-[220px] md:w-full"
          />
          {renderActionButtons()}
        </div>
      </div>
    </div>
  );
};

export default ArticlePreview;
