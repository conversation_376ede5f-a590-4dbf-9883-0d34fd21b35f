"use client";
import React, { useState, useEffect } from "react";
import { Volume2 } from "lucide-react";
import { trackArticleView } from "@/app/actions/trackArticleView";
import ArticlePreview from "./ArticlePreview";
import SummaryView from "./SummaryView";
import AudioPlayer from "./AudioPlayer";
import { Article } from "./types";
import { useClientMediaQuery } from "@/hooks/useClientMediaQuery";

interface ArticleDisplayProps {
  article: Article;
  isPaid: boolean;
  isAdmin: boolean;
}

export default function ArticleDisplay({
  isPaid,
  isAdmin,
  article,
}: ArticleDisplayProps) {
  const [showPreview, setShowPreview] = useState(true);
  const [summaryType, setSummaryType] = useState<"simple" | "technical">(
    "simple",
  );
  const [showAudioPlayer, setShowAudioPlayer] = useState(false);

  const isMobile = useClientMediaQuery("(max-width: 640px)");

  console.log("ArticleDisplay", article);

  const handleReadNow = () => {
    trackArticleView(article.id);
    setShowPreview(false);
  };
  const toggleAudioPlayer = () => {
    setShowAudioPlayer(!showAudioPlayer);
  };

  return (
    <div className="max-w-full w-full min-h-screen mx-auto p-5 flex flex-col justify-start items-center bg-[#132435] relative">
      {article.audioId && !showPreview && !isMobile && (
        <button
          onClick={toggleAudioPlayer}
          className="fixed opacity-0 sm:opacity-100 lg:left-16 top-1/2 transform -translate-y-1/2 bg-[#88D84D] text-white rounded-full p-3 z-10"
        >
          <Volume2 size={24} />
        </button>
      )}
      <div className="lg:mt-12 mb-20 w-full">
        {showPreview ? (
          <ArticlePreview
            article={article}
            isPaid={isPaid}
            isAdmin={isAdmin}
            onReadNow={handleReadNow}
          />
        ) : (
          <SummaryView
            article={article}
            summaryType={summaryType}
            onSummaryTypeChange={setSummaryType}
            onBackToPreview={() => setShowPreview(true)}
          />
        )}
      </div>
      {isMobile && !showPreview && article.audioId ? (
        <AudioPlayer audioId={article.audioId} />
      ) : null}

      {showAudioPlayer && article.audioId && !showPreview && (
        <AudioPlayer audioId={article.audioId} />
      )}
    </div>
  );
}
