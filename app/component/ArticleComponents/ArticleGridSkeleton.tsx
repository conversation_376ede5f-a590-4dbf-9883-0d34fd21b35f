import React from "react";

interface ArticleGridSkeletonProps {
  columns: number;
  rows?: number;
}

const ArticleGridSkeleton: React.FC<ArticleGridSkeletonProps> = ({
  columns,
  rows = 2,
}) => {
  return (
    <div
      className={`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-${columns} gap-6`}
    >
      {[...Array(columns * 1)].map((_, index) => (
        <div key={index} className="overflow-hidden flex flex-col">
          <div className="w-[150px] lg:w-[178px] flex flex-col">
            <div className="h-[220px] lg:h-[227px] relative flex items-center justify-start overflow-hidden mb-2">
              <div className="w-full h-full bg-gray-700 animate-pulse rounded-lg"></div>
            </div>
            <div className="flex flex-col justify-between items-start w-full">
              <div className="w-full flex flex-row justify-start items-start">
                <div className="text-md w-[90%] mb-2 h-8 bg-gray-700 animate-pulse rounded"></div>
                <div className="w-6 h-6 bg-gray-700 animate-pulse rounded-full"></div>
              </div>
              <div className="flex justify-start items-center">
                <div className="text-[10px] w-auto text-center mr-2 bg-gray-500 p-1 animate-pulse rounded">
                  <div className="w-8 h-3 bg-gray-700"></div>
                </div>
                <div className="text-[10px] text-center bg-gray-500 p-1 w-auto animate-pulse rounded">
                  <div className="w-12 h-3 bg-gray-700"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ArticleGridSkeleton;
