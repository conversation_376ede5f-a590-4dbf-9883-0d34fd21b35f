import React from "react";
import { But<PERSON> } from "@nextui-org/react";
import AltmetricsBadge from "./AltmetricsBadge";
import { Article, Summary } from "./types";
import SummaryToggle from "./SummaryToggle";

interface SummaryViewProps {
  article: Article;
  summaryType: "simple" | "technical";
  onSummaryTypeChange: (type: "simple" | "technical") => void;
  onBackToPreview: () => void;
}

const SummaryView: React.FC<SummaryViewProps> = ({
  article,
  summaryType,
  onSummaryTypeChange,
  onBackToPreview,
}) => {
  return (
    <div className="w-full md:w-[756px] lg:w-[1040px] mx-auto text-white relative">
      <div className="flex items-center justify-between mb-4">
        <Button
          onPress={onBackToPreview}
          className="bg-[#88D84D] text-[#132435]"
        >
          Back to Preview
        </Button>
        <SummaryToggle
          summaryType={summaryType}
          onSummaryTypeChange={onSummaryTypeChange}
        />
      </div>
      <h1 className="text-lg lg:text-3xl font-semibold my-8">
        {article.title}
      </h1>
      <div className="bg-white p-4 w-[354px] lg:w-[1040px] text-black rounded-xl">
        {(summaryType === "simple"
          ? article.simpleSummary
          : article.defaultSummary
        ).map((summary: Summary, index: number) => (
          <div key={index} className="">
            <h2 className="text-lg lg:text-2xl font-semibold mb-3 first:lg:mb-4">
              {summary.heading}
            </h2>
            <p className="text-sm lg:text-[18px] leading-loose">
              {summary.content}
            </p>
            <br />
            <br />
          </div>
        ))}
        <div className="py-4 px-2 flex w-full flex-col justify-start items-start">
          <div className="w-full flex justify-center items-center">
            <AltmetricsBadge doi={article.doi} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SummaryView;
