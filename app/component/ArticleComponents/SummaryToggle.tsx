import React from "react";

interface SummaryToggleProps {
  summaryType: "simple" | "technical";
  onSummaryTypeChange: (type: "simple" | "technical") => void;
}

const SummaryToggle: React.FC<SummaryToggleProps> = ({
  summaryType,
  onSummaryTypeChange,
}) => {
  return (
    <div className="flex items-center">
      <span className="mr-2">Simple</span>
      <label className="switch">
        <input
          type="checkbox"
          checked={summaryType === "technical"}
          onChange={() =>
            onSummaryTypeChange(
              summaryType === "simple" ? "technical" : "simple",
            )
          }
        />
        <span
          className={`slider ${
            summaryType === "simple" ? "bg-gray-500" : "!bg-[#88D84D]"
          } round`}
        />
      </label>
      <span className="ml-2">Technical</span>
    </div>
  );
};

export default SummaryToggle;
