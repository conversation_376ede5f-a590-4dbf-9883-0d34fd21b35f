const FB_ACCESS_TOKEN =
  "EAANxhpLlZBd4BOzaJZABHAo8SeNrEMtmtlH5A20CkYGwuAjT65lypwwTVOZAZCvzZBdRZA88rKdNyvaIdGLnOggoXA5NZCmwHTYiErcMiJSgK8BTEHYSdw1YtSG1Yrt7hrX09ASbg9rLTWxNd4gZC0YSLsfk7xzLZCuFyi3BeGUU8ZCcvQCPlb257TLPMsyIlpLhytHwZDZD";
const FB_PIXEL_ID = "1788816715206755"; // You need to add your Facebook Pixel ID here

const sendFBEvent = (eventName: string, eventData: any) => {
  const url = `https://graph.facebook.com/v13.0/${FB_PIXEL_ID}/events?access_token=${FB_ACCESS_TOKEN}`;
  console.log("Sending Facebook event:", eventName, eventData);

  const data = {
    data: [
      {
        event_name: eventName,
        event_time: Math.floor(Date.now() / 1000),
        action_source: "website",
        event_source_url: window.location.href,
        user_data: {
          client_user_agent: navigator.userAgent,
          // Add any other user data here
        },
        custom_data: eventData,
      },
    ],
  };

  fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
    .then((response) => response.json())
    .then((data) => console.log("Facebook event sent successfuly"))
    .catch((error) => console.error("Error sending Facebook event:", error));
};

export const handleViewContent = (pageLink: string) => {
  sendFBEvent("ViewContent", { pageLink: pageLink });
};

// Search event
export const handleSearch = (searchQuery: string) => {
  sendFBEvent("Search", { searchQuery });
};

// Purchase event
export const handlePurchase = (value: number, currency: string) => {
  sendFBEvent("Purchase", { value, currency });
};

// InitiateCheckout event
export const handleInitiateCheckout = (plan: any) => {
  sendFBEvent("InitiateCheckout", { plan: plan });
};
