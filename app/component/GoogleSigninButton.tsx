import { createClient } from "@/lib/supabase/client";

export const handleGoogleSignIn = async () => {
  const supabase = createClient();
  const redirectUrl = `${window.location.origin}/auth/callback`;
  console.log(redirectUrl);
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "google",
    options: {
      redirectTo: redirectUrl, // This will automatically use the correct protocol
      scopes: "email profile",
      queryParams: {
        prompt: "select_account",
      },
    },
  });

  if (error) {
    console.error("Sign in error:", error);
    return;
  }
};
