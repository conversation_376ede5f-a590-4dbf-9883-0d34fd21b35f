"use client";

import React, { useEffect, useState } from "react";
import { useClientMediaQuery } from "@/hooks/useClientMediaQuery";
import { Divider } from "@nextui-org/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faDiscord,
  faInstagram,
  faLinkedin,
} from "@fortawesome/free-brands-svg-icons";

import { usePathname } from "next/navigation";
import LoggedInFooter from "./LoggedInFooter";

function DesktopComponent() {
  return (
    <div className="w-full h-[300px] flex flex-col items-center text-white justify-center bg-[#132435]">
      <Divider className="bg-gray-700 mb-12"></Divider>
      <div className="my-4 flex flex-row items-center justify-center w-full h-auto text-sm">
        <a className="mx-2" href="/newsletter">
          Newsletter
        </a>
        <a className="mx-2" href="/privacy">
          Privacy Policy
        </a>
        <a className="mx-2" href="/tos">
          Terms of Service
        </a>
        <a className="mx-2" href="/blog">
          Blog
        </a>
        <a className="mx-2" href="/presskit">
          Press Kit
        </a>
      </div>
      <div className="flex flex-row gap-2">
        <div>
          <a href="https://www.instagram.com/outread.official/">
            <FontAwesomeIcon icon={faInstagram} size="lg"></FontAwesomeIcon>
          </a>
        </div>
        <div className="">
          <a href="https://www.linkedin.com/company/outread/">
            <FontAwesomeIcon icon={faLinkedin} size="lg"></FontAwesomeIcon>
          </a>
        </div>
        <div>
          <div>
            <a href="https://discord.gg/Sd8tMr6g5v">
              <FontAwesomeIcon icon={faDiscord} size="lg"></FontAwesomeIcon>
            </a>
          </div>
        </div>
      </div>
      ©Outread 2025
    </div>
  );
}

function MobileComponent() {
  return (
    <div className="w-full h-[900px] bg-[#132435] text-white flex flex-col items-center justify-start">
      <Divider className="mt-4 mb-[26px]  bg-gray-200" />
      <div className=" flex flex-row w-full justify-between my-4 text-[12px]">
        <a href="/newsletter">Newletter</a>
        <a href="/privacy">Privacy Policy</a>
        <a href="/tos">Terms of Service</a>
        <a href="/contact-us">Contact Us</a>
      </div>
      <div className="flex flex-row gap-2">
        <div>
          <a href="https://www.instagram.com/outread.official/">
            <FontAwesomeIcon icon={faInstagram} size="lg"></FontAwesomeIcon>
          </a>
        </div>
        <div>
          <a href="https://www.linkedin.com/company/outread/">
            <FontAwesomeIcon icon={faLinkedin} size="lg"></FontAwesomeIcon>
          </a>
        </div>
        <div>
          <div>
            <a href="https://discord.gg/Sd8tMr6g5v">
              <FontAwesomeIcon icon={faDiscord} size="lg"></FontAwesomeIcon>
            </a>
          </div>
        </div>
      </div>
      <div className="mb-[10px] mt-4 flex flex-row items-center justify-center w-full h-auto text-sm">
        ©Outread 2025
      </div>
    </div>
  );
}

export default function App() {
  const isMobile = useClientMediaQuery("(max-width: 768px)");
  const pathname = usePathname();
  const [hasCustomFooter, setHasCustomFooter] = useState(
    pathname == "/darwinai" || pathname == "/history",
  );

  useEffect(() => {
    setHasCustomFooter(pathname == "/darwinai" || pathname == "/history");
  }, [pathname]);

  // Hide footer on chatbot page
  if (pathname === "/chatbot") {
    return null;
  }

  return (
    <>
      {hasCustomFooter ? (
        <LoggedInFooter />
      ) : (
        <>{isMobile ? <MobileComponent /> : <DesktopComponent />}</>
      )}
    </>
  );
}
