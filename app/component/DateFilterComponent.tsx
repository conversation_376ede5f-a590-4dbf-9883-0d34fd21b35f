"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";

interface DateFilterComponentProps {
  startYear: number | null;
  startMonth: number | null;
  endYear: number | null;
  endMonth: number | null;
}

export default function DateFilterComponent({
  startYear,
  startMonth,
  endYear,
  endMonth,
}: DateFilterComponentProps) {
  const router = useRouter();
  const [showDateFilter, setShowDateFilter] = useState(false);
  const [localStartYear, setLocalStartYear] = useState(startYear);
  const [localStartMonth, setLocalStartMonth] = useState(startMonth);
  const [localEndYear, setLocalEndYear] = useState(endYear);
  const [localEndMonth, setLocalEndMonth] = useState(endMonth);

  const toggleDateFilter = () => {
    setShowDateFilter(!showDateFilter);
  };

  const applyDateFilter = () => {
    const params = new URLSearchParams();
    if (localStartYear) params.append("startYear", localStartYear.toString());
    if (localStartMonth)
      params.append("startMonth", localStartMonth.toString());
    if (localEndYear) params.append("endYear", localEndYear.toString());
    if (localEndMonth) params.append("endMonth", localEndMonth.toString());
    window.location.href = `/all-articles?${params.toString()}`;
  };

  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear; i >= currentYear - 10; i--) {
      years.push(i);
    }
    return years;
  };

  const monthOptions = [
    { value: 1, label: "January" },
    { value: 2, label: "February" },
    { value: 3, label: "March" },
    { value: 4, label: "April" },
    { value: 5, label: "May" },
    { value: 6, label: "June" },
    { value: 7, label: "July" },
    { value: 8, label: "August" },
    { value: 9, label: "September" },
    { value: 10, label: "October" },
    { value: 11, label: "November" },
    { value: 12, label: "December" },
  ];

  return (
    <div>
      <button
        onClick={toggleDateFilter}
        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      >
        {showDateFilter ? "Hide Date Filter" : "Show Date Filter"}
      </button>
      {showDateFilter && (
        <div className="mt-4 space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Start Date
            </label>
            <div className="flex items-center space-x-4">
              <select
                value={localStartYear || ""}
                onChange={(e) =>
                  setLocalStartYear(Number(e.target.value) || null)
                }
                className="bg-white text-black p-2 rounded"
              >
                <option value="">Year</option>
                {generateYearOptions().map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
              <select
                value={localStartMonth || ""}
                onChange={(e) =>
                  setLocalStartMonth(Number(e.target.value) || null)
                }
                className="bg-white text-black p-2 rounded"
              >
                <option value="">Month</option>
                {monthOptions.map((month) => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              End Date
            </label>
            <div className="flex items-center space-x-4">
              <select
                value={localEndYear || ""}
                onChange={(e) =>
                  setLocalEndYear(Number(e.target.value) || null)
                }
                className="bg-white text-black p-2 rounded"
              >
                <option value="">Year</option>
                {generateYearOptions().map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
              <select
                value={localEndMonth || ""}
                onChange={(e) =>
                  setLocalEndMonth(Number(e.target.value) || null)
                }
                className="bg-white text-black p-2 rounded"
              >
                <option value="">Month</option>
                {monthOptions.map((month) => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <button
            onClick={applyDateFilter}
            className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
          >
            Apply Filter
          </button>
        </div>
      )}
    </div>
  );
}
