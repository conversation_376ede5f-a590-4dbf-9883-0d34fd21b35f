import { createClient } from "@/lib/supabase/client";

export const handleAppleSignIn = async () => {
  const supabase = createClient();
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "apple",
    options: {
      redirectTo: `${window.location.origin}/auth/callback`, // This will automatically use the correct protocol
    },
  });

  if (error) {
    console.error("Sign in error:", error);
    return;
  }
};
