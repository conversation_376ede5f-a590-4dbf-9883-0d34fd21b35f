import { createClient } from "@/lib/supabase/server";
import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { cookies } from "next/headers";
import { revalidatePath } from "next/cache";

export async function GET(request: Request) {
  // Initialize Supabase client
  const requestUrl = new URL(request.url);
  const { searchParams, origin } = requestUrl;

  console.log("Full callback URL:", request.url);
  console.log("Headers:", Object.fromEntries(request.headers));

  const code = searchParams.get("code");
  const error = searchParams.get("error");
  const errorDescription = searchParams.get("error_description");
  const next = searchParams.get("next") ?? "/";

  if (error) {
    console.log("OAuth error details:", {
      error,
      errorDescription,
      fullUrl: request.url,
    });
    return NextResponse.redirect(`${origin}/auth/auth-code-error`);
  }

  if (!code) {
    console.error("No code received in callback");
    return NextResponse.redirect(`${origin}/auth/auth-code-error`);
  }

  try {
    const supabase = await createClient();
    const { data, error: exchangeError } =
      await supabase.auth.exchangeCodeForSession(code);

    if (exchangeError) {
      console.error("Session exchange error:", {
        error: exchangeError,
        code,
      });
      throw exchangeError;
    }

    console.log("Session exchange successful:", {
      session: data.session ? "Session present" : "No session",
      user: data.user?.id,
    });

    // Check if the user is new (signing up) or existing (signing in)
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        console.log("User found:", user.id);
        console.log("Creating new user in database");
        try {
          // Try to create a new user in the database
          await prisma.user.create({
            data: {
              email: user.email!,
              supabaseUserId: user.id.toUpperCase(),
              isWebsite: true,
            },
          });
        } catch (error) {
          // If creation fails, the user already exists
          console.log("Existing user signed in:", user.id);
        }

        try {
          // Create newsletter subscriber
          await prisma.newsletterSubscriber.upsert({
            where: {
              email: user.email!,
            },
            create: {
              email: user.email!,
            },
            update: {
              email: user.email!,
            },
          });
        } catch (error) {
          console.error("Error creating newsletter subscriber:", error);
        }
      }
    } catch {
      console.log("No user found");
    }

    // Retrieve the original URL from the cookie
    const cookieStore = cookies();
    let originalUrl = cookieStore.get("originalUrl")?.value;

    // Clear the originalUrl cookie
    if (originalUrl?.startsWith("/article") && !originalUrl.includes("-")) {
      originalUrl = "/";
    }
    cookieStore.delete("originalUrl");

    revalidatePath("/");

    // Determine where to redirect the user
    const redirectPath = originalUrl || next;

    const forwardedHost = request.headers.get("x-forwarded-host");
    const isLocalEnv = process.env.NODE_ENV === "development";

    const redirectUrl = isLocalEnv
      ? `${origin}${redirectPath}`
      : forwardedHost
        ? `https://${forwardedHost}${redirectPath}`
        : `${origin}${redirectPath}`;

    console.log("Redirecting to:", redirectUrl);
    return NextResponse.redirect(redirectUrl);
  } catch (error) {
    console.error("Callback processing error:", error);
    return NextResponse.redirect(`${origin}/auth/auth-code-error`);
  }
}
