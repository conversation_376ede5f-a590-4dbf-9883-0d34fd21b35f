import React from "react";

const ArticleLoading = () => {
  return (
    <div className="max-w-full w-full mx-auto p-5 flex flex-col justify-center items-center bg-[#132435]">
      <div className="w-full max-w-[393px] md:max-w-[756px] lg:max-w-[1040px] mx-auto p-5">
        <div className="animate-pulse flex flex-col md:flex-row w-full items-start justify-between gap-8">
          {/* Content section */}
          <div className="w-full md:w-2/3 flex flex-col min-h-[500px] order-2 md:order-1">
            <div className="flex flex-col max-w-[700px] mb-2">
              {/* Title and subtitle skeletons */}
              <div className="h-6 md:h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-px bg-gray-400 my-2"></div>

              {/* Metadata skeletons */}
              <div className="flex justify-between">
                <div className="h-3 md:h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="flex flex-col gap-1">
                  <div className="h-3 md:h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-3 md:h-4 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
              <div className="h-px bg-gray-400 my-2"></div>
            </div>

            {/* Category skeletons */}
            <div className="flex flex-wrap mb-4">
              <div className="h-8 bg-gray-500 rounded w-20 mr-2 mb-2"></div>
              <div className="h-8 bg-gray-500 rounded w-20 mr-2 mb-2"></div>
            </div>

            {/* Content skeletons */}
            <div className="w-full mt-2 flex justify-center flex-col">
              <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-11/12 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-10/12 mb-2"></div>
            </div>
          </div>

          {/* Image and button section */}
          <div className="w-full md:w-1/3 flex flex-col items-center order-1 md:order-2">
            <div className="w-[220px] md:w-full h-64 md:h-96 bg-gray-200 rounded mb-4"></div>
            <div className="h-[50px] bg-[#88D84D] rounded w-[220px] md:w-[300px]"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleLoading;
