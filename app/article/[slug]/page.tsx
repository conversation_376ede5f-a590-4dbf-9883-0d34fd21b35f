import { redirect } from "next/navigation";
import ArticleDisplay from "@/app/component/ArticleComponents/ArticleDisplay";
import prisma from "@/lib/prisma";
import { getUser } from "@/lib/cache/getUser";

interface Summary {
  heading: string;
  content: string;
}

interface Article {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  coverImage: string;
  originalPaperTitle: string;
  authorName: string;
  audioId: string | null;
  doi: string;
  altMetricScore: number;
  oneCardSummary: any;
  publishDate: Date | null;
  simpleSummary: [Summary];
  defaultSummary: [Summary];
  estimatedReadingTime: number;
  categories: { name: string }[];
}

async function getArticle(slug: string): Promise<Article | null> {
  try {
    const article = await prisma.article.findUnique({
      where: { slug },
      include: {
        articleImage: true,
        categories: true,
      },
    });

    if (!article) {
      console.warn(`Article not found for slug: ${slug}`);
      return null;
    }

    const defaultSummary = article.defaultSummary as [any];
    const simpleSummary = article.simpleSummary as [any];
    const oneCardSummary = article.oneCardSummary as any;

    return {
      ...article,
      coverImage: article.articleImage?.src || "",
      oneCardSummary: oneCardSummary,
      simpleSummary: simpleSummary,
      defaultSummary: defaultSummary,
      audioId: article.audioId || null,
    };
  } catch (error) {
    console.error(`Error fetching article for slug ${slug}:`, error);
    throw error; // Propagate the error to be handled by the caller
  }
}

export const dynamic = "force-dynamic";

export default async function ArticlePage({
  params,
}: {
  params: { slug: string };
}) {
  try {
    const article = await getArticle(params.slug);

    if (!article) {
      console.warn(`Article not found for slug: ${params.slug}`);
      throw new Error("Article not found");
    }

    const user = await getUser();

    if (!user) {
      console.warn(
        `User not authenticated when accessing article: ${params.slug}`,
      );
      return redirect("/signin");
    }

    const isPaidUser = user.role === "PAID_USER";
    const isAdmin = user.role === "ADMIN";

    return (
      <>
        <ArticleDisplay
          isPaid={isPaidUser}
          isAdmin={isAdmin}
          article={article}
        />
      </>
    );
  } catch (error) {
    console.error("Error in ArticlePage:", error);
    throw error; // This will be caught by the error boundary
  }
}
