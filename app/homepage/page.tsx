"use server";

import { useRouter } from "next/navigation";
import { getUser } from "@/lib/cache/getUser";
import { useAuthStore } from "@/lib/zustand/zustand";
import prisma from "@/lib/prisma";
import { redirect } from "next/navigation";

export default async function App() {
  const user = await getUser();
  const hasOnboard = await prisma.preference.findUnique({
    where: { userId: user?.id },
  });

  if (hasOnboard) redirect("/darwinai");
  redirect("/onboarding");
}
