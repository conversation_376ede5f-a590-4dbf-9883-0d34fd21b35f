"use client";

import React, { useState, useRef, useEffect } from "react";
import { useClientMediaQuery } from "@/hooks/useClientMediaQuery";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/lib/zustand/zustand";
import DataLoadingSkeleton from "../component/History/DataLoadingSkeleton";

interface Insight {
  type: "outread" | "semantic_scholar";
  paper_id: string;
  insight: string;
  keyword: string;
  insight_explanation: string;
  doi: string;
  title?: string;
  authors?: string[];
  year?: number;
  citationCount?: number;
  url?: string;
}

interface RelevantPaper {
  title: string;
  author: string;
  slug?: string;
  doi: string;
  relevance_score: number;
  altMetricScore: number;
  one_card_summary: {
    content: string;
    heading: string;
  };
  type: "outread" | "semantic_scholar";
  abstract?: string;
  citationCount?: number;
}

interface ArticleData {
  summary: {
    insight: Insight[];
    summarised_response: string;
  };
  relevant_papers: RelevantPaper[];
}

interface PreviousQuestion {
  id: string;
  query: string;
  complexity: string;
  createdAt: string;
  response: ArticleData;
}

function HistoryList({
  className,
  isMobile,
  questionList,
  isLoading,
}: {
  isMobile: boolean;
  className: string;
  questionList: {
    title: string;
    questions: { query: string; complexity: string }[];
  }[];
  isLoading: boolean;
}) {
  const router = useRouter();
  const handleClick = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    searchParam: string,
    complexity: string,
  ) => {
    e.preventDefault();
    router.push(`/darwinai?search=${searchParam}&complexity=${complexity}`);
  };

  return (
    <div
      className={`${className} flex flex-col items-center py-2 bg-[#F4F7FB]`}
    >
      <div className="flex flex-col gap-5 w-full justify-center  text-center">
        {questionList.length > 0 && !isLoading && (
          <>
            {questionList.map((questionBlock, index) => {
              if (questionBlock.questions.length === 0) {
                return <React.Fragment key={index}></React.Fragment>;
              } else {
                return (
                  <>
                    <div className="mt-[60px] mb-[60px]" key={index}>
                      <p className="font-medium text-2xl text-left text-[#686868] mb-10 ">
                        {questionBlock.title}
                      </p>
                      <div className="flex flex-col gap-4">
                        {questionBlock.questions.map((question, index) => (
                          <div
                            key={index}
                            className="flex justify-between w-full bg-white items-center drop-shadow-lg rounded-xl  mb-4  py-2 px-4 cursor-default"
                          >
                            <p className="text-[#132435] text-lg font-normal text-left">
                              {question.query}
                            </p>
                            <Button
                              onClick={(e) =>
                                handleClick(
                                  e,
                                  question.query,
                                  question.complexity,
                                )
                              }
                              disabled={isLoading}
                              className="w-fit bg-white text-[#D9D9D9] hover:bg-[#D9D9D9] hover:text-white  min-w-fit px-[10px] py-[25px] rounded-full transition-all"
                            >
                              <svg
                                className="text-2xl text-default-400 pointer-events-none flex-shrink-0 "
                                xmlns="http://www.w3.org/2000/svg"
                                width="32"
                                height="32"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  fill="currentColor"
                                  d="m19.6 21l-6.3-6.3q-.75.6-1.725.95T9.5 16q-2.725 0-4.612-1.888T3 9.5t1.888-4.612T9.5 3t4.613 1.888T16 9.5q0 1.1-.35 2.075T14.7 13.3l6.3 6.3zM9.5 14q1.875 0 3.188-1.312T14 9.5t-1.312-3.187T9.5 5T6.313 6.313T5 9.5t1.313 3.188T9.5 14"
                                />
                              </svg>
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                    {index !== questionList.length - 1 && (
                      <Divider key={index} className="bg-[#D9D9D9] h-[1.7px]" />
                    )}
                  </>
                );
              }
            })}
          </>
        )}
      </div>
    </div>
  );
}

function LoadingAnimation() {
  const messages = [
    "Fetch Data",
    "Searching internal and external resources",
    "Linking Resources",
    "Checking Relevance",
    "Validating Results",
    "Listing History",
  ];
  const [currentMessage, setCurrentMessage] = useState(0);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % messages.length);
    }, 5000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <div className={`xl:w-10/12 w-full lg:max-w-4xl`}>
      <p className="text-center text-xl font-medium text-[black]">
        {messages[currentMessage]}...
      </p>
      <DataLoadingSkeleton />
    </div>
  );
}

function HistoryPage() {
  const isMobile = useClientMediaQuery("(max-width: 768px)");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { user, isAuthenticated } = useAuthStore();
  const [preQuestionData, setPreQuestionData] = useState<PreviousQuestion[]>(
    [],
  );
  const [questionListOfToday, setQuestionListOfToday] = useState<
    { query: string; complexity: string }[]
  >([]);
  const [questionListOfPastSevenDays, setQuestionListOfPastSevenDays] =
    useState<{ query: string; complexity: string }[]>([]);
  const fetchPreviousQuestions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/getPreviousQuestions?id=${user?.id}`, {
        next: { revalidate: 3600 },
      }); // Replace USER_ID with the actual user ID
      const data = await response.json();
      if (data.length > 0) {
        setPreQuestionData(data);
      }
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching previous questions:", error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (preQuestionData.length > 0) {
      setQuestionListOfPastSevenDays([]);
      setQuestionListOfToday([]);
      preQuestionData.map((item, index) => {
        if (
          new Date(item.createdAt).toDateString() === new Date().toDateString()
        ) {
          setQuestionListOfToday((prev) => [
            ...prev,
            { query: item.query, complexity: item.complexity },
          ]);
        } else {
          setQuestionListOfPastSevenDays((prev) => [
            ...prev,
            { query: item.query, complexity: item.complexity },
          ]);
        }
      });
    }
  }, [preQuestionData]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchPreviousQuestions();
    }
  }, [isAuthenticated]);

  const content = (
    <>
      <div className="flex flex-col items-center justify-start w-full h-full bg-[#F4F7FB] ">
        {error && <div className="text-red-500 mb-4">{error}</div>}
        <HistoryList
          className={`xl:w-10/12 w-full lg:max-w-4xl  ${isMobile ? "w-full" : "w-1/2"}   mb-${isMobile ? "4" : "8"} `}
          isLoading={isLoading}
          isMobile={isMobile!}
          questionList={[
            {
              title: "Today",
              questions: questionListOfToday,
            },
            {
              title: "Last 7 Days",
              questions: questionListOfPastSevenDays,
            },
          ]}
        />
        {isLoading && <LoadingAnimation />}
        <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
          <ModalContent className="text-black">
            <ModalHeader className="flex flex-col gap-1">
              Credit Limit Reached
            </ModalHeader>
            <ModalBody>
              <p>
                You have reached your credit limit. Please wait until tomorrow
                for your quota to reset.
              </p>
            </ModalBody>
            <ModalFooter>
              <Button color="primary" onPress={() => setIsModalOpen(false)}>
                Close
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </>
  );

  return (
    <div className="flex w-full min-h-screen bg-[#F5F5F5]">
      {/* {!isMobile && <Sidebar questions={previousQuestions} onQuestionClick={handleQuestionClick} />} */}
      <div
        className={`bg-[#F4F7FB] flex-1 flex flex-col items-center justify-start p-${isMobile ? "4" : "8"}`}
      >
        {content}
      </div>
    </div>
  );
}

export default HistoryPage;
