// app/signup/action.ts
"use server";

import { createClient } from "@/lib/supabase/server";
import prisma from "@/lib/prisma";
import { cookies } from "next/headers";
import { revalidatePath } from "next/cache";

export async function signUp(prevState: any, formData: FormData) {
  const supabase = createClient();
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  if (!email || !password) {
    return {
      error: "Email and password are required",
    };
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });

  if (error) {
    console.error("Signup error:", error);
    return { error: error.message };
  }

  if (!data) {
    return {
      error: "An error occurred while creating your account. Please try again.",
    };
  }

  const user = await prisma.user.create({
    data: {
      email: email,
      supabaseUserId: data.user!.id.toUpperCase(),
      isWebsite: true,
    },
  });

  try {
    await prisma.newsletterSubscriber.upsert({
      where: {
        email: email,
      },
      create: {
        email: email,
      },
      update: {
        email: email,
      },
    });
  } catch (error) {
    console.error("Error creating newsletter subscriber:", error);
  }

  // Retrieve the original URL from the cookie
  const cookieStore = cookies();
  const cookie = cookieStore.get("originalUrl");

  console.log(cookie);

  let originalUrl = cookieStore.get("originalUrl")?.value;
  // Clear the originalUrl cookie
  if (originalUrl?.startsWith("/article") && !originalUrl.indexOf("-")) {
    originalUrl = "/";
  }

  cookieStore.delete("originalUrl");

  revalidatePath("/");

  return {
    userId: user.id,
    success: "Account created successfully and signed in successfully",
    redirectUrl: originalUrl || "/homepage",
  };
}
