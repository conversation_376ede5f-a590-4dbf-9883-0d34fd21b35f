import { useCallback } from "react";
import { handleViewContent } from "../component/FacebookConversionAPI"; // Replace with your Facebook Pixel ID

const useClickTracking = () => {
  const trackClick = useCallback((event: React.MouseEvent<HTMLElement>) => {
    const element = event.currentTarget;
    handleViewContent(element.id || "unknown");
  }, []);

  return trackClick;
};

export default useClickTracking;
