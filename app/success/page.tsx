"use client";

import { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { trackCheckoutSuccess } from "@/lib/branch";
import { handlePurchase } from "../component/FacebookConversionAPI";
import { useAuthStore } from "@/lib/zustand/zustand";

export default function SuccessPage() {
  const [message, setMessage] = useState("Processing your subscription...");
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuthStore();

  if (!searchParams) {
    router.push("/");
  }

  const sessionId = searchParams.get("session_id");

  const updateUserStatus = async (sessionId: string) => {
    try {
      const response = await fetch("/api/update-user-status", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ session_id: sessionId }),
      });

      if (response.ok) {
        const data = await response.json();
        setMessage(
          "Thank you for your subscription! Your account has been upgraded.",
        );

        // Track checkout success with the new data from the API
        if (data.planName && data.amount) {
          handlePurchase(data.plan, data.amount);
          await trackCheckoutSuccess(
            user?.id as string,
            data.planName,
            data.amount,
          );
        } else {
          console.error("Missing planName or amount in API response");
        }
      } else {
        const errorData = await response.json();
        setMessage(`Error: ${errorData.error.message}`);
      }
    } catch (error) {
      console.error("Error updating user status:", error);
      setMessage(
        "An error occurred while processing your subscription. Please contact support.",
      );
    }
  };

  useEffect(() => {
    if (sessionId) {
      updateUserStatus(sessionId);
    }
  }, [sessionId]);

  function SearchBarFallback() {
    return <>Loading...</>;
  }

  return (
    <Suspense fallback={<SearchBarFallback />}>
      <div className="h-[600px] w-full bg-[#132435] text-white flex flex-col items-center justify-center">
        <div className="h-[400px] flex flex-col items-center justify-center">
          <div className="bg-white text-black  p-8 rounded-lg shadow-md text-center">
            <h1 className="text-2xl font-bold mb-4 text-[#88D84D]">Success!</h1>
            <p className="text-lg">{message}</p>
            <button
              className="bg-[#88D84D] text-white rounded-full px-4 py-2 mt-4"
              onClick={() => router.push("/homepage")}
            >
              Go to Homepage
            </button>
          </div>
        </div>
      </div>
    </Suspense>
  );
}
