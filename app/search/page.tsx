"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { BookmarkButton } from "../catalogue/components/BookmarkButton";
import CategoryButton from "../collections/CategoryButton";

interface SearchResult {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  coverImage: string;
  authorName: string;
  doi: string;
  altMetricScore: number;
  estimatedReadingTime: number;
  categories: { name: string }[];
  simpleSummary: {
    heading: string;
    content: string;
  }[];
  originalPaperTitle: string;
}

type SearchType = "semantic" | "doi" | "authorName" | "originalPaperTitle";

export default function SearchPage() {
  const searchParams = useSearchParams();
  const [query, setQuery] = useState("");
  const [searchType, setSearchType] = useState<SearchType>("semantic");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const initialQuery = searchParams.get("q");
    if (initialQuery) {
      setQuery(initialQuery);
      handleSearch(initialQuery, searchType);
    }
  }, [searchParams]);

  const handleSearch = async (searchQuery: string, type: SearchType) => {
    setIsLoading(true);
    setResults([]);

    try {
      const response = await fetch("/api/search", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query: searchQuery, type }),
      });

      if (!response.ok) {
        throw new Error("Search request failed");
      }

      const data = await response.json();
      setResults(data.results);
    } catch (error) {
      console.error("Error during search:", error);
      // Handle error (e.g., show error message to user)
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageError = (articleId: string) => {
    console.log(articleId);
    setResults((prevResults) =>
      prevResults.map((result) =>
        result.id === articleId ? { ...result, coverImage: "" } : result,
      ),
    );
  };

  return (
    <div className="mx-auto bg-[#132435] w-full min-h-screen py-4">
      <div className="p-4 w-[393px] mb-4 lg:w-[850px] mx-auto bg-[#132435] text-white min-h-full flex justify-start flex-col">
        <h1 className="text-2xl text-center mb-8">Search</h1>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSearch(query, searchType);
          }}
          className="mb-12 flex justify-center  items-center w-full"
        >
          <div className="flex w-full max-w-2xl relative">
            <select
              value={searchType}
              onChange={(e) => setSearchType(e.target.value as SearchType)}
              className={`absolute left-0 top-0 bottom-0 w-32 text-center  p-2 bg-[#1E2A3A] text-white border-r border-gray-600 rounded-l-full`}
            >
              <option value="semantic">Semantic :</option>
              <option value="doi">DOI :</option>
              <option value="authorName">Author :</option>
              <option value="originalPaperTitle">Title :</option>
            </select>
            <div
              className={`flex pl-32 bg-[#1E2A3A] rounded-full overflow-hidden justify flex-grow`}
            >
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search for articles..."
                className="flex-grow w-[170px] ml-4lg:w-[500px] text-start px-4 bg-transparent focus:outline-none text-white"
              />
              <button
                type="submit"
                className="p-4 bg-[#1E2A3A]"
                disabled={isLoading}
              >
                {isLoading ? (
                  <svg
                    className="animate-spin h-6 w-6 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                ) : (
                  <svg
                    className="h-6 w-6 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </form>

        {isLoading ? (
          <div className="space-y-6">
            {[...Array(3)].map((_, index) => (
              <div
                key={index}
                className="flex bg-[#27394F] rounded-lg overflow-hidden shadow-lg p-4 h-[200px] lg:h-[227px] w-full"
              >
                <div className="w-[155px] lg:w-[178px] flex justify-center items-center relative">
                  <div className="w-full h-full bg-gray-300 rounded-lg animate-pulse"></div>
                </div>
                <div className="w-[200px] lg:w-3/4 px-4 relative flex flex-col justify-between">
                  <div>
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2 animate-pulse"></div>
                    <div className="h-4 bg-gray-300 rounded w-full mb-2 animate-pulse"></div>
                    <div className="h-4 bg-gray-300 rounded w-1/2 animate-pulse"></div>
                  </div>
                  <div className="h-6 w-16 bg-gray-300 rounded animate-pulse"></div>
                </div>
                <div className="flex items-start">
                  <div className="h-8 w-8 bg-gray-300 rounded-full animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        ) : results.length > 0 ? (
          <div>
            <h2 className="text-2xl mb-6">Search Results</h2>
            <div className="space-y-6">
              {results.map((article: SearchResult) => (
                <Link
                  href={`/article/${article.slug}`}
                  key={article.id}
                  className="flex bg-[#27394F] rounded-2xl overflow-hidden shadow-lg p-4 lg:p-8 h-[200px] lg:h-[288px] w-full"
                >
                  <div className="w-[155px] lg:w-[178px] flex justify-center items-center relative">
                    {article.coverImage && (
                      <Image
                        className="rounded-2xl"
                        width={178}
                        height={268}
                        style={{
                          width: "100%",
                          height: "auto",
                        }}
                        src={article.coverImage}
                        alt={article.title}
                        onError={() => handleImageError(article.id)}
                      />
                    )}
                  </div>
                  <div className="w-[200px] lg:w-3/4 px-4 lg:px-8 relative flex items-start justify-start">
                    <div className="flex flex-col h-full items-start justify-between">
                      <div>
                        <h2 className="text-[13px] lg:text-2xl font-semibold mb-2 line-clamp-4">
                          {article.title}
                        </h2>
                        <p className="text-gray-200 text-[11px] lg:text-xl mb-2 line-clamp-3">
                          {article.subtitle}
                        </p>
                        {searchType === "doi" && (
                          <p className="text-gray-300 text-[11px] lg:text-lg mb-2">
                            DOI: {article.doi}
                          </p>
                        )}
                        {/* {searchType === "originalPaperTitle" && (
                          <p className="text-gray-300 text-[11px] lg:text-lg mb-2 truncate-8">
                            Original Paper:{" "}
                            {article.originalPaperTitle || article.title}
                          </p>
                        )} */}
                        {searchType === "authorName" && (
                          <p className="text-gray-300 text-[11px] lg:text-lg mb-2">
                            Author: {article.authorName}
                          </p>
                        )}
                        <div className="flex items-end  w-full gap-2 overflow-x-scroll mt-[14px] mb-[12px]">
                          {article.categories.map((category, index) => (
                            <CategoryButton
                              key={index}
                              category={category.name}
                            />
                          ))}
                          <CategoryButton
                            category={`${article.estimatedReadingTime} min`}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <BookmarkButton
                      articleId={article.id}
                      initialIsBookmarked={false}
                    />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        ) : (
          <></>
        )}
      </div>
    </div>
  );
}
