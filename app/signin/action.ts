"use server";
import { createClient } from "@/lib/supabase/server";
import { revalidatePath } from "next/cache";
import { cookies } from "next/headers";

export async function signIn(
  prevState: any,
  formData: FormData,
): Promise<{
  error?: string;
  success?: string;
  redirectUrl?: string;
}> {
  const supabase = createClient();

  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  const { error } = await supabase.auth.signInWithPassword({
    email: email,
    password: password,
  });

  if (error) {
    return {
      error: "Invalid email or password. Please try again.",
    };
  }

  // Retrieve the original URL from the cookie
  const cookieStore = cookies();
  const cookie = cookieStore.get("originalUrl");

  console.log(cookie);

  let originalUrl = cookieStore.get("originalUrl")?.value;
  // Clear the originalUrl cookie
  if (originalUrl?.startsWith("/article") && !originalUrl.indexOf("-")) {
    originalUrl = "/";
  }

  cookieStore.delete("originalUrl");

  revalidatePath("/");
  return {
    success: "Signed in successfully",
    redirectUrl: originalUrl || "/homepage",
  };
}
