"use client";

import { useEffect } from "react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[#132435]">
      <div className="p-8 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-4xl font-bold text-red-600 mb-4">
          Oops! Something went wrong
        </h1>
        <p className="text-xl text-gray-700 mb-6">
          We&apos;re sorry, but it seems there was a server-side error.
        </p>
        <p className="text-md text-gray-600 mb-8">
          Our team has been notified and is working on resolving the issue.
        </p>
        <button
          onClick={() => reset()}
          className="px-6 py-3 bg-blue-500 text-white rounded hover:bg-blue-600 transition duration-300"
        >
          Try again
        </button>
      </div>
    </div>
  );
}
