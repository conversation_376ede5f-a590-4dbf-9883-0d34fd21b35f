@tailwind base;
@tailwind components;
@tailwind utilities;
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap");

@layer utilities {
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
  }
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px #132435 inset !important; /* match your bg */
  -webkit-text-fill-color: white !important;
  transition: background-color 5000s ease-in-out 0s;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom,
      transparent,
      rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
}

/* Toggle Switch Styles */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

input:checked+.slider {
  background-color: #2196f3;
}

input:focus+.slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked+.slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

textarea {
  color: #fff !important;
}

.selectdropdown span.text-foreground-500 {
  color: #fff !important;
}

.selectdropdown button:hover span.text-foreground-500 {
  color: #111827 !important;
}

/* Darwin Insight Sidebar Styles */
.citation-card {
  background: #fafafa; /* off-white color */
  border: 1px solid #e5e7eb; /* light gray border */
  border-radius: 0.5rem;
  padding: 0.75rem;
  transition: all 0.3s ease;
  color: #374151; /* dark gray text */
}

.citation-card:hover {
  background: #f5f5f5; /* slightly darker off-white on hover */
  border-color: #d1d5db;
}

.citation-card-highlighted {
  background: #ecfdf5 !important; /* light green background */
  border-color: #059669 !important; /* green border */
  box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2) !important; /* green glow */
  animation: highlightPulse 2s ease-in-out;
}

@keyframes highlightPulse {
  0% {
    box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.4);
  }
  100% {
    box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
  }
}

.citation-card h3 {
  color: #374151 !important;
}

.citation-card .text-muted-foreground {
  color: #374151 !important; /* Make year and other metadata black */
}

.citation-card .text-xs {
  color: #374151 !important; /* Make small text black */
}

.citation-card span {
  color: #374151 !important; /* Make all text in citation cards black */
}

.citation-card .badge {
  color: #374151 !important; /* Make badge text black */
}

.citation-card [class*="badge"] {
  color: #374151 !important; /* Make all badge variants black */
}

/* Darwin text input styling - make text black */
.chat-input textarea,
input[type="text"], 
input[type="search"], 
textarea,
input[placeholder="Search citations..."],
input[placeholder*="Search"] {
  color: #000 !important; /* black text in inputs */
  background-color: white !important; /* white background */
}

/* Fix placeholder text color */
.chat-input textarea::placeholder,
input::placeholder {
  color: #6b7280 !important; /* gray placeholder */
}

/* Override mode button styling - remove purple background */
.bg-primary {
  background-color: #059669 !important; /* green instead of purple */
}

.bg-secondary {
  background-color: #f3f4f6 !important; /* light gray background */
}

/* Ensure mode buttons don't have purple styling */
button.bg-primary {
  background-color: #059669 !important;
  color: white !important;
}

button:not(.bg-primary) {
  background-color: transparent !important;
}

/* Add these styles to your global CSS file or component */

@media print {
  /* Hide everything except the report */
  body * {
    visibility: hidden;
  }
  
  #executive-report, #executive-report * {
    visibility: visible;
  }
  
  #executive-report {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    background-color: white !important;
    color: black !important;
    padding: 20px;
  }
  
  /* Ensure all text is visible and black */
  #executive-report * {
    color: black !important;
    background-color: white !important;
  }
  
  /* Reset flexbox column layouts to fit page width */
  #executive-report .md\:grid-cols-2,
  #executive-report .md\:grid-cols-3 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }
  
  /* Ensure proper page breaks */
  h2 {
    page-break-before: auto;
    page-break-after: avoid;
  }
  
  /* Ensure page breaks don't happen in the middle of elements */
  p, div.bg-gray-100 {
    page-break-inside: avoid;
  }
  
  /* Hide the buttons */
  button {
    display: none;
  }
  
  /* Format specific elements */
  .border-l-4 {
    border-left-width: 5px !important;
    border-left-color: #3b82f6 !important;
  }
  
  /* Progress bars */
  .bg-gray-300 {
    background-color: #e5e7eb !important;
    print-color-adjust: exact;
  }
  
  .bg-blue-500 {
    background-color: #3b82f6 !important;
    print-color-adjust: exact;
  }
  
  /* Keywords */
  .bg-blue-100 {
    background-color: #dbeafe !important;
    border: 1px solid #3b82f6 !important;
    print-color-adjust: exact;
  }
  
  .text-blue-800 {
    color: #1e40af !important;
    print-color-adjust: exact;
  }
  
  /* Metrics boxes */
  .bg-gray-100 {
    background-color: #f3f4f6 !important;
    border: 1px solid #d1d5db !important;
    print-color-adjust: exact;
  }
}