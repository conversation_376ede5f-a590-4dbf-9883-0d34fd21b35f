import React from "react";
import { getBookmarkedArticles } from "../actions/getBookmarkedArticles";
import ArticleGrid from "../component/ArticleComponents/ArticleGrid";
import PaginationControls from "../component/PaginationControls";

interface BookmarksPageProps {
  searchParams: { page?: string };
}

export default async function BookmarksPage({
  searchParams,
}: BookmarksPageProps) {
  const currentPage = Number(searchParams.page) || 1;
  const {
    articles,
    totalPages,
    currentPage: page,
  } = await getBookmarkedArticles(currentPage);

  return (
    <div className="w-full bg-[#132435] text-white flex flex-col justify-center items-center">
      <div className="lg:w-[1100px] md:w-[756px] w-[393px] flex flex-col justify-center p-5">
        <h1 className="text-2xl mb-6 my-4 text-left">Bookmarked Articles</h1>
        <ArticleGrid articles={articles} columns={5} />
        <div className="mt-8 flex items-center justify-center w-full">
          <PaginationControls
            currentPage={page}
            totalPages={totalPages}
            href="/bookmarks"
          />
        </div>
      </div>
    </div>
  );
}
