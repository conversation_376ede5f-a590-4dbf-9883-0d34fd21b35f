:root {
  --default-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Ubuntu, "Helvetica Neue", Helvetica, Arial, "PingFang SC",
    "Hiragino Sans GB", "Microsoft Yahei UI", "Microsoft Yahei",
    "Source Han Sans CN", sans-serif;
}

.main-container {
  overflow: hidden;
}

.main-container,
.main-container * {
  box-sizing: border-box;
}

button,
input,
select,
textarea {
  outline: 0;
}

.main-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: auto;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px;
}

.circle-undefined-glyph {
  position: relative;
  width: 10.472px;
  height: 10.472px;
  margin: -44px 0 0 1138.528px;
  background: url(/031a5502-a873-434d-9bfb-170fae0f0be0.png) no-repeat 50%;
  background-size: cover;
  z-index: 29;
  overflow: hidden;
}

.rectangle {
  position: relative;
  width: 1280px;
  height: 96px;
  margin: 33.528px 0 0;
  background: #fff;
  z-index: 31;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}

.outread-logo-blue {
  position: absolute;
  width: 178px;
  height: 56.439px;
  top: 19.78px;
  left: 26px;
  background: url(/bc363818c3a781ca4bc52af94b52bb43e40ed60a.png) no-repeat 50%;
  background-size: cover;
  z-index: 32;
}

.frame {
  align-items: center;
  flex-wrap: nowrap;
  gap: 12.195px;
  position: absolute;
  width: 167.073px;
  height: 50px;
  top: 26px;
  left: 878px;
  padding: 0 12.195px;
  cursor: pointer;
  background: #fff;
  border: 2.439px solid #000;
  z-index: 33;
  border-radius: 6.707px;
}

.frame,
.newsletter {
  display: flex;
  justify-content: center;
}

.newsletter {
  align-items: flex-start;
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  width: 134px;
  height: 53px;
  color: #000;
  font-family: Poppins, var(--default-font-family);
  font-size: 21.95121955871582px;
  font-weight: 500;
  line-height: 52.439px;
  text-align: center;
  white-space: nowrap;
  z-index: 34;
}

.frame-1 {
  align-items: center;
  flex-wrap: nowrap;
  gap: 12.195px;
  position: absolute;
  width: 167.073px;
  height: 50px;
  top: 27px;
  left: 1056px;
  padding: 0 12.195px;
  cursor: pointer;
  background: #fff;
  border: 2.439px solid #000;
  z-index: 35;
  border-radius: 6.707px;
}

.frame-1,
.get-app {
  display: flex;
  justify-content: center;
}

.get-app {
  align-items: flex-start;
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  width: 88px;
  height: 53px;
  color: #000;
  font-family: Poppins, var(--default-font-family);
  font-size: 21.95121955871582px;
  font-weight: 500;
  line-height: 52.439px;
  text-align: center;
  white-space: nowrap;
  z-index: 36;
}

.rectangle-2 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  max-width: 2400px;
  margin: 0 auto;
  top: 98px;
  left: 0;
  background: #fff;
  gap: 50px;
  align-items: center;
}

@media only screen and (max-width: 768px) {
  .rectangle-2 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    max-width: 2400px;
    margin: 0 auto;
    top: 98px;
    left: 0;
    background: #fff;
    gap: 50px;
    align-items: center;
  }
}

.rectangle-3 {
  flex: 1.5;
  max-width: 800px;
  height: 100%;
  width: 100%;
  background: #111e2b;
  border: 1px solid #27394f;
  z-index: 14;
  border-radius: 38px;
  margin: 10px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  flex-direction: column;
}

.darwin-ai-logo-white,
.rectangle-3 {
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
}

.darwin-ai-logo-white {
  position: relative;
  width: 210px;
  height: 70px;
  background: url(/6ecf89567da3a2f22065acbd7bb2764bce55325c.png) no-repeat 50%;
  background-size: cover;
}

.darwin-ai-logo-white,
.frame-4 {
  margin: 20px auto;
  padding: 10px 20px;
}

.frame-4 {
  width: 200px;
  cursor: pointer;
  background: #88d84c;
  border-radius: 13.044px;
}

.frame-4,
.try-for-free {
  display: flex;
  align-items: center;
  justify-content: center;
}

.try-for-free {
  color: #fff;
  font-family: Poppins, var(--default-font-family);
  font-size: 16px;
  font-weight: 500;
}

.qa-platform-hero {
  width: 600.62px;
  height: 390px;
  margin: 20px auto;
  background: url(/ea12a346421c291b25f69a7462cf365fcb5e4877.png) no-repeat 50%;
  background-size: cover;
  padding: 10px 20px;
}

@media only screen and (max-width: 768px) {
  .qa-platform-hero {
    width: 70vw;
    height: 20vh;
    margin: 20px auto;
    background: url(/ea12a346421c291b25f69a7462cf365fcb5e4877.png) no-repeat 50%;
    background-size: cover;
    padding: 10px 20px;
  }
}

.flex-column-ea {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 600px;
  height: 100%;
  top: 93px;
  left: 753px;
  z-index: 24;
  margin: 10px;
  justify-content: center;
}

.ask-question-answers {
  position: relative;
  /* width:450px; */
  /* height:61px; */
  margin: 0;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
  /* line-height:28px; */
  text-align: left;
  z-index: 21;
}

.ask-any-question {
  font-weight: 500;
}

.and-get,
.ask-any-question {
  position: relative;
  color: #111e2b;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  line-height: 28px;
  text-align: left;
}

.and-get {
  font-weight: 400;
}

.ask-any-question-5 {
  font-weight: 500;
}

.and-get-6,
.ask-any-question-5 {
  position: relative;
  color: #111e2b;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  line-height: 28px;
  text-align: left;
}

.and-get-6 {
  font-weight: 400;
}

.ask-any-question-7 {
  position: relative;
  color: #111e2b;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  text-align: left;
}

.get-insights {
  margin: 20px 0 0;
  z-index: 22;
}

.get-insights,
.get-insights-8 {
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  text-align: left;
}

.get-insights-8 {
  color: #111e2b;
}

.individual-insights {
  font-weight: 500;
}

.get-insights-9,
.individual-insights {
  color: #111e2b;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  line-height: 28px;
  text-align: left;
}

.get-insights-9 {
  font-weight: 400;
}

.individual-insights-a {
  font-weight: 500;
}

.answering-query,
.individual-insights-a {
  position: relative;
  color: #111e2b;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  line-height: 28px;
  text-align: left;
}

.answering-query {
  font-weight: 400;
}

.search-papers {
  margin: 20px 0 0;
  z-index: 23;
}

.search-papers,
.search-papers-b {
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
}

.search-papers-b {
  color: #111e2b;
}

.research-papers {
  font-weight: 500;
}

.research-papers,
.search-papers-c {
  position: relative;
  color: #111e2b;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  line-height: 28px;
  text-align: left;
}

.search-papers-c {
  font-weight: 400;
}

.research-papers-d {
  color: #111e2b;
  font-weight: 500;
}

.ask-questions,
.research-papers-d {
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
}

.ask-questions {
  margin: 20px 0 0;
  font-weight: 400;
}

.ask-questions-like {
  font-weight: 500;
}

.ask-questions-like,
.gut-microbiome-depression {
  position: relative;
  color: #000;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  line-height: 28px;
  text-align: left;
}

.gut-microbiome-depression {
  font-weight: 400;
}

.frame-e {
  flex-wrap: nowrap;
  gap: 5.797px;
  width: 257.639px;
  height: 35px;
  margin: 24px 0 0 89px;
  padding: 27.537px 20.291px;
  cursor: pointer;
  background: #88d84c;
  border: none;
  z-index: 18;
  border-radius: 13.044px;
}

.frame-e,
.try-for-free-f {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.try-for-free-f {
  color: #fff;
  font-family: Poppins, var(--default-font-family);
  font-size: 16px;
  font-weight: 500;
}

.flex-column-ccbc {
  position: absolute;
  width: 1.8%;
  height: 34.81%;
  top: 11.04%;
  left: 56.33%;
  z-index: 28;
}

.vector {
  margin: 0;
  background: url(/73bdc486-5571-4129-b947-231a4b4c135f.png) no-repeat 50%;
  background-size: 100% 100%;
  z-index: 25;
}

.vector,
.vector-10 {
  position: relative;
  width: 23px;
  height: 17px;
}

.vector-10 {
  margin: 68px 0 0;
  background: url(/bfa76f81-9368-422a-a264-368ce5acacf8.png) no-repeat 50%;
  background-size: 100% 100%;
  z-index: 26;
}

.vector-11 {
  margin: 69px 0 0;
  background: url(/44f5dcfd-798e-4478-8c2f-65b9875a7f61.png) no-repeat 50%;
  background-size: 100% 100%;
  z-index: 27;
}

.vector-11,
.vector-12 {
  position: relative;
  width: 23px;
  height: 17px;
}

.vector-12 {
  margin: 101px 0 0;
  background: url(/13f68f9e-a2f5-4c6f-add3-288dec3a1c1f.png) no-repeat 50%;
  background-size: 100% 100%;
  z-index: 28;
}

.rectangle-13 {
  position: absolute;
  width: 1281px;
  height: 399px;
  top: 863px;
  left: 0;
  background: #27394f;
  z-index: 2;
}

.download-on-app-store-white {
  position: relative;
  width: 278px;
  height: 82px;
  margin: 55px 0 0 501px;
  cursor: pointer;
  background: transparent;
  background: no-repeat 50%;
  background-size: cover;
  border: none;
  z-index: 3;
}

.frame-14 {
  flex-wrap: nowrap;
  gap: 9px;
  width: 414px;
  margin: 35px 0 0 433px;
  z-index: 4;
}

.frame-14,
.newsletter-15 {
  display: flex;
  align-items: flex-start;
  position: relative;
}

.newsletter-15 {
  justify-content: center;
  flex-shrink: 0;
  flex-basis: auto;
  width: 101px;
  height: 34px;
  color: #d9d9d9;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 500;
  line-height: 34px;
  text-align: center;
  white-space: nowrap;
  z-index: 5;
}

.privacy-policy {
  width: 139px;
  height: 34px;
  line-height: 34px;
  z-index: 6;
}

.privacy-policy,
.terms-of-service {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  color: #d9d9d9;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

.terms-of-service {
  width: 156px;
  height: 35px;
  line-height: 35px;
  z-index: 7;
}

.flex-row-ad {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  width: 91.097px;
  height: 37.335px;
  margin: 54px 0 0 595px;
  z-index: 12;
}

.instagram {
  background: url(/c8905553-2214-4dc6-9eba-a14b6926b176.png) no-repeat 50%;
  background-size: cover;
  z-index: 11;
}

.button-linkedin,
.instagram {
  flex-shrink: 0;
  position: relative;
  width: 37.335px;
  height: 37.335px;
  overflow: hidden;
}

.button-linkedin {
  cursor: pointer;
  background: transparent;
  background: no-repeat 50%;
  background-size: cover;
  border: none;
  z-index: 12;
}

.outread {
  display: block;
  position: relative;
  height: 34px;
  margin: 6.322px 0 0 564px;
  color: #d9d9d9;
  font-family: Poppins, var(--default-font-family);
  font-size: 17.920705795288086px;
  font-weight: 500;
  line-height: 34px;
  text-align: left;
  white-space: nowrap;
  z-index: 9;
}
