"use client";
import React from "react";
import "./index.css";

const DarwinLandingPage = () => {
  const handleRedirect = () => {
    window.location.href = "/darwinai";
  };
  return (
    <div className="main-container">
      <div className="rectangle-2">
        <div className="rectangle-3">
          <div className="darwin-ai-logo-white" />
          <button className="frame-4" onClick={handleRedirect}>
            <span className="try-for-free">Try for free</span>
          </button>
          <div className="qa-platform-hero" />
        </div>
        <div className="flex-column-ea">
          <div className="ask-question-answers">
            <span
              className="ask-any-question1"
              style={{
                color: "#88d84c",
                fontSize: "25px",
              }}
            >
              ✓
            </span>
            <span className="ask-any-question">Ask any question</span>
            <span className="and-get"> and get </span>
            <span className="ask-any-question-5">science-backed</span>
            <span className="and-get-6"> answers. </span>
            <span className="ask-any-question-7">No misinformation.</span>
          </div>
          <div className="get-insights">
            <span
              className="ask-any-question1"
              style={{
                color: "#88d84c",
                fontSize: "25px",
              }}
            >
              ✓
            </span>
            <span className="get-insights-8">Get </span>
            <span className="individual-insights">individual insights</span>
            <span className="get-insights-9"> from </span>
            <span className="individual-insights-a">most relevant papers</span>
            <span className="answering-query"> answering your query.</span>
          </div>
          <div className="search-papers">
            <span
              className="ask-any-question1"
              style={{
                color: "#88d84c",
                fontSize: "25px",
              }}
            >
              ✓
            </span>
            <span className="search-papers-b">Search through over </span>
            <span className="research-papers">200M research papers</span>
            <span className="search-papers-c"> across every domain of </span>
            <span className="research-papers-d">science & academia.</span>
          </div>
          <div className="ask-questions">
            <span
              className="ask-any-question1"
              style={{
                color: "#88d84c",
                fontSize: "25px",
              }}
            >
              ✓
            </span>
            <span className="ask-questions-like">Ask questions like</span>
            <span className="gut-microbiome-depression">
              {" "}
              <br />
              * Does gut microbiome affect depression?
              <br />
              * Connection between Magnesium and sleep?
              <br />
              * How can AI accelerate scientific discovery?
              <br />
              <br /> and more...
            </span>
          </div>
          <button className="frame-4" onClick={handleRedirect}>
            <span className="try-for-free-f">Try for free</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default DarwinLandingPage;
