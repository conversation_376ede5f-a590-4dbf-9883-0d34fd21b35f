"use client";
import React, { useState, useEffect, useRef, useCallback } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { income as incomeData } from "./income";
import { code as codeData } from "./code";
import { consciousness } from "./consciousness";
import { childhood } from "./childhood";

type Evidence = {
  quote: string;
  page_number: string;
  context: string;
};

type Section = {
  heading: string;
  content: string;
  evidence?: Evidence[];
  table?: string;
};

const tableStyles = `
  .markdown-table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
  }
  .markdown-table th {
    background-color: black;
    color: white;
    font-weight: bold;
    padding: 0.5rem;
    text-align: center;
  }
  .markdown-table td {
    border: 1px solid #ddd;
    padding: 0.5rem;
    text-align: center;
  }
`;

const AcademicReader = () => {
  const [selectedPdf, setSelectedPdf] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [dataType, setDataType] = useState<
    "income" | "code" | "consciousness" | "childhood"
  >("income");
  const [data, setData] = useState<Section[]>(incomeData);
  const [expandedEvidence, setExpandedEvidence] = useState<Evidence | null>(
    null,
  );
  const pdfViewerRef = useRef<HTMLIFrameElement>(null);
  const [totalEvidenceCount, setTotalEvidenceCount] = useState<number>(0);

  useEffect(() => {
    let newData;
    switch (dataType) {
      case "income":
        newData = incomeData;
        break;
      case "code":
        newData = codeData;
        break;
      case "consciousness":
        newData = consciousness;
        break;
      case "childhood":
        newData = childhood;
        break;
      default:
        newData = incomeData;
    }
    setData(newData);
    const totalCount = newData.reduce(
      (sum, section) => sum + (section.evidence?.length || 0),
      0,
    );
    setTotalEvidenceCount(totalCount);
  }, [dataType]);

  const pdfOptions = [
    {
      name: "Income PDF",
      url: "/papers/income.pdf",
    },
    { name: "Code PDF", url: "/papers/code.pdf" },
    {
      name: "Conciousness PDF",
      url: "/papers/consciousness.pdf",
    },
    { name: "Childhood PDF", url: "/papers/childhood.pdf" },
  ];

  const handlePdfSelect = (url: string) => {
    setSelectedPdf(url);
    setError(null);
    if (url.includes("income")) {
      setDataType("income");
    } else if (url.includes("code")) {
      setDataType("code");
    } else if (url.includes("childhood")) {
      setDataType("childhood");
    } else if (url.includes("consciousness")) {
      setDataType("consciousness");
    }
  };

  const handleBack = () => {
    setSelectedPdf(null);
    setError(null);
  };

  const handleIframeError = () => {
    setError(
      "Failed to load the PDF. Please try again or select a different file.",
    );
  };

  const handleEvidenceClick = useCallback(
    (evidence: Evidence) => {
      setExpandedEvidence((prev) => (prev === evidence ? null : evidence));
      if (pdfViewerRef.current) {
        const pageNumber = parseInt(evidence.page_number);
        const iframe = pdfViewerRef.current;
        iframe.src = `${selectedPdf}#page=${pageNumber}`;
      }
    },
    [selectedPdf],
  );

  return (
    <div className="container mx-auto p-4 min-h-screen flex flex-col">
      <style>{tableStyles}</style>
      <h1 className="text-3xl font-bold mb-6 text-center">Academic Reader</h1>

      {!selectedPdf ? (
        <div className="flex-grow flex flex-col items-center justify-center">
          <p className="mb-6 text-xl">Select a PDF to view:</p>
          <div className="flex space-x-4">
            {pdfOptions.map((pdf, index) => (
              <button
                key={index}
                onClick={() => handlePdfSelect(pdf.url)}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 ease-in-out transform hover:scale-105"
              >
                {pdf.name}
              </button>
            ))}
          </div>
        </div>
      ) : (
        <div className="flex-grow flex flex-col bg-white text-black">
          <button
            onClick={handleBack}
            className="mb-4 bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg self-start transition duration-300 ease-in-out"
          >
            ← Back to Selection
          </button>
          {error ? (
            <div className="text-red-500 text-center">{error}</div>
          ) : (
            <div className="flex flex-grow space-x-4">
              <div
                className="w-1/2 overflow-y-auto border rounded p-4"
                style={{
                  height: "calc(100vh - 200px)",
                }}
              >
                {data.map((section, index) => (
                  <div key={index} className="mb-6">
                    <h2 className="text-2xl font-bold mb-2">
                      {section.heading}
                    </h2>
                    <p className="mb-4 whitespace-pre-wrap">
                      {section.content}
                    </p>
                    {section.evidence && (
                      <div className="relative space-y-2">
                        {section.evidence.map((evidence, evidenceIndex) => {
                          const globalEvidenceIndex =
                            data
                              .slice(0, index)
                              .reduce(
                                (sum, s) => sum + (s.evidence?.length || 0),
                                0,
                              ) + evidenceIndex;
                          return (
                            <div key={evidenceIndex} className="relative group">
                              <span
                                className="inline-block mr-2 mb-2 px-2 py-1 bg-yellow-200 rounded cursor-pointer hover:bg-yellow-300"
                                onClick={() => handleEvidenceClick(evidence)}
                              >
                                [E
                                {globalEvidenceIndex + 1}]
                              </span>
                              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute z-10 p-2 bg-gray-800 text-white rounded shadow-lg max-w-md whitespace-normal">
                                <p>
                                  <strong>Context:</strong> {evidence.context}
                                </p>
                              </div>
                              {expandedEvidence === evidence && (
                                <div className="mt-2 p-2 bg-gray-100 rounded">
                                  <p>
                                    <strong>Quote:</strong> {evidence.quote}
                                  </p>
                                  <p>
                                    <strong>Page:</strong>{" "}
                                    {evidence.page_number}
                                  </p>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                    {section.table && (
                      <div className="mt-4">
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          components={{
                            table: ({ node, ...props }) => (
                              <table className="markdown-table" {...props} />
                            ),
                          }}
                        >
                          {section.table}
                        </ReactMarkdown>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <div className="w-1/2">
                <iframe
                  ref={pdfViewerRef}
                  src={`${selectedPdf}#view=FitH&pagemode=none`}
                  className="w-full h-[calc(100vh-200px)] border-2 border-gray-300 rounded-lg"
                  title="PDF Viewer"
                  onError={handleIframeError}
                />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AcademicReader;
