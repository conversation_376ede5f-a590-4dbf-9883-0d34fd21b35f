import React from "react";
import ArticleGridSkeleton from "@/app/component/ArticleComponents/ArticleGridSkeleton";

export default function Loading() {
  return (
    <div className="w-full bg-[#132435] text-white flex flex-col justify-center items-center">
      <div className="w-[393px] lg:w-[1100px] md:w-[756px] flex justify-center items-start flex-col p-5">
        <h1 className="text-2xl mb-6 my-4 text-left">All Articles</h1>
        <ArticleGridSkeleton columns={5} />
        <div className="mt-8 flex items-center justify-center w-full">
          <div className="h-10 w-64 bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}
