import { getAllArticles } from "../../lib/cache/getAllArticles";
import ArticleGrid from "../component/ArticleComponents/ArticleGrid";
import PaginationControls from "../component/PaginationControls";
import { getUser } from "@/lib/cache/getUser";
import { redirect } from "next/navigation";
import DateFilterComponent from "../component/DateFilterComponent";

interface AllArticlesPageProps {
  searchParams: {
    page?: string;
    startYear?: string;
    startMonth?: string;
    endYear?: string;
    endMonth?: string;
  };
}

export const revalidate = 60; // Revalidate the page every minute

export default async function AllArticlesPage({
  searchParams,
}: AllArticlesPageProps) {
  try {
    const currentPage = Number(searchParams.page) || 1;
    const user = await getUser();

    if (!user) {
      redirect("/signin");
    }

    const startYear = searchParams.startYear
      ? parseInt(searchParams.startYear)
      : null;
    const startMonth = searchParams.startMonth
      ? parseInt(searchParams.startMonth)
      : null;
    const endYear = searchParams.endYear
      ? parseInt(searchParams.endYear)
      : null;
    const endMonth = searchParams.endMonth
      ? parseInt(searchParams.endMonth)
      : null;

    const {
      articles,
      totalPages,
      currentPage: page,
    } = await getAllArticles(
      currentPage,
      25,
      user.id,
      startYear,
      startMonth,
      endYear,
      endMonth,
    );

    return (
      <div className="w-full bg-[#132435] text-white flex flex-col justify-center items-center">
        <div className="lg:w-[1100px] md:w-[756px] w-[393px] flex flex-col justify-center p-5">
          <div className="flex flex-col justify-between items-start mb-6">
            <h1 className="text-2xl my-4 text-start">All Articles</h1>
            <DateFilterComponent
              startYear={startYear}
              startMonth={startMonth}
              endYear={endYear}
              endMonth={endMonth}
            />
          </div>
          <ArticleGrid articles={articles} columns={5} />
          <div className="mt-8 flex items-center justify-center w-full">
            <PaginationControls
              currentPage={page}
              totalPages={totalPages}
              href="/all-articles"
            />
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error in AllArticlesPage:", error);
    throw error;
  }
}
