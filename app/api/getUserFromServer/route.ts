import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function POST(request: Request) {
  try {
    let { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    const user = await prisma.user.findFirst({
      where: {
        email: email,
      },
      select: {
        supabaseUserId: true,
        username: true,
        credit: true,
        id: true,
        role: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const hasPreference = await prisma.preference.findUnique({
      where: { userId: user?.id },
    });

    return NextResponse.json({
      id: user.id,
      userName: user.username,
      superbaseId: user.supabaseUserId,
      email: email,
      hasPreference: hasPreference !== null,
      role: user.role,
      credit: user.credit,
      isPaidUser: user.role !== "USER",
      isAdmin: user.role === "ADMIN",
    });
  } catch (error) {
    console.error("Error checking user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
