import { NextRequest, NextResponse } from 'next/server';
import { streamChatCompletion } from '../../../lib/openai';

export async function POST(req: NextRequest) {
  try {
    const { question, context } = await req.json() as { question: string, context: string };

    const prompt = `Based on the previous answer: "${context}"\nGenerate 4 concise follow-up research questions a researcher might ask next. Return them as a JSON array of strings.`;

    const stream = await streamChatCompletion({
      messages: [
        { role: 'system', content: 'You generate follow-up research questions.' },
        { role: 'user', content: prompt },
      ],
      model: 'gpt-4o-mini',
    });

    let full = '';
    for await (const chunk of stream) {
      full += (chunk as any).choices?.[0]?.delta?.content || '';
    }

    let suggestions: string[] = [];
    try { suggestions = JSON.parse(full.trim()); } catch { suggestions = full.split(/\n|•|-|\d+\. /).filter(Boolean).slice(0,4); }

    return NextResponse.json({ suggestions });
  } catch (err) {
    console.error(err);
    return NextResponse.json({ error: 'Failed' }, { status: 500 });
  }
} 