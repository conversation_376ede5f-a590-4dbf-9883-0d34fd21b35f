import { NextRequest, NextResponse } from 'next/server';
import { streamChatCompletion } from '../../../lib/openai';

export async function POST(req: NextRequest) {
  try {
    const { query } = await req.json() as { query: string };

    const prompt = `The user asked a very short research query: "${query}". Offer 3 disambiguation suggestions phrased as full research questions, return as JSON array of strings.`;

    const stream = await streamChatCompletion({
      messages: [
        { role: 'system', content: 'You clarify ambiguous research queries.' },
        { role: 'user', content: prompt },
      ],
      model: 'gpt-4o-mini',
      temperature: 0.7,
    });

    let full = '';
    for await (const chunk of stream) {
      full += (chunk as any).choices?.[0]?.delta?.content || '';
    }

    let options: string[] = [];
    try { options = JSON.parse(full.trim()); } catch { options = full.split(/\n|•|-|\d+\. /).filter(Boolean).slice(0,3); }

    return NextResponse.json({ options });
  } catch (err) {
    console.error(err);
    return NextResponse.json({ error: 'Failed' }, { status: 500 });
  }
} 