import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);

async function getArticleMetrics() {
  const [topArticles, recentArticles] = await Promise.all([
    prisma.article.findMany({
      take: 10,
      orderBy: { views: "desc" },
      select: { title: true, views: true, slug: true },
    }),
    prisma.article.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      select: { title: true, createdAt: true, views: true, slug: true },
    }),
  ]);
  return { topArticles, recentArticles };
}

async function getPlaylistMetrics() {
  const topPlaylists = await prisma.playlist.findMany({
    take: 5,
    orderBy: { views: "desc" },
    select: { name: true, views: true },
  });
  return { topPlaylists };
}

async function getUserMetrics() {
  const [
    totalUsers,
    totalPaidUsersCount,
    newUsersLast24Hours,
    newPaidUsersLast24Hours,
    recentUsers,
    latestUsers,
    latestPaidUsers,
  ] = await Promise.all([
    prisma.user.count(),
    prisma.user.count({ where: { role: "PAID_USER" } }),
    prisma.user.count({
      where: {
        createdAt: {
          gte: yesterday,
        },
      },
    }),
    prisma.user.count({
      where: {
        role: "PAID_USER",
        createdAt: {
          gte: yesterday,
        },
      },
    }),
    prisma.user.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      select: {
        username: true,
        createdAt: true,
      },
    }),
    prisma.user.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      select: {
        id: true,
        username: true,
        email: true,
        createdAt: true,
        isWebsite: true,
      },
    }),
    prisma.user.findMany({
      where: { role: "PAID_USER" },
      orderBy: { createdAt: "desc" },
      take: 10,
      select: { id: true, username: true, email: true, createdAt: true },
    }),
  ]);
  return {
    totalUsers,
    totalPaidUsers: totalPaidUsersCount,
    newUsersLast24Hours,
    newPaidUsersLast24Hours,
    recentUsers,
    latestUsers,
    latestPaidUsers,
  };
}

async function getNewsletterMetrics() {
  const [
    totalNewsletterUsers,
    newNewsletterUsersLast24Hours,
    latestNewsletterSubscribers,
  ] = await Promise.all([
    prisma.newsletterSubscriber.count(),
    prisma.newsletterSubscriber.count({
      where: {
        createdAt: {
          gte: yesterday,
        },
      },
    }),
    prisma.newsletterSubscriber.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      select: { email: true, createdAt: true },
    }),
  ]);
  return {
    totalNewsletterUsers,
    newNewsletterUsersLast24Hours,
    latestNewsletterSubscribers,
  };
}

async function getSearchMetrics() {
  const [recentSearches, recentSearchesLast24Hours, totalSearches] =
    await Promise.all([
      prisma.search.findMany({
        where: {
          userId: { not: null },
        },
        take: 10,
        orderBy: { createdAt: "desc" },
        select: {
          query: true,
          userId: true,
          createdAt: true,
        },
      }),
      prisma.search.count({
        where: {
          createdAt: {
            gte: yesterday,
          },
        },
      }),
      prisma.search.count(),
    ]);

  const users = await prisma.user.findMany({
    where: {
      id: {
        in: recentSearches.map((search) => search.userId as string),
      },
    },
    select: {
      id: true,
      username: true,
      email: true,
    },
  });

  const recentSearchesWithUsername = recentSearches.map((search) => {
    const user = users.find((user) => user.id === search.userId);
    return {
      ...search,
      user,
    };
  });

  return {
    recentSearches: recentSearchesWithUsername,
    recentSearchesLast24Hours,
    totalSearches,
  };
}

async function getTrendAnalysisMetrics() {
  const [
    recentTrendAnalyses,
    recentTrendAnalysesLast24Hours,
    totalTrendAnalyses,
    topTrendAnalysisUsers,
  ] = await Promise.all([
    prisma.trendAnalysisResponse.findMany({
      take: 10,
      orderBy: { createdAt: "desc" },
      select: {
        query: true,
        userId: true,
        response: true,
        createdAt: true,
        user: { select: { email: true, username: true } },
      },
    }),
    prisma.trendAnalysisResponse.count({
      where: {
        createdAt: {
          gte: yesterday,
        },
      },
    }),
    prisma.trendAnalysisResponse.count(),
    prisma.user.findMany({
      take: 10,
      orderBy: [
        {
          trendAnalysisResponses: {
            _count: "desc",
          },
        },
      ],
      select: {
        username: true,
        email: true,
        id: true,
        trendAnalysisResponses: {
          take: 10,
          orderBy: { createdAt: "desc" },
          select: {
            query: true,
            createdAt: true,
          },
        },
      },
    }),
  ]);

  const topTrendAnalysisUsersWithEmail = topTrendAnalysisUsers.map((user) => ({
    ...user,
    username: user.username || user.email,
  }));

  return {
    recentTrendAnalyses,
    recentTrendAnalysesLast24Hours,
    topTrendAnalysisUsers: topTrendAnalysisUsersWithEmail,
    totalTrendAnalyses,
  };
}

export async function GET() {
  try {
    const [
      articleMetrics,
      playlistMetrics,
      userMetrics,
      newsletterMetrics,
      searchMetrics,
      trendAnalysisMetrics,
    ] = await Promise.all([
      getArticleMetrics(),
      getPlaylistMetrics(),
      getUserMetrics(),
      getNewsletterMetrics(),
      getSearchMetrics(),
      getTrendAnalysisMetrics(),
    ]);

    return NextResponse.json({
      topArticles: articleMetrics.topArticles,
      recentArticles: articleMetrics.recentArticles,
      topPlaylists: playlistMetrics.topPlaylists,
      totalUsers: userMetrics.totalUsers,
      totalPaidUsers: userMetrics.totalPaidUsers,
      newUsersLast24Hours: userMetrics.newUsersLast24Hours,
      newPaidUsersLast24Hours: userMetrics.newPaidUsersLast24Hours,
      recentUsers: userMetrics.recentUsers,
      latestUsers: userMetrics.latestUsers,
      latestPaidUsers: userMetrics.latestPaidUsers,
      totalNewsletterUsers: newsletterMetrics.totalNewsletterUsers,
      newNewsletterUsersLast24Hours:
        newsletterMetrics.newNewsletterUsersLast24Hours,
      latestNewsletterSubscribers:
        newsletterMetrics.latestNewsletterSubscribers,
      recentSearches: searchMetrics.recentSearches,
      recentSearchesLast24Hours: searchMetrics.recentSearchesLast24Hours,
      totalSearches: searchMetrics.totalSearches,
      recentTrendAnalyses: trendAnalysisMetrics.recentTrendAnalyses,
      totalTrendAnalyses: trendAnalysisMetrics.totalTrendAnalyses,
      recentTrendAnalysesLast24Hours:
        trendAnalysisMetrics.recentTrendAnalysesLast24Hours,
      topTrendAnalysisUsers: trendAnalysisMetrics.topTrendAnalysisUsers,
    });
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
