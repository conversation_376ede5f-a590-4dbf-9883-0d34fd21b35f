import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { Pinecone } from "@pinecone-database/pinecone";
import { getUser } from "@/lib/cache/getUser";

const prisma = new PrismaClient();

interface Payload {
  query: string;
  userId: string | undefined;
  type: "semantic" | "doi" | "authorName" | "originalPaperTitle";
}

async function vectorise(query: string) {
  const url = "https://api.openai.com/v1/embeddings";
  const headers = {
    "Content-Type": "application/json",
    Authorization: "Bearer " + process.env.OPENAI_API_KEY,
  };
  const data = {
    input: query,
    model: "text-embedding-ada-002",
  };

  const response = await fetch(url, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`HTTP error ${response.status}`);
  }

  const result = await response.json();
  console.log(result);
  const queryVector = result.data[0].embedding;
  return queryVector;
}

export async function POST(request: NextRequest) {
  try {
    const body: Payload = await request.json();
    const { query, userId } = body;
    let { type } = body;
    if (!type) {
      type = "semantic";
    }
    const user = await getUser();

    if (!query) {
      return NextResponse.json({ error: "No query provided" }, { status: 400 });
    }

    let fullArticles;

    if (type === "semantic") {
      const pc = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY!,
      });

      const pineconeIndex = pc.Index("research-articles");

      const vectorisedQuery = await vectorise(query);

      const topKMatches = await pineconeIndex.query({
        topK: 20,
        vector: vectorisedQuery,
        includeMetadata: true,
      });

      const articleIds = topKMatches.matches.map((match) => match.id);

      fullArticles = await prisma.article.findMany({
        where: {
          id: {
            in: articleIds,
          },
        },
        include: {
          categories: {
            select: {
              name: true,
            },
          },
          articleImage: {
            select: {
              src: true,
            },
          },
        },
      });

      fullArticles = fullArticles
        .map((article) => {
          const matchScore =
            topKMatches.matches.find((match) => match.id === article.id)
              ?.score || 0;
          return { ...article, relevance_score: matchScore };
        })
        .sort((a, b) => b.relevance_score - a.relevance_score);
    } else {
      fullArticles = await prisma.article.findMany({
        where: {
          [type]: {
            contains: query,
            mode: "insensitive",
          },
        },
        include: {
          categories: {
            select: {
              name: true,
            },
          },
          articleImage: {
            select: {
              src: true,
            },
          },
        },
        take: 20,
      });
    }

    const relevantArticles = fullArticles.map((article: any) => ({
      id: article.id,
      slug: article.slug,
      title: article.title,
      subtitle: article.subtitle,
      imageId: article.imageId,
      coverImage: article.articleImage?.src,
      authorName: article.authorName,
      doi: article.doi,
      altMetricScore: article.altMetricScore,
      estimatedReadingTime: article.estimatedReadingTime,
      categories: article.categories,
      simpleSummary: article.simpleSummary,
      relevance_score: article.relevance_score || 1,
      originalPaperTitle: article.originalPaperTitle || article.title,
    }));

    if (user || userId) {
      try {
        const result = await prisma.search.create({
          data: {
            query,
            userId: user ? user.id : userId,
          },
        });
        console.log(result);
      } catch (error) {
        console.error("Error in search API:", error);
      }
    }

    return NextResponse.json({ results: relevantArticles }, { status: 200 });
  } catch (error) {
    console.error("Error in search API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
