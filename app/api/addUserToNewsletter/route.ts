import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    await prisma.newsletterSubscriber.upsert({
      where: {
        email: email,
      },
      create: {
        email: email,
      },
      update: {
        email: email,
      },
    });
    return NextResponse.json({
      message: `Added ${email} to newsletter`,
    });
  } catch (error) {
    console.error("Error adding to newsletter");
    return NextResponse.json(
      { error: "Failed to add to newsletter" },
      { status: 500 },
    );
  }
}
