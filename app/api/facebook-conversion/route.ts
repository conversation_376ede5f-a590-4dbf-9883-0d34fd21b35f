import { NextRequest, NextResponse } from "next/server";
import {
  handleViewContent,
  handleSearch,
  handlePurchase,
  handleInitiateCheckout,
} from "@/lib/FacebookConversionAPI";

export async function POST(request: NextRequest) {
  const body = await request.json();

  const { eventName, eventData } = body;

  const userAgent = request.headers.get("user-agent") || "";
  const userIp =
    request.headers.get("x-forwarded-for") ||
    request.headers.get("x-real-ip") ||
    "";
  eventData.userAgent = userAgent;
  eventData.userIp = userIp;
  const userData = { userAgent, userIp };

  try {
    switch (eventName) {
      case "ViewContent":
        handleViewContent(eventData.pageLink, userData, eventData.eventSource);
        break;
      case "Search":
        handleSearch(eventData.searchQuery, userData, eventData.eventSource);
        break;
      case "Purchase":
        handlePurchase(
          eventData.value,
          eventData.currency,
          userData,
          eventData.eventSource,
        );
        break;
      case "InitiateCheckout":
        await handleInitiateCheckout(
          eventData.plan,
          eventData.email,
          userData,
          eventData.eventSource,
        );
        break;
      default:
        throw new Error(`Unsupported event type: ${eventName}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error sending Facebook Conversion event:", error);
    return NextResponse.json(
      { success: false, error: "Failed to send event" },
      { status: 500 },
    );
  }
}
