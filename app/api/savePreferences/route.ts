import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getUser } from "@/lib/cache/getUser";

async function searchArticles(query: string) {
  const baseUrl =
    process.env.NODE_ENV === "production"
      ? process.env.NEXT_PUBLIC_APP_URL
      : "http://localhost:3000";

  const response = await fetch(`${baseUrl}/api/search`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ query, type: "semantic" }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error ${response.status}`);
  }

  const data = await response.json();
  return data.results.map((article: any) => article.id);
}

export async function POST(req: Request) {
  try {
    const user = await getUser();
    if (!user) {
      return NextResponse.json(
        { error: "User not authenticated" },
        { status: 401 },
      );
    }

    const { workPlace, role, useCase, heardFrom, categories } =
      await req.json();

    const groups = await Promise.all(
      categories
        .slice(0, 7)
        .map((category: string) => searchArticles(category)),
    );

    console.log(groups);

    const preference = await prisma.preference.upsert({
      where: { userId: user.id },
      update: {
        preference: JSON.stringify({ workPlace, role, useCase, heardFrom }),
        onboardingCategories: categories.join(","),
        onboardingAnswers: JSON.stringify({
          workPlace,
          role,
          useCase,
          heardFrom,
          categories,
        }),
        group1: groups[0] || [],
        group2: groups[1] || [],
        group3: groups[2] || [],
        group4: groups[3] || [],
        group5: groups[4] || [],
        group6: groups[5] || [],
        group7: groups[6] || [],
      },
      create: {
        userId: user.id,
        preference: JSON.stringify({ workPlace, role, useCase, heardFrom }),
        onboardingCategories: categories.join(","),
        onboardingAnswers: JSON.stringify({
          workPlace,
          role,
          useCase,
          heardFrom,
          categories,
        }),
        group1: groups[0] || [],
        group2: groups[1] || [],
        group3: groups[2] || [],
        group4: groups[3] || [],
        group5: groups[4] || [],
        group6: groups[5] || [],
        group7: groups[6] || [],
      },
    });

    return NextResponse.json({ success: true, preference });
  } catch (error) {
    console.error("Error saving preferences:", error);
    return NextResponse.json(
      { error: "Failed to save preferences" },
      { status: 500 },
    );
  }
}
