import { NextRequest, NextResponse } from 'next/server';
import { FeedbackRequest } from '../../../types/darwin';

export async function POST(request: NextRequest) {
  try {
    const { messageId, rating, sessionId }: FeedbackRequest = await request.json();

    if (!messageId || !rating || rating < 1 || rating > 5) {
      return NextResponse.json({ error: 'Invalid feedback data' }, { status: 400 });
    }

    // In production, this would save to a database
    console.log('Feedback received:', { messageId, rating, sessionId });

    return NextResponse.json({ 
      success: true, 
      message: 'Feedback recorded successfully' 
    });

  } catch (error) {
    console.error('Feedback API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 