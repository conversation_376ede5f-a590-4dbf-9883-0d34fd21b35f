import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2024-06-20",
});
const endpoint =
  process.env.DEV_MODE === "true"
    ? process.env.LOCALHOST
    : "https://outread.ai";

export async function POST(req: Request) {
  if (req.method === "POST") {
    try {
      const { priceId, trialDays } = await req.json();

      if (!priceId) {
        return NextResponse.json(
          {
            error: {
              message: "Price ID is required",
            },
          },
          { status: 400 },
        );
      }

      // Validate trialDays
      const parsedTrialDays = parseInt(trialDays, 10);
      if (isNaN(parsedTrialDays) || parsedTrialDays < 0) {
        return NextResponse.json(
          {
            error: {
              message: "Invalid trial days",
            },
          },
          { status: 400 },
        );
      }

      const session = await stripe.checkout.sessions.create({
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: "subscription",
        success_url: `${endpoint}/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${endpoint}/subscription`,
        subscription_data: {
          trial_period_days: parsedTrialDays,
        },
        allow_promotion_codes: true,
      });

      return NextResponse.json({
        sessionId: session.id,
      });
    } catch (err: any) {
      console.error("Error creating checkout session:", err);
      return NextResponse.json(
        { error: { message: err.message } },
        { status: 500 },
      );
    }
  } else {
    return NextResponse.json(
      {
        error: { message: "Method Not Allowed" },
      },
      { status: 405 },
    );
  }
}
