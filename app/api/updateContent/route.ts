// File: app/api/createArticle/route.js

import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, Prisma } from "@prisma/client";
// File: app/api/upload/route.js
import { createClient } from "@supabase/supabase-js";

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL as string,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
);

const prisma = new PrismaClient();

interface Payload {
  doi: string;
  defaultSummary: string;
}

export async function POST(request: NextRequest) {
  try {
    // Parse the JSON body from the request
    const body: Payload = await request.json();
    const { doi, defaultSummary } = body;

    const parsedSummary = JSON.parse(defaultSummary);

    try {
      const article = await prisma.article.update({
        where: { doi: doi },
        data: {
          defaultSummary: parsedSummary,
        },
      });

      if (!article) {
        return NextResponse.json(
          { error: "Article not found" },
          { status: 404 },
        );
      }
      return NextResponse.json({ id: article.id }, { status: 201 });
    } catch (error) {
      console.log(error);
      return NextResponse.json(
        { error: "Internal Server Error" },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error creating article:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  } finally {
    await prisma.$disconnect();
  }
}
