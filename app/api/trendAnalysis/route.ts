import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import {
  BedrockRuntimeClient,
  ConversationRole,
  ConverseCommand,
} from "@aws-sdk/client-bedrock-runtime";
import { createClient } from "@/lib/supabase/server";
import { searchSemanticScholar } from "./searchSemanticScholar";
import { semanticSearch } from "./pinecone";

const prisma = new PrismaClient();
const bedrockClient = new BedrockRuntimeClient({
  region: process.env.AWS_REGION || "us-west-2",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS!,
    secretAccessKey: process.env.AWS_SECRET!,
  },
});

interface Insight {
  type: "outread" | "semantic_scholar";
  paper_id: string;
  insight: string;
  keyword: string;
  insight_explanation: string;
  doi: string;
  title?: string;
  authors?: string[];
  year?: number;
  citationCount?: number;
  url?: string;
  verification?: string;
}

interface Payload {
  query: string;
  complexity?: string;
  userId: string;
  filters?: {
    minYear?: number;
    minCitations?: number;
    minRelevance?: number;
  };
}

function sanitizeJson(response: string): string | null {
  try {
    const start = response.indexOf("{");
    const end = response.lastIndexOf("}") + 1;
    const jsonString = response.slice(start, end);
    return jsonString;
  } catch (error) {
    console.log(response);
    return null;
  }
}

// Cache for frequently searched queries
const queryCache = new Map<string, any>();

async function generateInsightForPaper(
  paper: any,
  query: string,
): Promise<any> {
  const prompt = `
You are given one research paper and a query: "${query}". The paper is provided as JSON with a "paper_id" and metadata.

Generate exactly one insight in strict JSON format according to the schema below. Do not include any text outside of the JSON.

**Schema:**
{
  "type": "semantic_scholar",
  "paper_id": "string",
  "insight": "string",
  "keyword": "string",
  "insight_explanation": "string",
  "doi": "string"
}

**Field Requirements:**
- "type": Must always be "semantic_scholar".
- "paper_id": Must match exactly the "paper_id" from the input data without changes.
- "insight": A single-sentence statement capturing a key aspect of the paper relevant to the query. Focus on the paper's contribution to the field and its relevance to the query.
- "keyword": A single keyword related to the paper's main finding/concept.
- "insight_explanation": 1-2 sentences explaining how the insight was derived from the paper's abstract. Include specific details from the abstract.
- "doi": Must match the provided paper's DOI exactly.

No extra commentary, code fences, or formatting. Only return the JSON.

Here is the paper:
${JSON.stringify(paper, null, 2)}
`;

  const modelId = "anthropic.claude-3-5-sonnet-20240620-v1:0";
  const conversation = [
    {
      role: "user" as ConversationRole,
      content: [{ text: prompt }],
    },
  ];
  const command = new ConverseCommand({
    modelId,
    messages: conversation,
    inferenceConfig: {
      maxTokens: 4096,
      temperature: 0.5,
      topP: 0.9,
    },
  });

  let response = null;
  try {
    response = await bedrockClient.send(command);
  } catch (error) {
    console.log("Error with Claude (insight generation):", error);
    return null;
  }

  let responseText: string | undefined;
  if (response.output?.message?.content?.[0]?.text) {
    responseText = response.output.message.content[0].text;
  } else {
    console.error("Unexpected response structure for insight:", response);
    return null;
  }

  const json = sanitizeJson(responseText);
  if (!json) {
    console.log("Failed to parse insight response JSON");
    return null;
  }

  try {
    return JSON.parse(json);
  } catch (error) {
    console.log("Failed to parse JSON for insight:", error);
    return null;
  }
}

async function generatePaperSummary(
  paper: any,
  query: string,
): Promise<string> {
  const prompt = `
Summarize the following research paper in 2-3 sentences, focusing on its relevance to the query: "${query}".

Paper:
${JSON.stringify(paper, null, 2)}
`;

  const modelId = "anthropic.claude-3-5-sonnet-20240620-v1:0";
  const conversation = [
    {
      role: "user" as ConversationRole,
      content: [{ text: prompt }],
    },
  ];
  const command = new ConverseCommand({
    modelId,
    messages: conversation,
    inferenceConfig: {
      maxTokens: 4096,
      temperature: 0.5,
      topP: 0.9,
    },
  });

  const response = await bedrockClient.send(command);
  return response.output?.message?.content?.[0]?.text || "";
}

async function generateSummarisedResponse(
  insights: any[],
  query: string,
): Promise<string | null> {
  const schemaReminder = `
Return only the following strict JSON format with the updated "summarised_response" field. Nothing else.

{
  "summarised_response": "string"
}
`;

  const insightsJson = JSON.stringify(insights, null, 2);

  const prompt = `
You are given a set of insights from multiple papers and the original query: "${query}".

The insights are provided as a JSON array. Each element has "paper_id" and a brief "insight". Integrate these insights into a coherent single narrative. Reference each paper by its paper_id using <reference>paper_id</reference> tags. Do not repeat all details, just weave them into a summarised_response. No extra commentary, code fences, or formatting.

Below are the previously generated insights:
${insightsJson}

Follow the schema exactly and return only the JSON. No extra text.

${schemaReminder}
`;

  const modelId = "anthropic.claude-3-5-sonnet-20240620-v1:0";
  const conversation = [
    {
      role: "user" as ConversationRole,
      content: [{ text: prompt }],
    },
  ];

  const command = new ConverseCommand({
    modelId,
    messages: conversation,
    inferenceConfig: {
      maxTokens: 4096,
      temperature: 0.5,
      topP: 0.9,
    },
  });

  let response = null;
  try {
    response = await bedrockClient.send(command);
  } catch (error) {
    console.log("Error with Claude (summarised response):", error);
    return null;
  }

  let responseText: string | undefined;
  if (response.output?.message?.content?.[0]?.text) {
    responseText = response.output.message.content[0].text;
  } else {
    console.error("Unexpected response structure:", response);
    return null;
  }

  const json = sanitizeJson(responseText);
  if (!json) {
    console.log("Failed to parse summarised_response JSON");
    return null;
  }

  try {
    const parsed = JSON.parse(json);
    return parsed.summarised_response;
  } catch (error) {
    console.log("Failed to parse summarised_response JSON:", error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  const supabase = createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) throw new Error("Unauthorized session");

  const prismaUser = await prisma.user.findUnique({
    where: {
      email: user.email,
    },
  });

  if (!prismaUser) {
    throw new Error("Unauthorized prisma");
  }

  const userId = prismaUser.id;

  try {
    const body: Payload = await request.json();
    const { query, complexity = "simple", filters = {} } = body;

    console.log("DEBUG - Query:", query);
    console.log("DEBUG - Filters:", JSON.stringify(filters));

    if (!query) {
      return NextResponse.json({ error: "No query provided" }, { status: 400 });
    }
    // Check if user is paid or has sufficient credits
    if (prismaUser.role === "USER" && prismaUser.credit <= 0) {
      return NextResponse.json(
        { error: "INSUFFICIENT_CREDIT" },
        { status: 403 },
      );
    }

    // Check cache for existing results
    if (queryCache.has(query)) {
      return NextResponse.json(
        { result: queryCache.get(query) },
        { status: 200 },
      );
    }

    // Deduct one credit only for non-paid users
    if (prismaUser.role !== "PAID_USER") {
      await prisma.user.update({
        where: { id: userId },
        data: { credit: { decrement: 1 } },
      });
    }

    // No Outread articles considered
    const fullArticles: any[] = [];
    const dois = fullArticles.map((article) => article.doi);

    const papers = await searchSemanticScholar(query, dois, bedrockClient);
    console.log("DEBUG - Number of papers returned:", papers.length);
    console.log(
      "DEBUG - First few relevance scores:",
      papers.slice(0, 3).map((p) => p.relevance_score),
    );

    const relevantPapers = papers.map((paper, index) => {
      return {
        paper_id: `${index}`,
        title: paper.title,
        authors: paper.authors,
        doi: paper.doi,
        relevance_score:
          parseFloat((Number(paper.relevance_score) * 100).toFixed(2)) / 100,
        one_card_summary: paper.abstract,
        type: "semantic_scholar",
        citationCount: paper.citationCount,
        year: paper.year,
        abstract: paper.abstract,
        venue: paper.venue,
        influentialCitations: paper.influentialCitations,
      };
    });

    const filteredPapers = relevantPapers.filter((paper) => {
      const yearCondition = filters.minYear
        ? (paper.year || 0) >= filters.minYear
        : true;
      const citationCondition = filters.minCitations
        ? (paper.citationCount || 0) >= filters.minCitations
        : true;
      const relevanceCondition = filters.minRelevance
        ? (paper.relevance_score || 0) >= filters.minRelevance / 100
        : true;
      return yearCondition && citationCondition && relevanceCondition;
    });

    console.log("DEBUG - Final filtered papers count:", filteredPapers.length);
    if (filteredPapers.length > 0) {
      console.log(
        "DEBUG - Sample filtered paper scores:",
        filteredPapers.slice(0, 3).map((p) => p.relevance_score),
      );
    }

    const papersJson = JSON.stringify(
      relevantPapers.map((p) => ({
        paper_id: p.paper_id,
        title: p.title,
        authors: p.authors,
        doi: p.doi,
        citationCount: p.citationCount,
        year: p.year,
        abstract: p.abstract,
        relevance_score: p.relevance_score,
      })),
      null,
      2,
    );

    // Generate insights for each paper asynchronously
    const insightPromises = filteredPapers.map((paper) =>
      generateInsightForPaper(paper, query),
    );
    const insights = (await Promise.all(insightPromises)).filter(Boolean);

    // Once we have the insights, generate the summarised_response
    const summarised_response = await generateSummarisedResponse(
      insights,
      query,
    );
    if (!summarised_response) {
      return NextResponse.json(
        {
          error: "Failed to generate summarised response",
        },
        { status: 500 },
      );
    }

    // Construct final JSON as per original schema
    const result = {
      summary: {
        insight: insights,
        summarised_response,
      },
      relevant_papers: filteredPapers,
    };

    // Cache the result
    queryCache.set(query, result);

    try {
      await prisma.trendAnalysisResponse.create({
        data: {
          userId: userId,
          query,
          complexity,
          response: result,
        },
      });
    } catch (dbError) {
      console.error("Error saving query to database:", dbError);
    }

    console.log("DEBUG - Final response insights count:", insights.length);
    if (filteredPapers.length > 0) {
      console.log(
        "DEBUG - Final response papers preview:",
        filteredPapers.slice(0, 2).map((p) => ({
          title: p.title,
          relevance_score: p.relevance_score,
          year: p.year,
        })),
      );
    }

    return NextResponse.json({ result }, { status: 200 });
  } catch (error) {
    console.log({ error });
    return NextResponse.json({ error: error }, { status: 500 });
  }
}
