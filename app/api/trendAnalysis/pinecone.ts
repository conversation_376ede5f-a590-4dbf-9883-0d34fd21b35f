import { Pinecone } from '@pinecone-database/pinecone';
import axios from 'axios';

// Initialize Pinecone with new SDK
export async function pineconeInit() {
  try {
    const pinecone = new Pinecone({ 
      apiKey: process.env.PINECONE_API_KEY! 
    });
    
    const indexName = process.env.PINECONE_INDEX || 'semantic-scholar-v2';
    
    if (!indexName) {
      throw new Error('Pinecone index name is not defined in environment variables');
    }
    
    console.log(`Connecting to Pinecone index: ${indexName}`);
    return pinecone.Index(indexName);
  } catch (error) {
    console.error('Failed to initialize Pinecone:', error);
    throw error;
  }
}

// New function to generate embeddings from OpenAI
export async function generateEmbedding(query: string) {
  try {
    const embedRes = await axios.post(
      'https://api.openai.com/v1/embeddings',
      {
        input: query,
        model: 'text-embedding-3-small',
        dimensions: 512,
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    
    return embedRes.data.data[0].embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw new Error('Failed to generate embedding');
  }
}

// New function to fetch paper data from Semantic Scholar with better rate limit handling
async function fetchSemanticScholarData(corpusId: string, retries = 5) {
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
  
  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      const semanticScholarUrl = `https://api.semanticscholar.org/graph/v1/paper/CorpusId:${corpusId}?fields=title,abstract,citationCount,authors,externalIds,year,influentialCitationCount,publicationVenue`;
      
      const response = await axios.get(semanticScholarUrl, {
        headers: {
          'x-api-key': process.env.SEMANTIC_SCHOLAR_API_KEY
        }
      });
      
      return response.data;
    } catch (error: any) {
      const isRateLimitError = error.response?.status === 429;
      
      if (isRateLimitError && attempt < retries - 1) {
        // More aggressive exponential backoff: 3s, 6s, 12s, 24s, 48s
        const backoffTime = 3000 * Math.pow(2, attempt);
        console.log(`Rate limited, retrying in ${backoffTime}ms...`);
        await delay(backoffTime);
        continue;
      }
      
      console.error(`Error fetching data for corpus ID ${corpusId}:`, error);
      return null;
    }
  }
  
  return null;
}

// New semantic search function that includes both Pinecone and Semantic Scholar
export async function semanticSearch(query: string, topK = 30) {
  try {
    console.log("DEBUG - Pinecone search for query:", query);
    
    // Step 1: Generate embedding from query
    const embedding = await generateEmbedding(query);
    console.log("DEBUG - Generated embedding vector length:", embedding.length);
    
    // Step 2: Query Pinecone
    const index = await pineconeInit();
    const results = await index.query({
      vector: embedding,
      topK: topK,
      includeMetadata: true,
    });
    
    const matches = results.matches || [];
    console.log("DEBUG - Pinecone returned matches:", matches.length);
    if (matches.length > 0) {
      console.log("DEBUG - Sample raw scores:", matches.slice(0, 3).map(m => m.score));
    }
    
    // NEW: Process in smaller batches with longer delays
    const BATCH_SIZE = 2; // Reduce to just 2 papers at a time
    const papers = [];
    
    for (let i = 0; i < matches.length; i += BATCH_SIZE) {
      const batch = matches.slice(i, i + BATCH_SIZE);
      
      const batchResults = await Promise.all(
        batch.map(async (match, batchIndex) => {
          const index = i + batchIndex;
          const metadata = match.metadata || {};
          const corpusId = metadata.title?.toString();
          
          // Create basic paper with correct property structure
          const paper = {
            paper_id: `${index}`,
            abstract: metadata.abstract || '',
            relevance_score: match.score !== undefined ? parseFloat((match.score * 100).toFixed(2)) / 100 : 0,
            doi: '',
            title: 'Unknown Title',
            authors: [],
            year: null,
            citationCount: 0,
            venue: '',
            influentialCitations: [],
            one_card_summary: metadata.abstract || '',
            type: "semantic_scholar"
          };
          
          if (corpusId && !isNaN(Number(corpusId))) {
            try {
              const scholarData = await fetchSemanticScholarData(corpusId);
              
              if (scholarData) {
                return {
                  ...paper,
                  title: scholarData.title || 'Unknown Title',
                  year: scholarData.year,
                  authors: scholarData.authors || [],
                  citationCount: scholarData.citationCount || 0,
                  doi: scholarData.externalIds?.DOI || '',
                  venue: scholarData.publicationVenue?.name || scholarData.venue?.name || '',
                  influentialCitations: scholarData.influentialCitationCount || 0,
                  abstract: scholarData.abstract || metadata.abstract || '',
                  one_card_summary: scholarData.abstract || metadata.abstract || '',
                  relevance_score: match.score !== undefined ? parseFloat((match.score * 100).toFixed(2)) / 100 : 0
                };
              }
            } catch (error) {
              console.error(`Error enriching paper ${corpusId}:`, error);
              // Return base paper if enrichment fails
            }
          }
          
          return paper;
        })
      );
      
      papers.push(...batchResults);
      
      // Wait longer between batches to avoid rate limiting
      if (i + BATCH_SIZE < matches.length) {
        // Minimum 3 second delay between batches
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }
    
    return papers;
  } catch (error) {
    console.error('Error in semantic search:', error);
    throw new Error('Semantic search failed');
  }
}

export function calculateCosineSimilarity(
  vectorA: number[],
  vectorB: number[],
): number {
  const dotProduct = vectorA.reduce((acc, val, i) => acc + val * vectorB[i], 0);
  const magnitudeA = Math.sqrt(
    vectorA.reduce((acc, val) => acc + val * val, 0),
  );
  const magnitudeB = Math.sqrt(
    vectorB.reduce((acc, val) => acc + val * val, 0),
  );
  return dotProduct / (magnitudeA * magnitudeB);
}
