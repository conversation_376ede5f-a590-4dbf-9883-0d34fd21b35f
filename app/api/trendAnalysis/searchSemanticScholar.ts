import {
  BedrockRuntimeClient,
  ConversationRole,
  ConverseCommand,
} from "@aws-sdk/client-bedrock-runtime";
import {
  generateEmbedding as vectorise,
  calculateCosineSimilarity,
} from "./pinecone";

async function generateSearchQueries(
  question: string,
  bedrockClient: BedrockRuntimeClient,
): Promise<string[]> {
  const prompt = `Analyze this research question to identify core concepts and generate 5 optimized search queries for academic databases:
  
  Original question: "${question}"

  Return JSON format:
  {
    "exact": ["...", "..."],
    "conceptual": ["...", "..."],
    "interdisciplinary": ["..."]
  }`;

  const conversation = [
    {
      role: "user" as ConversationRole,
      content: [{ text: prompt }],
    },
  ];

  const command = new ConverseCommand({
    modelId: "anthropic.claude-3-5-sonnet-20240620-v1:0",
    messages: conversation,
    inferenceConfig: {
      maxTokens: 400,
      temperature: 0.5,
      topP: 0.9,
    },
  });

  try {
    const response = await bedrockClient.send(command);
    const responseText = response.output?.message?.content?.[0]?.text || "";
    const jsonMatch = responseText.match(/\{[\s\S]*?\}/);
    if (!jsonMatch) return [question];

    const queries = JSON.parse(jsonMatch[0]);
    return [
      ...queries.exact,
      ...queries.conceptual,
      ...queries.interdisciplinary,
      question,
    ]
      .filter((q, i, a) => a.indexOf(q) === i)
      .slice(0, 6);
  } catch (error) {
    console.error("Error generating queries:", error);
    return [question];
  }
}

function calculateRelevanceScore(
  cosineSim: number,
  keywordScore: number,
  paper: any,
): number {
  const citationWeight = Math.log10(paper.citationCount + 1) * 0.2;
  const recencyWeight = new Date().getFullYear() - paper.year < 5 ? 0.15 : 0;
  const abstractQuality = paper.abstract.length > 500 ? 0.1 : 0;

  return Math.min(
    1,
    cosineSim * 0.5 +
      keywordScore * 0.2 +
      citationWeight +
      recencyWeight +
      abstractQuality,
  );
}

export async function searchSemanticScholar(
  question: string,
  dois: string[],
  bedrockClient: BedrockRuntimeClient,
) {
  const headers = {
    "x-api-key": process.env.SEMANTIC_SCHOLAR_API_KEY as string,
  };

  const searchQueries = await generateSearchQueries(question, bedrockClient);
  const papersMap = new Map();
  const questionEmbedding = await vectorise(question);

  for (const query of searchQueries) {
    const searchQuery = encodeURIComponent(query);
    const url = `https://api.semanticscholar.org/graph/v1/paper/search?query=${searchQuery}&sort=influentialCitationCount:desc&fields=title,abstract,citationCount,authors,externalIds,year,influentialCitationCount,publicationVenue&limit=100`;

    try {
      const response = await fetch(url, { headers });
      const json = await response.json();

      await Promise.all(
        json.data.map(async (paper: any) => {
          try {
            if (!paper.abstract || !paper.title || paper.year < 1900)
              return null;
            const doi = paper.externalIds?.DOI;
            if (!doi || dois.includes(doi)) return null;

            const paperContent = `${paper.title} ${paper.abstract}`;
            const paperEmbedding = await vectorise(paperContent);
            const cosineSim = calculateCosineSimilarity(
              questionEmbedding,
              paperEmbedding,
            );
            const keywordScore = calculateKeywordDensity(
              paperContent,
              question,
            );

            const relevance = calculateRelevanceScore(cosineSim, keywordScore, {
              citationCount: paper.citationCount,
              year: paper.year,
              abstract: paper.abstract,
            });

            if (relevance < 0.82) return null;

            const paperData = {
              abstract: paper.abstract,
              title: paper.title,
              citationCount: paper.citationCount || 0,
              doi: doi,
              authors: paper.authors?.map((a: any) => a.name) || [],
              year: paper.year,
              venue: paper.publicationVenue?.name,
              influentialCitations: paper.influentialCitationCount || 0,
              relevance_score: relevance,
            };

            const existing = papersMap.get(doi);
            if (!existing || relevance > existing.relevance_score) {
              papersMap.set(doi, paperData);
            }
          } catch (error) {
            console.error(`Error processing paper: ${error}`);
          }
        }),
      );
    } catch (error) {
      console.error(`Search query failed: ${query}`, error);
    }
  }

  return Array.from(papersMap.values())
    .sort((a, b) => {
      if (b.relevance_score - a.relevance_score > 0.05)
        return b.relevance_score - a.relevance_score;
      if (b.influentialCitations - a.influentialCitations !== 0)
        return b.influentialCitations - a.influentialCitations;
      return b.citationCount - a.citationCount;
    })
    .slice(0, 12);
}

function calculateKeywordDensity(text: string, question: string): number {
  const keywords = question.toLowerCase().split(/[\s\-]+/);
  const words = text.toLowerCase().split(/[\s\-]+/);
  const matches = keywords.filter((kw) => words.includes(kw));
  return matches.length / keywords.length;
}
