import { initializeServerCache } from "@/lib/serverInit";
import { NextResponse } from "next/server";

export const maxDuration = 300; // 5 minutes timeout

export async function GET() {
  try {
    await initializeServerCache();
    return NextResponse.json({
      status: "success",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Cache warmup failed:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Failed to initialize cache",
      },
      { status: 500 },
    );
  }
}
