import { NextRequest, NextResponse } from 'next/server';
import { streamChatCompletion, createEmbedding } from '../../../lib/openai';
import { ChatRequest, Citation } from '../../../types/darwin';

// OpenAI only - no Pinecone or Bedrock needed

/**
 * Helper to calculate cosine similarity between two vectors
 */
function calculateCosineSimilarity(vec1: number[], vec2: number[]): number {
  const dot = vec1.reduce((sum, a, i) => sum + a * vec2[i], 0);
  const mag1 = Math.sqrt(vec1.reduce((sum, a) => sum + a * a, 0));
  const mag2 = Math.sqrt(vec2.reduce((sum, a) => sum + a * a, 0));
  return dot / (mag1 * mag2);
}

/**
 * Calculate keyword density for relevance scoring
 */
function calculateKeywordDensity(text: string, question: string): number {
  const keywords = question.toLowerCase().split(/[\s\-]+/);
  const words = text.toLowerCase().split(/[\s\-]+/);
  const matches = keywords.filter((kw) => words.includes(kw));
  return matches.length / keywords.length;
}

/**
 * Calculate overall relevance score
 */
function calculateRelevanceScore(
  cosineSim: number,
  keywordScore: number,
  paper: any,
): number {
  const citationWeight = Math.log10(paper.citationCount + 1) * 0.2;
  const recencyWeight = new Date().getFullYear() - paper.year < 5 ? 0.15 : 0;
  const abstractQuality = paper.abstract.length > 500 ? 0.1 : 0;

  return Math.min(
    1,
    cosineSim * 0.5 +
      keywordScore * 0.2 +
      citationWeight +
      recencyWeight +
      abstractQuality,
  );
}

/**
 * Simple Semantic Scholar search without Bedrock dependencies
 */
async function searchSemanticScholar(question: string): Promise<any[]> {
  const headers = {
    "x-api-key": process.env.SEMANTIC_SCHOLAR_API_KEY as string,
  };

  // Use the question directly as search query
  const searchQuery = encodeURIComponent(question);
  const url = `https://api.semanticscholar.org/graph/v1/paper/search?query=${searchQuery}&sort=influentialCitationCount:desc&fields=title,abstract,citationCount,authors,externalIds,year,influentialCitationCount,publicationVenue&limit=20`;

  try {
    const response = await fetch(url, { headers });
    const json = await response.json();

    if (!json.data) return [];

    const questionEmbedding = await createEmbedding(question);
    const papersMap = new Map();

    await Promise.all(
      json.data.map(async (paper: any) => {
        try {
          if (!paper.abstract || !paper.title || paper.year < 1900) return null;
          
          const doi = paper.externalIds?.DOI;
          if (!doi) return null;

          const paperContent = `${paper.title} ${paper.abstract}`;
          const paperEmbedding = await createEmbedding(paperContent);
          const cosineSim = calculateCosineSimilarity(questionEmbedding, paperEmbedding);
          const keywordScore = calculateKeywordDensity(paperContent, question);

          const relevance = calculateRelevanceScore(cosineSim, keywordScore, {
            citationCount: paper.citationCount,
            year: paper.year,
            abstract: paper.abstract,
          });

          if (relevance < 0.6) return null; // Lower threshold for OpenAI

          const paperData = {
            abstract: paper.abstract,
            title: paper.title,
            citationCount: paper.citationCount || 0,
            doi: doi,
            authors: paper.authors?.map((a: any) => a.name) || [],
            year: paper.year,
            venue: paper.publicationVenue?.name,
            influentialCitations: paper.influentialCitationCount || 0,
            relevance_score: relevance,
            paper_id: String(papersMap.size), // Simple ID assignment
          };

          papersMap.set(doi, paperData);
        } catch (error) {
          console.error(`Error processing paper: ${error}`);
        }
      }),
    );

    return Array.from(papersMap.values())
      .sort((a, b) => {
        if (b.relevance_score - a.relevance_score > 0.05)
          return b.relevance_score - a.relevance_score;
        if (b.influentialCitations - a.influentialCitations !== 0)
          return b.influentialCitations - a.influentialCitations;
        return b.citationCount - a.citationCount;
      })
      .slice(0, 10);
  } catch (error) {
    console.error('Semantic Scholar search failed:', error);
    return [];
  }
}

/**
 * Transform paper into Citation format for Darwin sidebar
 */
function paperToCitation(paper: any): Citation {
  return {
    id: paper.paper_id || String(Math.random()),
    title: paper.title || 'Unknown Title',
    authors: Array.isArray(paper.authors) ? paper.authors : ['Unknown'],
    url: paper.doi ? `https://doi.org/${paper.doi}` : 'https://example.com',
    journal: paper.venue || undefined,
    year: paper.year || undefined,
    impactFactor: undefined,
    noveltyScore: paper.relevance_score || undefined,
    isOpenAccess: undefined,
    relevanceScore: paper.relevance_score || undefined,
    doi: paper.doi || undefined,
    abstract: paper.abstract || undefined,
  };
}

export async function POST(req: NextRequest) {
  try {
    const body: ChatRequest = await req.json();
    const { message, mode = 'deep', conversationHistory = [], existingCitations = [] } = body;

    if (!message || message.trim().length === 0) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 });
    }

    console.log('Darwin chat request:', message, 'mode:', mode, 'history length:', conversationHistory.length);

    const encoder = new TextEncoder();
    const sseStream = new ReadableStream({
      async start(controller) {
        try {
          console.log('Starting research with OpenAI...');
          
          // Step 1: Search for papers using Semantic Scholar (only if it's a new topic or no existing citations)
          let papers = [];
          let citations: Citation[] = [];
          
          // If this is a follow-up question and we have existing citations, reuse them
          if (conversationHistory.length > 0 && existingCitations.length > 0) {
            console.log('Using existing citations for follow-up question');
            citations = existingCitations;
          } else {
            // Search for new papers
            papers = await searchSemanticScholar(message);
            console.log('Papers found:', papers.length);

            // Convert papers to citations
            citations = papers.map((paper, index) => {
              const citation = paperToCitation({
                ...paper,
                paper_id: String(index),
              });
              console.log('Citation created:', index, {
                id: citation.id,
                title: citation.title,
                authors: citation.authors,
                abstract: citation.abstract?.substring(0, 100) + '...'
              });
              return citation;
            });
          }

          console.log('Citations to use:', citations.length);
          
          // Send citations first so UI can render sidebar immediately
          const citationPayload = { citations: conversationHistory.length === 0 ? citations : [], content: '', isComplete: false };
          console.log('Sending citation payload:', JSON.stringify(citationPayload).substring(0, 200) + '...');
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(citationPayload)}\n\n`),
          );
          
          console.log('Sent citations to frontend:', citations.length);

          // Step 3: Build context for LLM
          const contextText = citations.map((c, i) => 
            `[${i}] TITLE: ${c.title}\nAUTHORS: ${c.authors.join(', ')}\nABSTRACT: ${c.abstract || 'N/A'}\n`
          ).join('\n---\n');

          const systemPrompt = mode === 'deep'
            ? 'You are Darwin, an expert research analyst providing evidence-based scientific insights. Write in clear, conversational English without using markdown formatting (no #, *, _, etc.). Deliver comprehensive responses using precise terminology, quantitative data, and peer-reviewed evidence. Employ appropriate scientific jargon and technical language. Cite primary sources inline using the provided citation tags. Focus on statistical significance, effect sizes, confidence intervals, and mechanistic explanations. Keep responses focused and well-structured in plain text format.'
            : 'You are Darwin, a research analyst providing concise, evidence-based scientific summaries. Write in clear, conversational English without using markdown formatting (no #, *, _, etc.). Deliver focused responses using appropriate technical terminology backed by quantitative data and peer-reviewed research. Cite primary sources inline using the provided citation tags. Emphasize key findings, statistical measures, and clinical significance. Keep responses concise and to the point in plain text format.';

          // Build conversation context
          let conversationContext = '';
          if (conversationHistory.length > 0) {
            conversationContext = '\n\nCONVERSATION HISTORY:\n' + 
              conversationHistory.slice(-4).map((msg, i) => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n') + 
              '\n\nCURRENT QUESTION: ';
          }

          const userPrompt = `You are providing a professional research analysis based on peer-reviewed scientific literature. Write in clear, conversational English without any markdown formatting characters. Deliver evidence-based insights using appropriate scientific terminology and quantitative data. ${conversationHistory.length > 0 ? 'This is a follow-up inquiry - build upon the previous analytical context.' : ''} 

ANALYTICAL REQUIREMENTS:
- Write in plain English without markdown symbols (no #, *, _, etc.)
- Keep responses concise and focused - avoid overly long explanations
- Employ precise scientific terminology and technical language
- Present quantitative data with statistical measures (p-values, effect sizes, confidence intervals)
- Reference methodological approaches and study designs
- Include sample sizes, demographic characteristics, and study duration
- Discuss mechanistic pathways and biological/physiological processes
- Present findings with appropriate scientific rigor
- Structure responses clearly in plain text format
- Always cite papers inline using the exact format: <|INSIGHT|>ID||title||authors||url

RESEARCH QUERY: ${conversationContext}${message}

PEER-REVIEWED EVIDENCE:
${contextText}

Provide a focused analysis grounded in the available research evidence, emphasizing statistical significance and clinical/practical relevance. Keep your response concise and well-structured in plain text.`;

          console.log('Generating response with OpenAI...');

          // Step 4: Use OpenAI for response generation
          try {
            // Build messages array with conversation history
            const messages: { role: 'system' | 'user' | 'assistant'; content: string }[] = [
              { role: 'system', content: systemPrompt },
            ];

            // Add conversation history if available (limit to last 4 messages to stay within token limits)
            if (conversationHistory.length > 0) {
              const recentHistory = conversationHistory.slice(-4);
              messages.push(...recentHistory.map(msg => ({
                role: msg.role as 'user' | 'assistant',
                content: msg.content
              })));
            }

            // Add current user prompt
            messages.push({ role: 'user', content: userPrompt });

            const stream = await streamChatCompletion({
              messages,
            });

            for await (const chunk of stream) {
              const token = (chunk as any).choices?.[0]?.delta?.content || '';
              if (token) {
                try {
                  controller.enqueue(
                    encoder.encode(`data: ${JSON.stringify({ citations: [], content: token, isComplete: false })}\n\n`),
                  );
                } catch (enqueueError) {
                  console.log('Controller closed during streaming, stopping...');
                  break;
                }
              }
            }

            // Send final event only if controller is still open
            try {
              controller.enqueue(
                encoder.encode(`data: ${JSON.stringify({ citations: [], content: '', isComplete: true })}\n\n`),
              );
            } catch (finalError) {
              console.log('Controller already closed, skipping final event');
            }
          } catch (streamError) {
            console.error('OpenAI streaming error:', streamError);
            try {
              controller.enqueue(
                encoder.encode(`data: ${JSON.stringify({ error: 'Streaming failed: ' + (streamError instanceof Error ? streamError.message : String(streamError)), isComplete: true })}\n\n`),
              );
            } catch (errorEnqueueError) {
              console.log('Controller closed, cannot send error message');
            }
          }

        } catch (error) {
          console.error('OpenAI research error:', error);
          try {
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({ error: 'Failed to generate response: ' + (error instanceof Error ? error.message : String(error)), isComplete: true })}\n\n`),
            );
          } catch (enqueueError) {
            console.log('Controller closed, cannot send error message');
          }
        } finally {
          try {
            controller.close();
          } catch (closeError) {
            console.log('Controller already closed');
          }
        }
      },
    });

    return new NextResponse(sseStream, {
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'text/event-stream',
        Connection: 'keep-alive',
      },
    });
  } catch (err) {
    console.error('Chat route error', err);
    return NextResponse.json({ error: 'Internal error: ' + (err instanceof Error ? err.message : String(err)) }, { status: 500 });
  }
} 