import { NextResponse } from "next/server";
import { SNSClient, PublishCommand } from "@aws-sdk/client-sns";

// Configure AWS SDK
const snsClient = new SNSClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.V_AWS_ACCESS!,
    secretAccessKey: process.env.V_AWS_SECRET!,
  },
});

export async function POST(request: Request) {
  try {
    const { doi, title, subtitle } = await request.json();

    // Create the message
    const message = JSON.stringify({
      doi,
      title,
      subtitle,
      timestamp: new Date().toISOString(),
    });

    // Ensure SNS_TOPIC_ARN is set
    const topicArn = process.env.REGENERATE_SNS;
    console.log(topicArn);

    if (!topicArn) {
      console.log(topicArn);
      throw new Error("SNS_TOPIC_ARN is not set in environment variables");
    }

    // Publish the message to SNS
    const command = new PublishCommand({
      Message: message,
      TopicArn: topicArn,
    });

    const result = await snsClient.send(command);

    return NextResponse.json({
      success: true,
      messageId: result.MessageId,
    });
  } catch (error) {
    console.error("Error publishing to SNS:", error);
    return NextResponse.json(
      {
        success: false,
        error: (error as Error).message || "Failed to publish message",
      },
      { status: 500 },
    );
  }
}
