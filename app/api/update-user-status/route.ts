import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { PrismaClient } from "@prisma/client";
import { revalidatePath } from "next/cache";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2024-06-20",
});

const prisma = new PrismaClient();

export async function POST(req: Request) {
  if (req.method === "POST") {
    try {
      const { session_id } = await req.json();

      // Retrieve the session from Stripe
      const session = await stripe.checkout.sessions.retrieve(session_id);

      if (session.payment_status === "paid") {
        // Get the customer email from the session
        const customerEmail = session.customer_details?.email;

        if (customerEmail) {
          // Update the user status in the database
          await prisma.user.update({
            where: { email: customerEmail },
            data: { role: "PAID_USER", credit: 100 },
          });
          revalidatePath("/");

          // Retrieve the plan name and amount from the session
          const lineItems =
            await stripe.checkout.sessions.listLineItems(session_id);
          const planName = lineItems.data[0]?.description || "Unknown Plan";
          const amount = session.amount_total ? session.amount_total / 100 : 0; // Convert cents to dollars

          return NextResponse.json({
            message: "User status updated successfully",
            planName,
            amount,
          });
        } else {
          throw new Error("Customer email not found in session");
        }
      } else {
        throw new Error("Payment not completed");
      }
    } catch (err: any) {
      console.error("Error updating user status:", err);
      return NextResponse.json(
        { error: { message: err.message } },
        { status: 500 },
      );
    }
  } else {
    return NextResponse.json(
      {
        error: { message: "Method Not Allowed" },
      },
      { status: 405 },
    );
  }
}
