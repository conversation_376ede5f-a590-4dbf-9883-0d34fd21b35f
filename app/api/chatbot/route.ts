import { NextRequest, NextResponse } from "next/server";
import { BedrockRuntimeClient, InvokeModelCommand, InvokeModelWithResponseStreamCommand } from "@aws-sdk/client-bedrock-runtime";

export interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

export interface DatasetContext {
  authors?: any[];
  papers?: any[];
  queryData?: {
    count: string;
    type: string;
    keyword: string;
    timeWindow: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const { messages, datasetContext, streaming = false }: {
      messages: ChatMessage[];
      datasetContext: DatasetContext;
      streaming?: boolean;
    } = await request.json();

    // Get AWS credentials from environment variables (server-side)
    const awsRegion = process.env.AWS_REGION || "us-west-2";
    const awsAccessKeyId = process.env.AWS_ACCESS;
    const awsSecretAccessKey = process.env.AWS_SECRET;

    if (!awsAccessKeyId || !awsSecretAccessKey) {
      return NextResponse.json(
        { error: "AWS credentials not configured on server" },
        { status: 500 }
      );
    }

    const client = new BedrockRuntimeClient({
      region: awsRegion,
      credentials: {
        accessKeyId: awsAccessKeyId,
        secretAccessKey: awsSecretAccessKey,
      },
    });

    // Build context from dataset
    let contextString = "";
    if (datasetContext.queryData) {
      contextString += `Query Context: Looking for top ${datasetContext.queryData.count} ${datasetContext.queryData.type} in "${datasetContext.queryData.keyword}" over ${datasetContext.queryData.timeWindow} months.\n\n`;
    }

    if (datasetContext.authors && datasetContext.authors.length > 0) {
      contextString += "Authors Data:\n";
      datasetContext.authors.slice(0, 10).forEach((author, index) => {
        contextString += `${index + 1}. ${author.display_name} (${author.works_count} works, ${author.cited_by_count} citations)\n`;
        if (author.summary_stats) {
          contextString += `   Recent activity: ${author.summary_stats.recent_works} works, ${author.summary_stats.recent_citations} citations\n`;
        }
      });
      contextString += "\n";
    }

    if (datasetContext.papers && datasetContext.papers.length > 0) {
      contextString += "Papers Data:\n";
      datasetContext.papers.slice(0, 10).forEach((paper, index) => {
        contextString += `${index + 1}. "${paper.title}" (${paper.publication_year})\n`;
        contextString += `   Citations: ${paper.cited_by_count}, Authors: <AUTHORS>
        if (paper.abstract) {
          contextString += `   Abstract: ${paper.abstract.substring(0, 200)}...\n`;
        }
      });
      contextString += "\n";
    }

    // Prepare messages for Claude
    const systemMessage = `You are a research assistant helping users understand academic research data. You have access to the following dataset context:

${contextString}

Please provide helpful, accurate responses based on this data. When discussing specific researchers or papers, reference the data provided. Be conversational but informative.`;

    const claudeMessages = [
      {
        role: "user" as const,
        content: systemMessage + "\n\nUser: " + messages[messages.length - 1].content
      }
    ];

    const requestBody = {
      anthropic_version: "bedrock-2023-05-31",
      max_tokens: 4000,
      messages: claudeMessages,
      temperature: 0.7,
    };

    if (streaming) {
      // Handle streaming response
      const command = new InvokeModelWithResponseStreamCommand({
        modelId: "anthropic.claude-3-5-sonnet-20240620-v1:0",
        contentType: "application/json",
        body: JSON.stringify(requestBody),
      });

      const response = await client.send(command);
      
      if (!response.body) {
        throw new Error("No response body received from Bedrock");
      }

      // Create a readable stream for the client
      const encoder = new TextEncoder();
      const stream = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of response.body!) {
              if (chunk.chunk?.bytes) {
                const chunkData = JSON.parse(new TextDecoder().decode(chunk.chunk.bytes));
                if (chunkData.type === "content_block_delta" && chunkData.delta?.text) {
                  controller.enqueue(encoder.encode(`data: ${JSON.stringify({ text: chunkData.delta.text })}\n\n`));
                }
              }
            }
            controller.close();
          } catch (error) {
            controller.error(error);
          }
        },
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      // Non-streaming response
      const command = new InvokeModelCommand({
        modelId: "anthropic.claude-3-5-sonnet-20240620-v1:0",
        contentType: "application/json",
        body: JSON.stringify(requestBody),
      });

      const response = await client.send(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));

      return NextResponse.json({
        response: responseBody.content[0].text
      });
    }
  } catch (error) {
    console.error("Error calling Bedrock:", error);
    return NextResponse.json(
      { error: `Failed to get response from Claude: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Health check endpoint
  try {
    const awsAccessKeyId = process.env.AWS_ACCESS;
    const awsSecretAccessKey = process.env.AWS_SECRET;
    const awsRegion = process.env.AWS_REGION;

    return NextResponse.json({
      configured: !!(awsAccessKeyId && awsSecretAccessKey && awsRegion),
      region: awsRegion || "not set"
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Configuration check failed" },
      { status: 500 }
    );
  }
}
