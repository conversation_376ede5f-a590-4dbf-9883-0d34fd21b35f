import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import prisma from "@/lib/prisma";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2024-06-20",
});

export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, role: true },
    });

    if (!user || user.role !== "PAID_USER") {
      return NextResponse.json(
        {
          error: "User not found or no active subscription",
        },
        { status: 404 },
      );
    }

    const stripeCustomers = await stripe.customers.list({
      email: email,
    });
    if (stripeCustomers.data.length === 0) {
      return NextResponse.json(
        {
          error: "No Stripe customer found for this user",
        },
        { status: 404 },
      );
    }

    for (const customer of stripeCustomers.data) {
      const stripeCustomerId = customer.id;

      const subscriptions = await stripe.subscriptions.list({
        customer: stripeCustomerId,
      });

      console.log({ stripeCustomerId, subscriptions });

      if (subscriptions.data.length === 0) {
        continue;
      }

      // Cancel all active subscriptions
      for (const subscription of subscriptions.data) {
        await stripe.subscriptions.cancel(subscription.id);
      }
    }

    // Update user's role in the database
    await prisma.user.update({
      where: { email },
      data: { role: "USER" },
    });

    console.log("Subscriptions canceled successfully");

    return NextResponse.json({
      message: "All active subscriptions successfully canceled",
    });
  } catch (error) {
    console.error("Error canceling subscription:", error);
    return NextResponse.json(
      { error: "Failed to cancel subscription" },
      { status: 500 },
    );
  }
}
