import { NextResponse } from "next/server";
import { Pinecone } from "@pinecone-database/pinecone";
import { getEmbedding } from "../../../lib/embeddings";

if (!process.env.PINECONE_API_KEY) {
  throw new Error("Missing PINECONE_API_KEY environment variable");
}

// Initialize Pinecone client
const pinecone = new Pinecone();

export async function POST(request: Request) {
  try {
    const index = pinecone.Index("semantic-scholar-v2");

    const { keyword, daysToFilter = 30, topK = 20 } = await request.json();

    // if (!keyword) {
    //   return NextResponse.json(
    //     { error: "Keyword is required" },
    //     { status: 400 },
    //   );
    // }

    // Generate vector embedding for the keyword
    const vector = await getEmbedding(keyword);

    // Calculate the date threshold for filtering
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - daysToFilter);
    const timestampThreshold = Math.ceil(dateThreshold.getTime() / 1000);

    // Prepare the filter
    const filter: Record<string, any> = {
      publishTimeStamp: { $gte: timestampThreshold },
    };

    // Perform semantic search
    const queryResponse = await index.query({
      topK,
      vector,
      // filter,
      includeMetadata: true,
    });

    console.log(queryResponse);

    // Sort results by publishTimeStamp in descending order
    const sortedResults = queryResponse.matches.sort(
      (a, b) => Number(b.score || 0) - Number(a.score || 0),
    );

    // Process and return results
    const results = sortedResults.map((match) => ({
      id: match.id,
      score: match.score,
      metadata: match.metadata, // Updated to `fields` since Pinecone uses that structure
    }));

    return NextResponse.json({ results });
  } catch (error) {
    console.error("Semantic search error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
