import React from "react";
import { ArticleCarouselSkeleton } from "@/app/catalogue/loading";
import ArticleGridSkeleton from "@/app/component/ArticleComponents/ArticleGridSkeleton";

export default function DSDLoading() {
  return (
    <div className="w-full bg-[#132435] text-white min-h-screen mx-auto py-4 overflow-x-hidden">
      <div className="p-[20px] lg:w-[1100px] bg-[#132435] md:w-[756px] mx-auto flex flex-col items-center justify-start text-white h-full">
        <ArticleGridSkeleton columns={5} rows={2} />
        <div className="w-full flex flex-col md:flex-row gap-8 mt-8 mb-2">
          <div className="w-1/2 h-[400px] md:w-1/2 bg-gray-700 animate-pulse"></div>
          <div className="w-1/2 h-[400px] md:w-1/2 bg-gray-700 animate-pulse"></div>
        </div>
        <ArticleCarouselSkeleton />
        <ArticleCarouselSkeleton />
        <ArticleCarouselSkeleton />
      </div>
    </div>
  );
}
