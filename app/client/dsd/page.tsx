import React, { Suspense } from "react";
import { Image, Link } from "@nextui-org/react";
import { redirect } from "next/navigation";
import {
  getFeaturedArticles,
  FeaturedArticlesResponse,
} from "@/app/catalogue/actions/getFeaturedArticle";
import { ArticleCarouselSkeleton, HomeSkeleton } from "@/app/catalogue/loading";
import { getSouthAustralianArticles } from "@/app/catalogue/actions/getSouthAustralianArticles";
import ArticleCarousel from "@/app/catalogue/components/ArticleCarousel";
import { BookmarkButton } from "@/app/catalogue/components/BookmarkButton";
import ArticleGridSkeleton from "@/app/component/ArticleComponents/ArticleGridSkeleton";
import TopAuthors from "./components/TopAuthors";
import QATool from "./components/QATool";
import PlaylistCarousel from "@/app/catalogue/components/PlaylistsCarousel";
import { getPlaylists } from "@/app/catalogue/actions/getPlaylists";
import { getUser } from "@/lib/cache/getUser";

async function FeaturedCatergoriesSection({ userId }: { userId: string }) {
  let { featuredArticles } = await getFeaturedArticles({ userId });
  featuredArticles = featuredArticles.slice(0, 5);
  return <FeaturedCatergories featuredArticles={featuredArticles} />;
}

async function PlaylistsSection() {
  const playlists = await getPlaylists("cm481ta2c0002ledk5p7ay0ab");
  return (
    <div className="w-full ">
      <div className="flex items-start justify-between mb-1">
        <h2 className="text-[22px] mb-5 text-white">Collections</h2>
        <a
          href={`/collections`}
          className="text-[22px] font-normal text-[#88D84D] "
        >
          View All
        </a>
      </div>
      <PlaylistCarousel playlists={playlists} />
    </div>
  );
}

function FeaturedCatergories({
  featuredArticles,
}: {
  featuredArticles: FeaturedArticlesResponse["featuredArticles"];
}) {
  return (
    <div className="w-full">
      <div className="my-4">
        <div className="flex items-start justify-between mb-1">
          <h2 className="text-[22px] mb-5 text-white">Featured Articles</h2>
        </div>
        <div className={`grid grid-cols-5 gap-8`}>
          {featuredArticles.map((article) => (
            <div key={article.id} className="overflow-hidden flex flex-col">
              <Link
                className="w-[150px] lg:w-[178px] flex flex-col text-white"
                href={`/article/${article.slug}`}
              >
                <div className="h-220 lg:h-[227px] relative flex items-center justify-start overflow-hidden mb-2">
                  <Image
                    src={article.articleImage.src}
                    alt={article.articleImage.alt}
                    className="w-full rounded-2xl"
                  />
                </div>
                <div className="flex flex-col justify-between items-start w-full">
                  <div className="flex flex-row justify-start items-start">
                    <h2 className="text-md w-[90%] mb-2 line-clamp-4">
                      {article.title}
                    </h2>
                    <BookmarkButton
                      articleId={article.id}
                      initialIsBookmarked={article.isBookmarked}
                    />
                  </div>
                  <div className="flex justify-start items-center">
                    <div className="text-[10px] w-auto text-center mr-2 bg-gray-500 px-1 text-white">
                      {article.estimatedReadingTime} min{" "}
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

async function SouthAustralianSection() {
  const southAustralianArticles = await getSouthAustralianArticles();
  return (
    <div className="w-full">
      <h2 className="text-2xl mb-4 mt-8 text-start w-full">
        Top South Australian Papers
      </h2>
      <ArticleCarousel articles={southAustralianArticles} />
    </div>
  );
}

async function GreenEnergySection() {
  const southAustralianArticles = await getSouthAustralianArticles();
  return (
    <div className="w-full">
      <h2 className="text-2xl mb-4 mt-8 text-start w-full">
        Top Green Energy Papers
      </h2>
      <ArticleCarousel articles={southAustralianArticles} />
    </div>
  );
}

export default async function DSDPage() {
  const user = await getUser();
  if (!user) {
    redirect("/signin");
  }
  const userId = user.id;
  return (
    <div className="w-full bg-[#132435] text-white min-h-screen mx-auto py-4 overflow-x-hidden">
      <div className="p-[20px] lg:w-[1100px] bg-[#132435] md:w-[756px] mx-auto flex flex-col items-center justify-start text-white h-full">
        <Suspense
          fallback={
            <div className="w-full space-y-8">
              {[1].map((i) => (
                <ArticleGridSkeleton key={i} columns={5} />
              ))}
            </div>
          }
        >
          <FeaturedCatergoriesSection userId={userId} />
        </Suspense>
        <h2 className="text-2xl font-bold mb-4">
          Top 10 South Australian Authors
        </h2>
        <div className="w-full flex flex-col md:flex-row gap-8 mt-8">
          <div className="w-1/2 h-[400px]md:w-1/2">
            <TopAuthors />
          </div>
          <div className="w-1/2 h-[400px] md:w-1/2">
            <QATool />
          </div>
        </div>
        <Suspense fallback={<ArticleCarouselSkeleton />}>
          <GreenEnergySection />
        </Suspense>

        <PlaylistsSection />

        <Suspense fallback={<ArticleCarouselSkeleton />}>
          <SouthAustralianSection />
        </Suspense>
      </div>
    </div>
  );
}
