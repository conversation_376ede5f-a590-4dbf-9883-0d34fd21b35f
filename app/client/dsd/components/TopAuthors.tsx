import React from "react";
import { Link } from "@nextui-org/react";
import Image from "next/image";

interface Author {
  name: string;
  institution: string;
  href: string;
  ranking?: number;
}

const authors: Author[] = [
  {
    name: "Prof <PERSON>",
    institution: "The University of Adelaide",
    href: "https://scholar.google.com.au/citations?user=WUuHiYQAAAAJ&hl=en",
    ranking: 1,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    institution: "Flinders University",
    href: "https://scholar.google.com/citations?user=AlbIWxwAAAAJ&hl=en",
    ranking: 2,
  },
  {
    name: "<PERSON>",
    institution: "University of South Australia",
    href: "https://scholar.google.com/citations?hl=en&user=288soRcAAAAJ&view_op=list_works&sortby=pubdate",
  },
  {
    name: "<PERSON>",
    institution: "The University of Adelaide",
    href: "https://scholar.google.com.au/citations?hl=en&user=B0bo5SUAAAAJ&view_op=list_works&sortby=pubdate",
    ranking: 4,
  },
  {
    name: "<PERSON>",
    institution: "The University of Adelaide",
    href: "https://scholar.google.com.au/citations?hl=en&user=1RbHHOoAAAAJ&view_op=list_works&sortby=pubdate",
    ranking: 5,
  },
  {
    name: "Peter Veitch",
    institution: "The University of Adelaide",
    href: "https://scholar.google.com.au/citations?user=9CpunLEAAAAJ&hl=en",
  },
  {
    name: "Daniel Brown",
    institution: "The University of Adelaide",
    href: "https://scholar.google.co.uk/citations?user=OukRd7oAAAAJ&hl=en",
  },
  {
    name: "Jesper Munch",
    institution: "The University of Adelaide",
    href: "https://scholar.google.com.au/citations?user=pURe3i0AAAAJ&hl=en",
  },
  {
    name: "Shaobin Wang",
    institution: "The University of Adelaide",
    href: "https://scholar.google.com/citations?user=wzVn_jkAAAAJ&hl=en",
    ranking: 9,
  },
  {
    name: "Jonathan Craig",
    institution: "Flinders University",
    href: "https://scholar.google.com/citations?user=D1qn1hkAAAAJ&hl=en",
    ranking: 10,
  },
];

export default function TopAuthors() {
  return (
    <div className="w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 bg-[#27394F]">
        {authors.map((author, index) => (
          <Link
            key={index}
            href={author.href}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center p-2 px-6 bg-[#27394F] rounded-lg hover:bg-[#324b6e] transition-colors"
          >
            <div className="w-12 h-12 mr-4 relative">
              <Image
                src={
                  author.ranking
                    ? `/authors/a-${author.ranking}.jpeg`
                    : "/authors/default.png"
                }
                alt={author.name}
                width={48}
                height={48}
                className="rounded-full"
              />
            </div>
            <div>
              <h3 className="text-m font-semibold">{author.name}</h3>
              <p className="text-sm text-gray-300">{author.institution}</p>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
