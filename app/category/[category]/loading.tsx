import React from "react";

const ArticleSkeletonItem = () => (
  <div className="flex bg-[#27394F] rounded-lg overflow-hidden shadow-lg p-4 h-[275px] w-full animate-pulse">
    <div className="w-auto relative h-full">
      <div className="w-[178px] h-[227px] bg-gray-700 rounded-lg"></div>
    </div>
    <div className="w-3/4 p-4 relative flex items-start justify-start">
      <div className="flex flex-col h-full items-start justify-between w-full">
        <div>
          <div className="h-6 bg-gray-700 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-700 rounded w-full mb-1"></div>
          <div className="h-4 bg-gray-700 rounded w-2/3"></div>
        </div>
        <div className="bg-gray-700 rounded-full w-16 h-6"></div>
      </div>
      <div className="w-6 h-6 bg-gray-700 rounded-full"></div>
    </div>
  </div>
);

export default function Loading() {
  return (
    <div className="w-full bg-[#132435] text-white">
      <div className="w-[393px] lg:w-[1100px] md:w-[756px] mx-auto p-5">
        <div className="h-10 bg-gray-700 rounded w-1/4 mb-6"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(6)].map((_, index) => (
            <ArticleSkeletonItem key={index} />
          ))}
        </div>
        <div className="flex justify-center mt-6">
          <div className="h-10 bg-gray-700 rounded w-64"></div>
        </div>
      </div>
    </div>
  );
}
