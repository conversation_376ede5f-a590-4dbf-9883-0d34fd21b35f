import React from "react";
import { notFound, redirect } from "next/navigation";
import prisma from "@/lib/prisma";
import Link from "next/link";
import Image from "next/image";
import PaginationControls from "../../component/PaginationControls";
import { BookmarkButton } from "@/app/catalogue/components/BookmarkButton";
import { unstable_cache } from "next/cache";
import { getUser } from "@/lib/cache/getUser";
import { Headphones } from "lucide-react";

const ITEMS_PER_PAGE = 20;

const noveltyScores = [
  { doi: "10.1093/icesjms/fsae138", noveltyScore: 2.660607493 },
  { doi: "10.1021/acs.chemrev.3c00795", noveltyScore: 1.111002683 },
  { doi: "10.1016/j.eneco.2024.108042", noveltyScore: 1.689356639 },
  { doi: "10.1007/s43621-024-00374-4", noveltyScore: 1.814600599 },
  { doi: "10.1016/j.enpol.2025.114520", noveltyScore: 1.663959898 },
  { doi: "10.1016/j.solmat.2024.113190", noveltyScore: 2.35636537 },
  { doi: "10.1016/j.jastp.2024.106384", noveltyScore: 2.660929389 },
  { doi: "10.1016/j.apenergy.2024.123645", noveltyScore: 2.696471613 },
  { doi: "10.1016/j.ccr.2024.216238", noveltyScore: 3.124105263 },
  { doi: "10.1016/j.apenergy.2024.124674", noveltyScore: 3.124105263 },
  { doi: "10.51414/sei2024.046", noveltyScore: 3.137910247 },
  { doi: "10.1016/j.solmat.2024.113245", noveltyScore: 3.544629229 },
  { doi: "10.1016/j.esd.2024.101549", noveltyScore: 2.947683013 },
  { doi: "10.1016/j.gee.2024.02.001", noveltyScore: 3.644789474 },
  { doi: "10.5194/wes-10-227-2025", noveltyScore: 3.716137064 },
  { doi: "10.1002/anie.202420687", noveltyScore: 4.720119825 },
  { doi: "10.1002/solr.202400628", noveltyScore: 6.690223894 },
  { doi: "10.1016/j.egycc.2024.100133", noveltyScore: 6.755626895 },
  { doi: "10.1016/j.solmat.2024.113188", noveltyScore: 7.078001309 },
  { doi: "10.1002/smll.202408168", noveltyScore: 8.062870556 },
  { doi: "10.1016/j.apenergy.2024.124656", noveltyScore: 8.81722704 },
  { doi: "10.1016/j.susoc.2024.11.002", noveltyScore: 14.36373884 },
  { doi: "10.1016/j.esd.2024.101595", noveltyScore: 15.21870595 },
  { doi: "10.1016/j.erss.2025.103923", noveltyScore: 15.21870595 },
  { doi: "10.1016/j.erss.2024.103904", noveltyScore: 18.04527738 },
  { doi: "10.1016/j.rser.2024.115057", noveltyScore: 20.31648558 },
  { doi: "10.1016/j.xinn.2024.100734", noveltyScore: 22.56797492 },
  { doi: "10.1016/j.apenergy.2024.124895", noveltyScore: 25.36450992 },
  { doi: "10.1016/j.matt.2024.10.007", noveltyScore: 26.1355248 },
  { doi: "10.2172/2479271", noveltyScore: 26.20308108 },
  { doi: "10.1016/j.apcatb.2024.124968", noveltyScore: 40.60521469 },
  { doi: "10.1016/j.joule.2024.09.003", noveltyScore: 51.29666372 },
  { doi: "10.1016/j.solmat.2025.113444", noveltyScore: 75.79640075 },
  { doi: "10.1016/j.solmat.2024.113325", noveltyScore: 77.09640075 },
  { doi: "10.1016/j.enpol.2024.114485", noveltyScore: 98.10251525 },
  { doi: "10.1016/j.solmat.2024.113289", noveltyScore: 119.1086297 },
  { doi: "10.1016/j.solmat.2025.113412", noveltyScore: 162.4208587 },
  { doi: "10.1016/j.solmat.2024.113320", noveltyScore: 194.9050305 },
  { doi: "10.1016/j.rser.2024.115044", noveltyScore: 151.5928015 },
  { doi: "10.1038/s41560-024-01684-7", noveltyScore: 137.7047014 },
];

interface Article {
  id: string;
  title: string;
  subtitle: string;
  slug: string;
  estimatedReadingTime: number;
  articleImage: {
    src: string;
  } | null;
  isBookmarked: boolean;
  noveltyScore?: number;
}

const getCachedArticlesByCategory = unstable_cache(
  async (category: string, page: number = 1, userId?: string) => {
    const skip = (page - 1) * ITEMS_PER_PAGE;

    const baseSelect = {
      id: true,
      title: true,
      subtitle: true,
      audioId: true,
      slug: true,
      doi: true,
      estimatedReadingTime: true,
      articleImage: {
        select: {
          src: true,
        },
      },
      bookmarkedBy: {
        where: {
          userId: userId,
        },
        select: {
          userId: true,
        },
        take: 1,
      },
    };

    const addNoveltyScore = (articles: any[]) => {
      return articles.map((article) => ({
        ...article,
        isBookmarked: article.bookmarkedBy?.length > 0 ? true : false,
        noveltyScore: noveltyScores.find((score) => score.doi === article.doi)
          ?.noveltyScore,
      }));
    };

    if (category.toLowerCase() === "other") {
      const totalCount = await prisma.article.count();
      const randomSkip = Math.max(
        0,
        Math.floor(Math.random() * (totalCount - 100)),
      );

      const allArticles = await prisma.article.findMany({
        take: 100,
        skip: randomSkip,
        select: baseSelect,
      });

      const articles = addNoveltyScore(
        allArticles.slice(skip, skip + ITEMS_PER_PAGE),
      );

      return { articles, totalCount: 100 };
    } else if (category.toLowerCase() === "all") {
      const [articlesData, totalCount] = await Promise.all([
        prisma.article.findMany({
          select: baseSelect,
          skip,
          take: ITEMS_PER_PAGE,
        }),
        prisma.article.count(),
      ]);

      const articles = addNoveltyScore(articlesData);

      return { articles, totalCount };
    } else if (category.toLowerCase() === "energy") {
      const [articlesData, totalCount] = await Promise.all([
        prisma.article.findMany({
          where: {
            categories: {
              some: {
                name: category,
              },
            },
          },
          select: baseSelect,
          skip,
        }),
        prisma.article.count({
          where: {
            categories: {
              some: {
                name: category,
              },
            },
          },
        }),
      ]);

      const articles = addNoveltyScore(articlesData);

      return { articles, totalCount };
    } else {
      console.log("category", category);
      const [articles, totalCount] = await Promise.all([
        prisma.article.findMany({
          where: {
            categories: {
              some: {
                name: category,
              },
            },
          },
          select: baseSelect,
        }),
        prisma.article.count({
          where: {
            categories: {
              some: {
                name: category,
              },
            },
          },
        }),
      ]);

      return { articles, totalCount };
    }
  },
  ["articles-by-category"],
  { revalidate: 3600 }, // Cache for 1 hour
);

export default async function CategoryPage({
  params,
  searchParams,
}: {
  params: { category: string };
  searchParams: { page?: string };
}) {
  try {
    const user = await getUser();

    if (!user) {
      redirect("/signin");
    }

    const category = decodeURI(params.category);
    const page = searchParams.page ? parseInt(searchParams.page) : 1;

    const { articles, totalCount } = await getCachedArticlesByCategory(
      category,
      page,
      user.id,
    );

    if (category.toLowerCase() === "energy" && articles) {
      articles.sort((a, b) => {
        if (a.noveltyScore && b.noveltyScore) {
          return b.noveltyScore - a.noveltyScore;
        } else {
          return 0;
        }
      });
    }

    // console.log("articles", articles);
    if (!articles || (articles.length === 0 && page === 1)) {
      notFound();
    }

    const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

    return (
      <div className="w-full bg-[#132435] text-white">
        <div className="w-[393px] md:[756px] lg:w-[1100px] mx-auto p-5">
          <h1 className="text-2xl mb-6 capitalize">{category}</h1>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            {articles.map((article: any) => (
              <Link
                href={`/article/${article.slug}`}
                key={article.id}
                className="flex bg-[#27394F] rounded-lg overflow-hidden shadow-lg p-4 h-[200px] lg:h-[227px] w-full"
              >
                <div className="w-[155px] lg:w-[178px] flex justify-center items-center relative">
                  {article.articleImage && (
                    <Image
                      width={178}
                      height={227}
                      style={{
                        width: "100%",
                        height: "auto",
                      }}
                      className="rounded-2xl"
                      src={article.articleImage.src}
                      alt={article.title}
                    />
                  )}
                </div>
                <div className="w-[170px] lg:w-3/4 px-4 relative flex items-start justify-start">
                  <div className="flex flex-col h-full items-start justify-between">
                    <div>
                      <h2 className="text-[13px] lg:text-xl w-36 lg:w-full font-semibold mb-2 line-clamp-4">
                        {article.title}
                      </h2>
                      <p className="text-white text-[11px] lg:text-sm mb-2 line-clamp-3">
                        {article.subtitle}
                      </p>
                    </div>
                    <div className="flex gap-2 items-center">
                      <div className="bg-gray-500 text-center w-16 px-1 py-1 text-xs">
                        {article.estimatedReadingTime} min
                      </div>
                      {article.audioId && (
                        <div className="text-[10px] w-auto text-center bg-gray-500 text-white p-[4px]">
                          <Headphones size={16} />
                        </div>
                      )}
                      {category.toLowerCase() === "energy" && (
                        <div className="bg-gray-500 text-center w-[120px] px-1 py-1 text-xs">
                          {category.toLowerCase() === "energy" &&
                            article.noveltyScore !== null &&
                            article.noveltyScore !== undefined && (
                              <p className="text-xs">
                                Novelty Score: {article.noveltyScore.toFixed(0)}
                              </p>
                            )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-start">
                  <BookmarkButton
                    articleId={article.id}
                    initialIsBookmarked={article.isBookmarked}
                  />
                </div>
              </Link>
            ))}
          </div>
          {category.toLowerCase() !== "energy" && (
            <div className="mt-8 flex items-center justify-center w-full">
              <PaginationControls
                currentPage={page}
                totalPages={totalPages}
                href={category}
              />
            </div>
          )}
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error in CategoryPage:", error);
    throw error;
  }
}
