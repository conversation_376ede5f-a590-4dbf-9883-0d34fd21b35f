"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/lib/zustand/zustand";
import toast from "react-hot-toast";
import { loadStripe } from "@stripe/stripe-js";
import { trackCheckoutInitiated, trackUnsubscribe } from "@/lib/branch";
import { handleInitiateCheckout } from "../component/FacebookConversionAPI"; // Replace with your Facebook Pixel ID

const STRIPE_PUBLIC_KEY =
  "pk_live_51K55kmDOnxS19iBlIjxFwa8OVBnqkPr0N78QHtwk7QM3F9Vx6THdZZZh8zlHVxN0QUYpPL7ihPufwsxNYIaGtHin00gq1f3UhI";

interface SubscriptionCardProps {
  title: string;
  price: string;
  billing: string;
  features: string[];
  priceId: string;
  trialDays: number;
  isPopular?: boolean;
}

const SubscriptionCard = ({
  title,
  price,
  billing,
  features,
  priceId,
  trialDays,
  isPopular,
}: SubscriptionCardProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuthStore();
  const router = useRouter();

  const handleSelect = async () => {
    setIsLoading(true);
    try {
      // Track checkout initiated
      await handleInitiateCheckout(title);
      await trackCheckoutInitiated(
        user!.id,
        title,
        parseFloat(price.replace("$", "")),
      );

      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          priceId,
          trialDays,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }

      const { sessionId } = await response.json();
      const stripe = await loadStripe(STRIPE_PUBLIC_KEY);
      if (stripe) {
        await stripe.redirectToCheckout({
          sessionId,
        });
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast.error("Failed to create checkout session. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className={` w-[300px] h-[450px] lg:w-[500px] mb-8 lg:mb-0 lg:h-[500px] rounded-xl ${isPopular ? "border-8 border-[#88D84D]" : "border-8 border-[#2B3A55]"} bg-[#2B3A55] text-white p-6 relative flex mx-8 flex-col justify-center hover:scale-105 transition-all duration-300`}
    >
      {isPopular && (
        <div className="absolute top-0 right-0 bg-[#88D84D] text-[#132435] px-3 py-1 rounded-bl-xl rounded-tr-sm text-lg ">
          MOST POPULAR
        </div>
      )}
      <h2 className="text-2xl lg:text-4xl font-semibold mb-2">{title}</h2>
      <div className="text-[11px] lg:text-sm mb-4">{billing}</div>
      <ul className="justify-center flex flex-col">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center my-2 text-sm lg:text-lg">
            <svg
              className="w-5 h-5 mr-2 text-[#88D84D]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              ></path>
            </svg>
            {feature}
          </li>
        ))}
      </ul>
      <div className="mt-2 text-md lg:text-2xl text-center text-[#88D84D] font-semi-bold">
        {price}
      </div>
      <div className="w-full flex justify-center items-center ">
        <button
          onClick={handleSelect}
          disabled={isLoading}
          className={`absolute -bottom-[36px] lg:-bottom-8 px-12 ${isPopular ? "bg-[#88D84D]" : "bg-[#88D84D]"} rounded-full text-md lg:text-2xl text-[#132435] font-semi-bold py-3 transition-colors`}
        >
          {isLoading ? "Processing..." : "Get Started"}
        </button>
      </div>
    </div>
  );
};

const subscriptionOptions: SubscriptionCardProps[] = [
  {
    title: "Yearly",
    price: "$5 USD/Month",
    billing: "Billed as one payment of $60 USD each year",
    features: [
      "Insights from over 300M research papers",
      "Full access to DarwinAI",
      "Full access to CurieAI",
      "Unlimited report downloads",
      "Constantly expanding research database",
      "Easy cancellation",
    ],
    priceId: "price_1QWRHnDOnxS19iBl4XzOGhyU",
    trialDays: 7,
    isPopular: true,
  },
  {
    title: "Monthly",
    price: "10 USD/Month",
    billing: "Billed as monthly payments of $10 USD",
    features: [
      "Insights from over 300M research papers",
      "Full access to DarwinAI",
      "Full access to CurieAI",
      "Unlimited report downloads",
      "Constantly expanding research database",
      "Easy cancellation",
    ],
    priceId: "price_1QWRJKDOnxS19iBl9pAY8pcC",
    trialDays: 7,
    isPopular: false,
  },
];

const SubscriptionManage = ({ email }: { email: string }) => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuthStore();
  console.log(user);

  if (!user) {
    router.push("/signin");
    return null;
  }

  const handleCancelSubscription = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/cancelSubscription", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: email }),
      });

      if (!response.ok) {
        throw new Error("Failed to cancel subscription");
      }

      // Track unsubscribe event
      await trackUnsubscribe(user.id as string, user.role as string);

      toast.success("Subscription cancelled successfully");
      router.push("/homepage"); // Redirect to home page or appropriate page after cancellation
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      toast.error("Failed to cancel subscription. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto bg-[#2B3A55] text-white p-6 rounded-xl shadow-lg">
      <h2 className="text-2xl font-semibold mb-4">Manage Your Subscription</h2>
      <p className="mb-6">
        You are currently on a paid subscription plan. You have access to all
        premium features.
      </p>
      <button
        onClick={handleCancelSubscription}
        disabled={isLoading}
        className="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition-colors"
      >
        {isLoading ? "Processing..." : "Cancel Subscription"}
      </button>
    </div>
  );
};

export default function Page() {
  const { user } = useAuthStore();
  const router = useRouter();
  if (!user) {
    // This should theoretically never happen due to middleware,
    // but it's a good fail-safe and satisfies TypeScript
    router.push("/signin");
    return null;
  }

  const paidUser = user.role === "PAID_USER";

  return (
    <div className="w-full bg-[#132435] text-white flex items-center justify-center">
      <main className="flex flex-col w-[383px] md:w-[756px] lg:[1100px] text-white items-center justify-center p-4">
        <div className="mb-8 text-xl lg:text-4xl font-semibold text-center">
          {paidUser
            ? "Manage Your Subscription"
            : "Start your knowledge journey today"}
        </div>
        {paidUser ? (
          <SubscriptionManage email={user.email as string} />
        ) : (
          <>
            <div className="text-md lg:text-2xl text-center line-clamp-2  mb-10">
              Upgrade for unlimited access
            </div>
            <div className="w-[1100px] justify-center flex-col lg:flex-row items-center flex gap-4 mb-8">
              {subscriptionOptions.map((option, key) => (
                <SubscriptionCard key={key} {...option} />
              ))}
            </div>
          </>
        )}
      </main>
    </div>
  );
}
