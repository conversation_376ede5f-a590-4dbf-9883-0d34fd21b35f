"use server";

import prisma from "@/lib/prisma";

export async function unsubscribe(prevState: any, formData: FormData) {
  const email = formData.get("email") as string;
  try {
    // Remove user from the database
    await prisma.newsletterSubscriber.delete({
      where: { email: email },
    });
    return {
      success: "Successfully unsubscribed",
    };
  } catch (error) {
    console.error("Error unsubscribing:", error);
    return {
      error: "Failed to unsubscribe. Please try again.",
    };
  }
}
