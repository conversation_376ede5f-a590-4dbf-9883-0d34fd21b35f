// CategoryButton.tsx (Client Component)
"use client";

import { useClientMediaQuery } from "@/hooks/useClientMediaQuery";
import { useRouter } from "next/navigation";

const CategoryButton = ({ category }: { category: string }) => {
  const router = useRouter();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();
    e.stopPropagation();
    router.push(`/category/${category}`);
  };

  const isMobile = useClientMediaQuery("(max-width: 768px)");

  if (!isMobile) {
    return (
      <button
        onClick={handleClick}
        className="text-white overflow-x-scroll bg-gray-500 text-[11px] lg:text-sm mb-2 line-clamp-3 px-2 py-1"
      >
        {category}
      </button>
    );
  }
  return null;
};

export default CategoryButton;
