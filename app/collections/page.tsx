import React from "react";
import {
  getPlaylists,
  GetPlaylistsResponse,
} from "../catalogue/actions/getPlaylists";
import Link from "next/link";
import Image from "next/image";
import { redirect } from "next/navigation";
import CategoryButton from "./CategoryButton";
import { unstable_cache } from "next/cache";
import { getUser } from "@/lib/cache/getUser";

export const revalidate = 3600; // Revalidate the page every hour

const getCachedPlaylists = unstable_cache(
  async () => {
    return await getPlaylists();
  },
  ["playlists"],
  { revalidate: 3600 }, // Cache for 1 hour
);

export default async function PlaylistPage() {
  try {
    const user = await getUser();

    if (!user) {
      redirect("/signin");
    }

    const { playlists } = await getCachedPlaylists();

    return (
      <div className="w-full bg-[#132435] text-white ">
        <div className="w-[393px] md:[756px] lg:w-[1100px] mx-auto p-5">
          <div className="flex flex-col gap-4 md:gap-6">
            {playlists.map(
              (playlist: GetPlaylistsResponse["playlists"][number]) => (
                <Link
                  key={playlist.id}
                  className="flex flex-col h-full items-start justify-between "
                  href={`/collections/${playlist.id}`}
                >
                  <div
                    key={playlist.id}
                    className="flex bg-[#27394F] rounded-lg overflow-hidden shadow-lg  p-4 lg:p-6 h-[180px] lg:h-full w-full"
                  >
                    <div className="size-[150px] lg:size-[300px] flex justify-center items-center relative mr-2 lg:mr-6">
                      {playlist.imageUrl && (
                        <Image
                          className="rounded-2xl"
                          width={150}
                          height={150}
                          style={{
                            width: "100%",
                            height: "auto",
                          }}
                          src={playlist.imageUrl}
                          alt={playlist.name}
                        />
                      )}
                    </div>
                    <div className=" w-[193px] lg:w-3/4 px-4 h-[150px] lg:h-full py-2 relative flex items-start justify-start">
                      <div>
                        <h2 className="text-lg lg:text-2xl mb-2 line-clamp-4">
                          {playlist.name}
                        </h2>
                        <div className="flex w-full gap-2 overflow-x-scroll mt-[14px] mb-[12px]">
                          {playlist.categories.map((category, index) => (
                            <CategoryButton key={index} category={category} />
                          ))}
                        </div>
                        <p className="text-gray-200 text-[11px] lg:text-xl w-full mb-2 line-clamp-3">
                          {playlist.description}
                        </p>
                      </div>
                      <div className="bg-gray-500 text-center w-16 px-1 py-1 text-xs">
                        {playlist.articleCount} articles
                      </div>
                    </div>
                  </div>
                </Link>
              ),
            )}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error in PlaylistPage:", error);
    throw error;
  }
}
