import React from "react";

const PlaylistSkeletonItem = () => (
  <div className="flex bg-[#27394F] rounded-lg overflow-hidden shadow-lg p-4 lg:p-6 h-[180px] lg:h-full w-full animate-pulse">
    <div className="size-[150px] lg:size-[300px] flex justify-center items-center relative mr-2 lg:mr-6">
      <div className="w-full h-full bg-gray-700 rounded-2xl"></div>
    </div>
    <div className="w-[193px] lg:w-3/4 px-4 h-[150px] lg:h-full py-2 relative flex items-start justify-start">
      <div className="flex flex-col h-full items-start justify-between w-full">
        <div>
          <div className="h-6 lg:h-8 bg-gray-700 rounded w-3/4 mb-2"></div>
          <div className="flex w-full gap-2 mb-2">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="h-6 bg-gray-700 rounded w-16"></div>
            ))}
          </div>
          <div className="h-3 bg-gray-700 rounded w-full mb-1"></div>
          <div className="h-3 bg-gray-700 rounded w-2/3"></div>
        </div>
        <div className="bg-gray-700 rounded w-20 h-6"></div>
      </div>
    </div>
  </div>
);

export default function Loading() {
  return (
    <div className="w-full bg-[#132435] text-white">
      <div className="w-[393px] md:[756px] lg:w-[1100px] mx-auto p-5">
        <div className="flex flex-col gap-4 md:gap-6">
          {[...Array(5)].map((_, index) => (
            <PlaylistSkeletonItem key={index} />
          ))}
        </div>
      </div>
    </div>
  );
}
