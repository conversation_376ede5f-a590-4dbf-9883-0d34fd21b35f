import React from "react";

const ArticleSkeletonItem = () => (
  <div className="flex bg-[#27394F] rounded-lg overflow-hidden shadow-lg p-4 h-[200px] lg:h-[227px] w-full animate-pulse">
    <div className="w-[155px] lg:w-[178px] flex justify-center items-center">
      <div className="w-full h-full bg-gray-700 rounded-2xl"></div>
    </div>
    <div className="w-[200px] lg:w-3/4 px-4 relative flex items-start justify-start">
      <div className="flex flex-col h-full items-start justify-between w-full">
        <div>
          <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-700 rounded w-full mb-1"></div>
          <div className="h-3 bg-gray-700 rounded w-2/3"></div>
        </div>
        <div className="bg-gray-700 rounded w-16 h-6"></div>
      </div>
      <div className="w-6 h-6 bg-gray-700 rounded-full"></div>
    </div>
  </div>
);

export default function Loading() {
  return (
    <div className=" w-full p-5 mx-auto  bg-[#132435]">
      <div className="flex flex-col w-[393px] lg:w-[1100px] gap-4 mx-auto items-center justify-center">
        <div className="w-[220px] lg:w-[350px] flex justify-center flex-col items-center mb-4 lg:mb-8">
          <div className="w-full h-[220px] lg:h-[350px] bg-gray-700 rounded-lg mb-2 lg:mb-4"></div>
        </div>
        <div className="w-[80%] flex flex-col items-center justify-center">
          <div className="h-8 bg-gray-700 rounded w-1/2 mb-6"></div>
          <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-700 rounded w-2/3 mb-8"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 w-full">
          {[...Array(6)].map((_, index) => (
            <ArticleSkeletonItem key={index} />
          ))}
        </div>
      </div>
    </div>
  );
}
