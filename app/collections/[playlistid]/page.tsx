import React from "react";
import {
  getPlaylistWithArticles,
  PlaylistWithRelations,
} from "../../catalogue/actions/getPlaylistById";
import { redirect } from "next/navigation";
import { Image, Link } from "@nextui-org/react";
import { BookmarkButton } from "@/app/catalogue/components/BookmarkButton";
import { getUser } from "@/lib/cache/getUser";
export default async function PlaylistDetail({
  params,
}: {
  params: { playlistid: string };
}) {
  const user = await getUser();

  if (!user) {
    redirect("/signin");
  }

  const playlist = await getPlaylistWithArticles({
    userId: user.id,
    playlistId: params.playlistid,
  });

  if (!playlist) {
    return <div>Playlist not found</div>;
  }

  return (
    <div className=" w-full mx-auto  bg-[#132435] text-white">
      <div className="flex flex-col w-[393px] p-4 lg:w-[1100px] gap-4 mx-auto items-center justify-center">
        <div className="w-[200px] lg:w-[350px] flex justify-center flex-col items-center mb-4 lg:mb-8">
          <Image
            src={playlist.image?.src}
            alt={playlist.name}
            width={""}
            className="mb-2 lg:mb-4"
          />
        </div>
        <div className="w-[80%] flex flex-col items-center justify-center">
          <h1 className="text-xl lg:text-3xl font-bold mb-6">
            {playlist.name}
          </h1>
          <p className="text-white text-center text-[12px] lg:text-sm mb-8">
            {playlist.description}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          {playlist.articles.map(
            (article: PlaylistWithRelations["articles"][number]) => (
              <div
                key={article.id}
                className="flex bg-[#27394F] text-white rounded-lg overflow-hidden shadow-lg p-4  h-[200px] lg:h-[227px] w-full"
              >
                <div className="w-[155px] lg:w-[178px] flex justify-center items-centerrelative">
                  {article && (
                    <Image
                      className="rounded-2xl"
                      src={article.articleImage?.src}
                      alt={article.title}
                    />
                  )}
                </div>
                <div className=" w-[200px] lg:w-3/4 px-4 relative flex items-start justify-start">
                  <Link
                    className="flex flex-col h-full items-start justify-between "
                    href={`/article/${article.slug}`}
                  >
                    <div>
                      <h2 className="text-[13px] lg:text-xl font-semibold mb-2 line-clamp-4 text-white">
                        {article.title}
                      </h2>
                      <p className="text-white text-[11px] lg:text-sm mb-2 line-clamp-3">
                        {article.subtitle}
                      </p>
                    </div>
                    <div className="bg-gray-500 text-center w-16 px-1 py-1 text-xs text-white">
                      {article.estimatedReadingTime} min
                    </div>
                  </Link>
                </div>
                <div className="flex items-start">
                  <BookmarkButton
                    articleId={article.id}
                    initialIsBookmarked={article.initialIsBookmarked}
                  />
                </div>
              </div>
            ),
          )}
        </div>
      </div>
    </div>
  );
}
