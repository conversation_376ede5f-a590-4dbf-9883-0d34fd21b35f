import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface InitialLandingProps {
  onQuerySubmit: (query: string) => void;
}

export function InitialLanding({ onQuerySubmit }: InitialLandingProps) {
  const [input, setInput] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;
    onQuerySubmit(input.trim());
  };

  const exampleQueries = [
    "How does climate change affect biodiversity?",
    "What's new in quantum computing research?",
    "Recent advances in cancer immunotherapy",
    "Machine learning applications in drug discovery"
  ];

  return (
    <motion.div
      initial={{ opacity: 1 }}
      exit={{ opacity: 0, y: -50 }}
      className="min-h-screen bg-background flex flex-col items-center justify-center p-8"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-12 max-w-2xl"
      >
        <div className="w-20 h-20 bg-primary rounded-2xl flex items-center justify-center mx-auto mb-6">
          <div className="text-primary-foreground font-bold text-2xl">D</div>
        </div>

        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Welcome to Darwin
        </h1>

        <p className="text-xl text-muted-foreground mb-8">
          Your AI research assistant. Ask complex research questions and get
          comprehensive answers with relevant citations from the latest academic literature.
        </p>

        <div className="flex items-center gap-2 justify-center text-sm text-muted-foreground mb-8">
          <Sparkles className="w-4 h-4 text-primary" />
          <span>Powered by advanced AI and real-time research analysis</span>
        </div>
      </motion.div>

      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        onSubmit={handleSubmit}
        className="w-full max-w-2xl"
      >
        <div className="relative">
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask a research question..."
            className="chat-input resize-none pr-12 min-h-[60px] text-lg"
            rows={3}
          />
          <Button
            type="submit"
            disabled={!input.trim()}
            className="absolute right-3 bottom-3 w-10 h-10 p-0 rounded-xl"
          >
            <Send className="w-5 h-5" />
          </Button>
        </div>
      </motion.form>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="mt-12 w-full max-w-2xl"
      >
        <p className="text-sm text-muted-foreground mb-4 text-center">
          Try one of these examples:
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {exampleQueries.map((query, index) => (
            <motion.button
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
              onClick={() => setInput(query)}
              className="text-left p-3 rounded-lg border border-border hover:bg-secondary/50 transition-colors text-sm"
            >
              "{query}"
            </motion.button>
          ))}
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
        className="mt-16 text-center"
      >
        <div className="flex items-center gap-6 text-xs text-muted-foreground">
          <span className="flex items-center gap-1">
            <div className="w-2 h-2 bg-success rounded-full" />
            Real-time citations
          </span>
          <span className="flex items-center gap-1">
            <div className="w-2 h-2 bg-primary rounded-full" />
            Streaming responses
          </span>
          <span className="flex items-center gap-1">
            <div className="w-2 h-2 bg-accent rounded-full" />
            Academic sources
          </span>
        </div>
      </motion.div>
    </motion.div>
  );
}