import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Brain, Sparkles, Clock, MoreVertical } from 'lucide-react';
import { ChatMessage, Citation, FilterOptions } from '../types';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { FollowUpSuggestions } from './FollowUpSuggestions';
import { ClarifyModal } from './ClarifyModal';
import { RatingWidget } from './RatingWidget';

interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (content: string) => void;
  isLoading?: boolean;
  mode: 'deep' | 'summary';
  onModeChange: (mode: 'deep' | 'summary') => void;
  onCitationClick?: (citation: Citation) => void;
  onRatingSubmit?: (messageId: string, rating: number) => void;
}

export function ChatInterface({
  messages,
  onSendMessage,
  isLoading = false,
  mode,
  onModeChange,
  onCitationClick,
  onRatingSubmit
}: ChatInterfaceProps) {
  const [input, setInput] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [showClarifyModal, setShowClarifyModal] = useState(false);
  const [clarifyQuery, setClarifyQuery] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  // messagesEndRef removed - no auto-scroll needed
  const { toast } = useToast();

  // Follow-up suggestions based on the last assistant message
  const followUpSuggestions = messages.length > 0 && messages[messages.length - 1].role === 'assistant'
    ? [
      "Can you provide more specific examples?",
      "What are the current limitations of this approach?",
      "How does this compare to alternative methods?",
      "What are the future research directions?"
    ]
    : [];

  // Clarification suggestions for short queries
  const getClarificationSuggestions = (query: string) => {
    const lowerQuery = query.toLowerCase();
    if (lowerQuery.includes('crispr') || lowerQuery.includes('gene')) {
      return [
        "What are the latest CRISPR applications in treating genetic diseases?",
        "How has CRISPR technology evolved in the past year?",
        "What are the ethical considerations around CRISPR gene editing?"
      ];
    }
    if (lowerQuery.includes('climate') || lowerQuery.includes('environment')) {
      return [
        "What are the most recent findings on climate change impacts?",
        "How are researchers addressing climate adaptation strategies?",
        "What new technologies are being developed for carbon capture?"
      ];
    }
    return [
      "Could you be more specific about what aspect interests you most?",
      "Are you looking for recent research or historical context?",
      "Would you like to focus on a particular application or theory?"
    ];
  };

  // Auto-scroll removed per user request

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    // Check for very short queries
    if (input.trim().split(' ').length < 3) {
      setClarifyQuery(input.trim());
      setShowClarifyModal(true);
      return;
    }

    onSendMessage(input.trim());
    setInput('');

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  const handleClarificationSelect = (suggestion: string) => {
    setInput(suggestion);
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  const handleFollowUpClick = (suggestion: string) => {
    onSendMessage(suggestion);
  };

  const handleRating = (messageId: string, rating: number) => {
    onRatingSubmit?.(messageId, rating);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };

  const parseCitations = (content: string) => {
    // More robust regex that handles various malformed citation patterns
    const citationRegex = /(?:\[(\d+)\])?\|?INSIGHT\|?>?(\d+)\|\|([^|]+)\|\|([^|]+)\|\|([^|>]+)>?/g;
    const parts = [];
    let lastIndex = 0;
    let match;
    let citationIndex = 1;

    while ((match = citationRegex.exec(content)) !== null) {
      // Add text before citation
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: content.slice(lastIndex, match.index)
        });
      }

      // Extract citation data (match[1] is optional [1], match[2] is ID, etc.)
      const citationId = match[2] || match[1] || String(citationIndex - 1);
      const title = match[3] || 'Unknown Title';
      const authors = match[4] || 'Unknown Authors';
      const url = match[5] || 'https://example.com';

      // Add citation
      parts.push({
        type: 'citation',
        id: citationId,
        title: title,
        authors: authors,
        url: url,
        index: citationIndex++
      });

      lastIndex = citationRegex.lastIndex;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      parts.push({
        type: 'text',
        content: content.slice(lastIndex)
      });
    }

    return parts;
  };

  const renderMessage = (message: ChatMessage, index: number) => {
    const parts = parseCitations(message.content);

    return (
      <motion.div
        key={message.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.1 }}
        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} mb-6`}
      >
        <div className={`max-w-[85%] ${message.role === 'user'
          ? 'chat-message-user'
          : 'chat-message-assistant'
          }`}>
          {message.role === 'assistant' && (
            <div className="flex items-center gap-2 mb-2 text-sm text-muted-foreground">
              <Brain className="w-4 h-4" />
              <span>Darwin Research Assistant</span>
              <Clock className="w-3 h-3 ml-auto" />
              <span>{message.timestamp.toLocaleTimeString()}</span>
            </div>
          )}

          <div className="prose prose-sm max-w-none">
            {parts.map((part, partIndex) => (
              part.type === 'text' ? (
                <span key={partIndex}>{part.content}</span>
              ) : (
                <button
                  key={partIndex}
                  onClick={() => onCitationClick?.({
                    id: part.id,
                    title: part.title,
                    authors: (part.authors || '').split(', ').filter(Boolean),
                    url: part.url
                  } as Citation)}
                  className="citation-inline mx-1 hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-primary/50"
                  title={part.title}
                >
                  [{part.index}]
                </button>
              )
            ))}
          </div>

          {message.isStreaming && (
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="flex items-center gap-1 mt-2 text-xs text-muted-foreground"
            >
              <div className="w-1 h-1 bg-primary rounded-full" />
              <div className="w-1 h-1 bg-primary rounded-full" />
              <div className="w-1 h-1 bg-primary rounded-full" />
              <span className="ml-1">Analyzing research...</span>
            </motion.div>
          )}

          {/* Rating Widget for completed assistant messages */}
          {message.role === 'assistant' && !message.isStreaming && (
            <div className="mt-3">
              <RatingWidget
                messageId={message.id}
                onRatingSubmit={handleRating}
              />
            </div>
          )}
        </div>
      </motion.div>
    );
  };

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Header with mode toggle */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Brain className="w-6 h-6 text-primary" />
            <h1 className="text-xl font-semibold">Darwin</h1>
          </div>
          <div className="text-sm text-muted-foreground">
            Research Assistant
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex bg-secondary rounded-lg p-1">
            <button
              onClick={() => onModeChange('summary')}
              className={`px-3 py-1 text-sm font-medium rounded transition-all ${mode === 'summary'
                ? 'bg-primary text-primary-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
                }`}
            >
              Summary
            </button>
            <button
              onClick={() => onModeChange('deep')}
              className={`px-3 py-1 text-sm font-medium rounded transition-all ${mode === 'deep'
                ? 'bg-primary text-primary-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
                }`}
            >
              Deep Dive
            </button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        <AnimatePresence>
          {messages.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center py-16"
            >
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-primary" />
              </div>
              <h2 className="text-2xl font-semibold mb-2">Welcome to Darwin</h2>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Your AI research assistant. Ask complex research questions and get
                comprehensive answers with relevant citations.
              </p>
              <div className="flex flex-wrap gap-2 justify-center">
                {[
                  "How does climate change affect biodiversity?",
                  "What's new in quantum computing research?"
                ].map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => setInput(suggestion)}
                    className="btn-ghost text-sm"
                  >
                    "{suggestion}"
                  </button>
                ))}
              </div>
            </motion.div>
          ) : (
            <>
              {messages.map((message, index) => renderMessage(message, index))}

              {/* Follow-up suggestions after assistant messages */}
              {followUpSuggestions.length > 0 && !isLoading && (
                <FollowUpSuggestions
                  suggestions={followUpSuggestions}
                  onSuggestionClick={handleFollowUpClick}
                />
              )}
            </>
          )}
        </AnimatePresence>
        {/* messagesEndRef removed - no auto-scroll */}
      </div>

      {/* Input */}
      <div className="border-t border-border p-4">
        <form onSubmit={handleSubmit} className="relative">
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => {
                setInput(e.target.value);
                adjustTextareaHeight();
              }}
              onKeyDown={handleKeyDown}
              onCompositionStart={() => setIsComposing(true)}
              onCompositionEnd={() => setIsComposing(false)}
              placeholder="Ask a research question..."
              className="chat-input resize-none pr-12 min-h-[52px] max-h-[120px] text-foreground placeholder:text-muted-foreground"
              disabled={isLoading}
            />
            <Button
              type="submit"
              size="sm"
              disabled={!input.trim() || isLoading}
              className="absolute right-2 bottom-2 w-8 h-8 p-0 rounded-lg"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </form>

        {input.trim().split(' ').length < 3 && input.trim().length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-2 text-xs text-muted-foreground"
          >
            💡 Try adding more context for better research insights
          </motion.div>
        )}
      </div>

      {/* Clarification Modal */}
      <ClarifyModal
        isOpen={showClarifyModal}
        onClose={() => setShowClarifyModal(false)}
        query={clarifyQuery}
        suggestions={getClarificationSuggestions(clarifyQuery)}
        onSuggestionSelect={handleClarificationSelect}
      />
    </div>
  );
}