import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Moon, Sun, Settings, Sidebar } from 'lucide-react';
import { ChatInterface } from './ChatInterface';
import { InsightSidebar } from './InsightSidebar';
import { InitialLanding } from './InitialLanding';
import { ChatMessage, Citation, FilterOptions } from '../types';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

export function Darwin() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [citations, setCitations] = useState<Citation[]>([]);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [mode, setMode] = useState<'deep' | 'summary'>('deep');
  const [isLoading, setIsLoading] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [showInitialLanding, setShowInitialLanding] = useState(true);
  const [highlightedCitationId, setHighlightedCitationId] = useState<string | null>(null);
  const { toast } = useToast();

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Theme management
  useEffect(() => {
    const root = document.documentElement;
    if (isDarkMode) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [isDarkMode]);

  const handleSendMessage = useCallback(async (content: string) => {
    if (isLoading) return;

    // Hide initial landing on first message
    if (showInitialLanding) {
      setShowInitialLanding(false);
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      await startChatStream(content);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to get response. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, showInitialLanding, toast]);

  const handleCitationClick = useCallback((citation: Citation) => {
    // Highlight the citation in the sidebar
    setHighlightedCitationId(citation.id);

    // Clear highlight after 3 seconds
    setTimeout(() => {
      setHighlightedCitationId(null);
    }, 3000);

    toast({
      title: citation.title,
      description: `${citation.authors.join(', ')} - ${citation.journal} (${citation.year})`,
      duration: 5000
    });
  }, [toast]);

  const handleExport = useCallback((format: 'bibtex' | 'endnote' | 'csv') => {
    toast({
      title: "Export Started",
      description: `Exporting citations in ${format.toUpperCase()} format...`,
    });
  }, [toast]);

  const handleRatingSubmit = useCallback((messageId: string, rating: number) => {
    toast({
      title: "Feedback Recorded",
      description: `Thanks for rating this response ${rating} stars!`,
    });
    // Here you would typically send the rating to your analytics service
  }, [toast]);

  /**
   * Stream response from /api/chat via SSE
   */
  const startChatStream = async (query: string) => {
    const assistantId = `assistant-${Date.now()}`;

    // optimistic assistant bubble
    setMessages(prev => [
      ...prev,
      {
        id: assistantId,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        isStreaming: true,
      },
    ]);

    try {
      // Build conversation history for context
      const conversationHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const res = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: query,
          mode,
          conversationHistory,
          existingCitations: citations
        }),
      });

      if (!res.ok || !res.body) throw new Error('Network error');

      const reader = res.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let accumulated = '';

      const pushContent = (chunk: string) => {
        accumulated += chunk;
        setMessages(prev => prev.map(m => m.id === assistantId ? { ...m, content: accumulated } : m));
      };

      const pushCitations = (cites: Citation[]) => {
        if (!cites?.length) {
          console.log('No citations received or empty array');
          return;
        }
        console.log('DEBUG: Received citations:', cites.length, cites);
        console.log('DEBUG: First citation structure:', JSON.stringify(cites[0], null, 2));
        setCitations(prev => {
          const ids = new Set(prev.map(c => c.id));
          const newCitations = [...prev, ...cites.filter(c => !ids.has(c.id))];
          console.log('DEBUG: Updated citations state:', newCitations.length);
          console.log('DEBUG: Citation IDs:', newCitations.map(c => c.id));
          return newCitations;
        });
        setMessages(prev => prev.map(m => m.id === assistantId ? { ...m, citations: cites } : m));
      };

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;
        buffer += decoder.decode(value, { stream: true });
        const parts = buffer.split('\n\n');
        buffer = parts.pop() || '';
        for (const p of parts) {
          if (!p.startsWith('data: ')) continue;
          const data = JSON.parse(p.slice(6));
          if (data.content) pushContent(data.content);
          if (data.citations) pushCitations(data.citations);
          if (data.isComplete) {
            setMessages(prev => prev.map(m => m.id === assistantId ? { ...m, isStreaming: false } : m));
          }
        }
      }
    } catch (err) {
      console.error(err);
      toast({ title: 'Error', description: 'Failed to fetch answer', variant: 'destructive' });
      setMessages(prev => prev.filter(m => m.id !== assistantId));
    }
  };

  return (
    <div className="h-screen bg-background text-foreground overflow-hidden">
      <AnimatePresence mode="wait">
        {showInitialLanding ? (
          <motion.div key="landing">
            <InitialLanding onQuerySubmit={handleSendMessage} />
          </motion.div>
        ) : (
          <motion.div
            key="chat"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="h-full"
          >
            {/* Header */}
            <header className="h-14 bg-card border-b border-border flex items-center justify-between px-4 z-10 relative">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                  className="flex items-center gap-2"
                >
                  <Sidebar className="w-4 h-4" />
                  <span className="hidden sm:inline text-sm">
                    {isSidebarOpen ? 'Hide' : 'Show'} Insights
                  </span>
                </Button>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center gap-2"
                >
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <span className="text-primary-foreground font-bold text-sm">D</span>
                  </div>
                  <div>
                    <h1 className="font-semibold text-lg">Darwin</h1>
                    <p className="text-xs text-muted-foreground">Research Assistant</p>
                  </div>
                </motion.div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsDarkMode(!isDarkMode)}
                >
                  {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
                </Button>

                <Button variant="ghost" size="sm">
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </header>

            {/* Main Content */}
            <div className="h-[calc(100vh-3.5rem)] flex">
              {/* Chat Interface */}
              <motion.div
                layout
                className={`flex-1 ${isSidebarOpen && !isMobile ? 'lg:w-2/3' : 'w-full'} transition-all duration-300`}
              >
                <ChatInterface
                  messages={messages}
                  onSendMessage={handleSendMessage}
                  isLoading={isLoading}
                  mode={mode}
                  onModeChange={setMode}
                  onCitationClick={handleCitationClick}
                  onRatingSubmit={handleRatingSubmit}
                />
              </motion.div>

              {/* Insight Sidebar */}
              <motion.div
                layout
                initial={false}
                animate={{
                  width: isSidebarOpen ? (isMobile ? '100%' : '33.333333%') : '0%',
                  opacity: isSidebarOpen ? 1 : 0
                }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className={`${isMobile
                  ? 'absolute top-0 right-0 bottom-0 z-20 bg-background'
                  : 'relative'
                  } overflow-hidden`}
              >
                {isSidebarOpen && (
                  <InsightSidebar
                    citations={citations}
                    filters={filters}
                    onFiltersChange={setFilters}
                    onCitationClick={handleCitationClick}
                    onExport={handleExport}
                    highlightedCitationId={highlightedCitationId}
                    className="w-full h-full"
                  />
                )}
              </motion.div>

              {/* Mobile Overlay */}
              {isMobile && isSidebarOpen && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10"
                  onClick={() => setIsSidebarOpen(false)}
                />
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}