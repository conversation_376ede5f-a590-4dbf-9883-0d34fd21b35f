import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HelpCircle, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface ClarifyModalProps {
  isOpen: boolean;
  onClose: () => void;
  query: string;
  suggestions: string[];
  onSuggestionSelect: (suggestion: string) => void;
}

export function ClarifyModal({
  isOpen,
  onClose,
  query,
  suggestions,
  onSuggestionSelect
}: ClarifyModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="w-5 h-5 text-primary" />
            Could you be more specific?
          </DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-4">
            Your query "{query}" is quite brief. Here are some specific questions you might be asking:
          </p>

          <div className="space-y-2">
            {suggestions.map((suggestion, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Button
                  variant="outline"
                  onClick={() => {
                    onSuggestionSelect(suggestion);
                    onClose();
                  }}
                  className="w-full justify-start text-left h-auto p-3"
                >
                  <div className="text-sm">
                    <div className="font-medium mb-1">
                      Did you mean:
                    </div>
                    <div className="text-muted-foreground">
                      {suggestion}
                    </div>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>

          <div className="mt-4 pt-4 border-t border-border">
            <Button
              variant="ghost"
              onClick={onClose}
              className="w-full"
            >
              Continue with original query
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}