import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Lightbulb } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface FollowUpSuggestionsProps {
  suggestions: string[];
  onSuggestionClick: (suggestion: string) => void;
  className?: string;
}

export function FollowUpSuggestions({
  suggestions,
  onSuggestionClick,
  className = ''
}: FollowUpSuggestionsProps) {
  if (suggestions.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`mb-6 ${className}`}
    >
      <div className="flex items-center gap-2 mb-3">
        <Lightbulb className="w-4 h-4 text-primary" />
        <span className="text-sm font-medium text-muted-foreground">
          You might ask next:
        </span>
      </div>
      
      <div className="space-y-2">
        {suggestions.map((suggestion, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Button
              variant="outline"
              onClick={() => onSuggestionClick(suggestion)}
              className="w-full justify-start text-left h-auto p-3 text-sm hover:bg-secondary/50 transition-all duration-200"
            >
              <div className="flex items-center gap-2 w-full">
                <span className="flex-1">{suggestion}</span>
                <ArrowRight className="w-3 h-3 text-muted-foreground flex-shrink-0" />
              </div>
            </Button>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}