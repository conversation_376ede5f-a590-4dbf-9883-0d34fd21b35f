import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Star, ThumbsUp, ThumbsDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface RatingWidgetProps {
  messageId: string;
  onRatingSubmit: (messageId: string, rating: number) => void;
  className?: string;
}

export function RatingWidget({
  messageId,
  onRatingSubmit,
  className = ''
}: RatingWidgetProps) {
  const [rating, setRating] = useState<number | null>(null);
  const [hasRated, setHasRated] = useState(false);
  const [hoveredStar, setHoveredStar] = useState<number | null>(null);
  const { toast } = useToast();

  const handleStarClick = (starRating: number) => {
    setRating(starRating);
    setHasRated(true);
    onRatingSubmit(messageId, starRating);

    toast({
      title: "Thank you for your feedback!",
      description: `You rated this response ${starRating} star${starRating !== 1 ? 's' : ''}.`,
      duration: 2000,
    });
  };

  const handleThumbsClick = (isPositive: boolean) => {
    const thumbsRating = isPositive ? 5 : 1;
    setRating(thumbsRating);
    setHasRated(true);
    onRatingSubmit(messageId, thumbsRating);

    toast({
      title: "Thank you for your feedback!",
      description: isPositive ? "We're glad you found this helpful!" : "We'll work on improving our responses.",
      duration: 2000,
    });
  };

  if (hasRated) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`flex items-center gap-2 text-sm text-muted-foreground ${className}`}
      >
        <span>Thanks for your feedback!</span>
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <Star
              key={star}
              className={`w-3 h-3 ${star <= (rating || 0)
                  ? 'fill-primary text-primary'
                  : 'text-muted-foreground'
                }`}
            />
          ))}
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex items-center gap-3 p-2 rounded-lg bg-gray-50 border border-gray-200 ${className}`}
    >
      <span className="text-xs text-muted-foreground">Rate this response:</span>

      {/* Star Rating */}
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => handleStarClick(star)}
            onMouseEnter={() => setHoveredStar(star)}
            onMouseLeave={() => setHoveredStar(null)}
            className="transition-transform hover:scale-110"
          >
            <Star
              className={`w-4 h-4 transition-colors ${star <= (hoveredStar || 0)
                  ? 'fill-primary text-primary'
                  : 'text-muted-foreground hover:text-primary'
                }`}
            />
          </button>
        ))}
      </div>

      <div className="w-px h-4 bg-border" />

      {/* Quick Thumbs */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleThumbsClick(true)}
          className="h-7 w-7 p-0 hover:bg-success/20"
        >
          <ThumbsUp className="w-3 h-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleThumbsClick(false)}
          className="h-7 w-7 p-0 hover:bg-destructive/20"
        >
          <ThumbsDown className="w-3 h-3" />
        </Button>
      </div>
    </motion.div>
  );
}