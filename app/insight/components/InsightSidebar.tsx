import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ExternalLink,
  Users,
  Calendar,
  Star,
  Filter,
  Download,
  BookOpen,
  TrendingUp,
  Bookmark,
  FileText
} from 'lucide-react';
import { Citation, FilterOptions } from '../types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';

interface InsightSidebarProps {
  citations: Citation[];
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  onCitationClick: (citation: Citation) => void;
  onExport: (format: 'bibtex' | 'endnote' | 'csv') => void;
  highlightedCitationId?: string | null;
  className?: string;
}

export function InsightSidebar({
  citations,
  filters,
  onFiltersChange,
  onCitationClick,
  onExport,
  highlightedCitationId,
  className = ''
}: InsightSidebarProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCitations, setSelectedCitations] = useState<Set<string>>(new Set());

  const filteredCitations = useMemo(() => {
    return citations.filter(citation => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          citation.title.toLowerCase().includes(searchLower) ||
          citation.authors.some(author => author.toLowerCase().includes(searchLower)) ||
          citation.journal?.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Year range filter
      if (filters.yearRange && citation.year) {
        const [minYear, maxYear] = filters.yearRange;
        if (citation.year < minYear || citation.year > maxYear) return false;
      }

      // Impact factor filter
      if (filters.minImpactFactor && citation.impactFactor) {
        if (citation.impactFactor < filters.minImpactFactor) return false;
      }

      // Novelty score filter
      if (filters.minNoveltyScore && citation.noveltyScore) {
        if (citation.noveltyScore < filters.minNoveltyScore) return false;
      }

      // Open access filter
      if (filters.openAccessOnly && !citation.isOpenAccess) return false;

      return true;
    });
  }, [citations, filters, searchTerm]);

  const toggleCitationSelection = (citationId: string) => {
    const newSelected = new Set(selectedCitations);
    if (newSelected.has(citationId)) {
      newSelected.delete(citationId);
    } else {
      newSelected.add(citationId);
    }
    setSelectedCitations(newSelected);
  };

  const getRelevanceColor = (score?: number) => {
    if (!score) return 'bg-muted';
    if (score >= 0.8) return 'bg-success';
    if (score >= 0.6) return 'bg-warning';
    return 'bg-destructive';
  };

  const citationVariants = {
    hidden: { opacity: 0, x: 20, scale: 0.95 },
    visible: (index: number) => ({
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.3,
        delay: index * 0.1,
        ease: "easeOut"
      }
    }),
    exit: { opacity: 0, x: -20, scale: 0.95 }
  };

  return (
    <div className={`bg-card border-l border-border flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-primary" />
            Insights ({filteredCitations.length})
          </h2>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? 'bg-secondary' : ''}
            >
              <Filter className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onExport('bibtex')}
              disabled={selectedCitations.size === 0}
            >
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search citations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="mt-4 space-y-4 overflow-hidden"
            >
              <div>
                <label className="text-sm font-medium mb-2 block">Year Range</label>
                <Slider
                  value={filters.yearRange || [1990, 2024]}
                  onValueChange={(value) => onFiltersChange({
                    ...filters,
                    yearRange: value as [number, number]
                  })}
                  min={1990}
                  max={2024}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{filters.yearRange?.[0] || 1990}</span>
                  <span>{filters.yearRange?.[1] || 2024}</span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Min Impact Factor</label>
                <Slider
                  value={[filters.minImpactFactor || 0]}
                  onValueChange={(value) => onFiltersChange({
                    ...filters,
                    minImpactFactor: value[0]
                  })}
                  min={0}
                  max={50}
                  step={0.1}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground mt-1">
                  {filters.minImpactFactor?.toFixed(1) || '0.0'}+
                </div>
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Open Access Only</label>
                <Switch
                  checked={filters.openAccessOnly || false}
                  onCheckedChange={(checked) => onFiltersChange({
                    ...filters,
                    openAccessOnly: checked
                  })}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Citations List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {/* Debug info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="text-xs text-muted-foreground mb-2 p-2 bg-muted rounded">
            Debug: {citations.length} citations, {filteredCitations.length} filtered
            <br />
            Citation IDs: {citations.map(c => c.id).join(', ')}
            <br />
            Filtered IDs: {filteredCitations.map(c => c.id).join(', ')}
          </div>
        )}

        <AnimatePresence mode="popLayout">
          {filteredCitations.map((citation, index) => (
            <motion.div
              key={citation.id}
              custom={index}
              variants={citationVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              layout
              className={`citation-card cursor-pointer ${highlightedCitationId === citation.id ? 'citation-card-highlighted' : ''
                }`}
              onClick={() => onCitationClick(citation)}
            >
              {/* Citation Header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <h3 className="font-medium text-sm leading-tight line-clamp-2 mb-1">
                    {citation.title}
                  </h3>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Users className="w-3 h-3" />
                    <span className="line-clamp-1">
                      {citation.authors.slice(0, 3).join(', ')}
                      {citation.authors.length > 3 && ` +${citation.authors.length - 3}`}
                    </span>
                  </div>
                </div>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleCitationSelection(citation.id);
                  }}
                  className={`w-4 h-4 rounded border transition-colors ${selectedCitations.has(citation.id)
                    ? 'bg-primary border-primary'
                    : 'border-border hover:border-primary'
                    }`}
                >
                  {selectedCitations.has(citation.id) && (
                    <div className="w-2 h-2 bg-primary-foreground rounded-sm m-0.5" />
                  )}
                </button>
              </div>

              {/* Citation Metadata */}
              <div className="flex items-center gap-2 mb-3">
                {citation.year && (
                  <Badge variant="secondary" className="text-xs">
                    <Calendar className="w-3 h-3 mr-1" />
                    {citation.year}
                  </Badge>
                )}

                {citation.relevanceScore && (
                  <Badge variant="secondary" className="text-xs">
                    <div className={`w-2 h-2 rounded-full mr-1 ${getRelevanceColor(citation.relevanceScore)}`} />
                    {Math.round(citation.relevanceScore * 100)}%
                  </Badge>
                )}

                {citation.isOpenAccess && (
                  <Badge variant="outline" className="text-xs text-success border-success">
                    Open Access
                  </Badge>
                )}
              </div>

              {/* Citation Details */}
              {citation.journal && (
                <div className="text-xs text-muted-foreground mb-2">
                  <span className="font-medium">{citation.journal}</span>
                  {citation.impactFactor && (
                    <span className="ml-2">IF: {citation.impactFactor.toFixed(1)}</span>
                  )}
                </div>
              )}

              {/* Citation Actions */}
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(citation.url, '_blank');
                  }}
                  className="h-7 px-2 text-xs"
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  View
                </Button>

                <div className="flex items-center gap-1">
                  {citation.noveltyScore && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <TrendingUp className="w-3 h-3" />
                      <span>{Math.round(citation.noveltyScore * 100)}%</span>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {filteredCitations.length === 0 && citations.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8 text-muted-foreground"
          >
            <Filter className="w-8 h-8 mx-auto mb-2" />
            <p>No citations match your filters</p>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onFiltersChange({})}
              className="mt-2"
            >
              Clear filters
            </Button>
          </motion.div>
        )}

        {citations.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8 text-muted-foreground"
          >
            <BookOpen className="w-8 h-8 mx-auto mb-2" />
            <p>Citations will appear here as you chat</p>
          </motion.div>
        )}
      </div>

      {/* Footer Actions */}
      {selectedCitations.size > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 border-t border-border bg-muted/50"
        >
          <div className="text-xs text-muted-foreground mb-2">
            {selectedCitations.size} selected
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onExport('bibtex')}
              className="flex-1"
            >
              Export BibTeX
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onExport('csv')}
              className="flex-1"
            >
              Export CSV
            </Button>
          </div>
        </motion.div>
      )}
    </div>
  );
}