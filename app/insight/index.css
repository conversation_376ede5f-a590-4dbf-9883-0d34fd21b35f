@tailwind base;
@tailwind components;
@tailwind utilities;

/* Darwin Research Chat - Design System inspired by modern research tools */

@layer base {
  :root {
    /* Core brand colors - sophisticated research theme */
    --background: 0 0% 100%;
    --foreground: 224 15% 16%;
    
    /* Interactive surfaces */
    --card: 0 0% 100%;
    --card-foreground: 224 15% 16%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 224 15% 16%;
    
    /* Primary - Research blue inspired by academic tools */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 217 91% 55%;
    --primary-subtle: 217 91% 95%;
    
    /* Secondary - Warm gray for balance */
    --secondary: 220 13% 96%;
    --secondary-foreground: 224 15% 25%;
    --secondary-hover: 220 13% 91%;
    
    /* Muted - Clean, minimal gray palette */
    --muted: 220 13% 96%;
    --muted-foreground: 220 9% 46%;
    
    /* Accent - Citation highlight color */
    --accent: 45 93% 47%;
    --accent-foreground: 0 0% 100%;
    --accent-subtle: 45 93% 95%;
    
    /* Success - Research validation green */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --success-subtle: 142 71% 95%;
    
    /* Warning - Review needed orange */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --warning-subtle: 38 92% 95%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --destructive-subtle: 0 84% 95%;
    
    /* Borders and inputs */
    --border: 220 13% 91%;
    --border-subtle: 220 13% 95%;
    --input: 0 0% 100%;
    --input-border: 220 13% 91%;
    --ring: 217 91% 60%;
    
    /* Chat-specific colors */
    --chat-user: 217 91% 60%;
    --chat-user-foreground: 0 0% 100%;
    --chat-assistant: 220 13% 96%;
    --chat-assistant-foreground: 224 15% 16%;
    
    /* Citation colors */
    --citation-bg: 45 93% 95%;
    --citation-border: 45 93% 47%;
    --citation-text: 45 100% 25%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(217 91% 55%) 100%);
    --gradient-subtle: linear-gradient(135deg, hsl(220 13% 96%) 0%, hsl(220 13% 91%) 100%);
    --gradient-citation: linear-gradient(135deg, hsl(45 93% 95%) 0%, hsl(45 93% 90%) 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(220 13% 91% / 0.5);
    --shadow-md: 0 4px 6px -1px hsl(220 13% 91% / 0.1), 0 2px 4px -1px hsl(220 13% 91% / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(220 13% 91% / 0.1), 0 4px 6px -2px hsl(220 13% 91% / 0.05);
    --shadow-chat: 0 4px 12px hsl(217 91% 60% / 0.08);
    --shadow-citation: 0 2px 8px hsl(45 93% 47% / 0.12);
    
    /* Spacing and layout */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;
    
    /* Typography */
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode - sophisticated research theme */
    --background: 224 15% 8%;
    --foreground: 0 0% 95%;
    
    --card: 224 15% 10%;
    --card-foreground: 0 0% 95%;
    
    --popover: 224 15% 10%;
    --popover-foreground: 0 0% 95%;
    
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 217 91% 65%;
    --primary-subtle: 217 91% 15%;
    
    --secondary: 224 15% 12%;
    --secondary-foreground: 0 0% 85%;
    --secondary-hover: 224 15% 15%;
    
    --muted: 224 15% 12%;
    --muted-foreground: 220 9% 55%;
    
    --accent: 45 93% 55%;
    --accent-foreground: 224 15% 8%;
    --accent-subtle: 45 93% 15%;
    
    --success: 142 71% 50%;
    --success-foreground: 224 15% 8%;
    --success-subtle: 142 71% 15%;
    
    --warning: 38 92% 55%;
    --warning-foreground: 224 15% 8%;
    --warning-subtle: 38 92% 15%;
    
    --destructive: 0 84% 65%;
    --destructive-foreground: 224 15% 8%;
    --destructive-subtle: 0 84% 15%;
    
    --border: 224 15% 15%;
    --border-subtle: 224 15% 12%;
    --input: 224 15% 10%;
    --input-border: 224 15% 15%;
    --ring: 217 91% 60%;
    
    --chat-user: 217 91% 60%;
    --chat-user-foreground: 0 0% 100%;
    --chat-assistant: 224 15% 12%;
    --chat-assistant-foreground: 0 0% 95%;
    
    --citation-bg: 45 93% 15%;
    --citation-border: 45 93% 55%;
    --citation-text: 45 93% 75%;
    
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(217 91% 65%) 100%);
    --gradient-subtle: linear-gradient(135deg, hsl(224 15% 12%) 0%, hsl(224 15% 15%) 100%);
    --gradient-citation: linear-gradient(135deg, hsl(45 93% 15%) 0%, hsl(45 93% 18%) 100%);
    
    --shadow-sm: 0 1px 2px 0 hsl(224 15% 5% / 0.8);
    --shadow-md: 0 4px 6px -1px hsl(224 15% 5% / 0.3), 0 2px 4px -1px hsl(224 15% 5% / 0.2);
    --shadow-lg: 0 10px 15px -3px hsl(224 15% 5% / 0.3), 0 4px 6px -2px hsl(224 15% 5% / 0.2);
    --shadow-chat: 0 4px 12px hsl(217 91% 60% / 0.15);
    --shadow-citation: 0 2px 8px hsl(45 93% 55% / 0.2);
    
    --sidebar-background: 224 15% 8%;
    --sidebar-foreground: 0 0% 85%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 224 15% 12%;
    --sidebar-accent-foreground: 0 0% 85%;
    --sidebar-border: 224 15% 15%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "cv03", "cv04", "cv11";
  }
}

@layer components {
  /* Chat-specific components */
  .chat-message-user {
    @apply bg-[hsl(var(--chat-user))] text-[hsl(var(--chat-user-foreground))] rounded-2xl px-4 py-3 max-w-[80%] ml-auto;
    box-shadow: var(--shadow-chat);
  }
  
  .chat-message-assistant {
    @apply bg-[hsl(var(--chat-assistant))] text-[hsl(var(--chat-assistant-foreground))] rounded-2xl px-4 py-3 max-w-[85%];
    box-shadow: var(--shadow-sm);
  }
  
  .chat-input {
    @apply bg-[hsl(var(--card))] border-[hsl(var(--border))] rounded-xl px-4 py-3 focus:ring-2 focus:ring-[hsl(var(--ring))] focus:border-[hsl(var(--primary))] transition-all duration-200 shadow-sm;
  }
  
  /* Citation components */
  .citation-inline {
    @apply bg-[hsl(var(--citation-bg))] text-[hsl(var(--citation-text))] border border-[hsl(var(--citation-border))] rounded px-1.5 py-0.5 text-sm font-medium cursor-pointer hover:opacity-80 transition-opacity;
  }
  
  .citation-card {
    @apply bg-card border border-border rounded-xl p-4 transition-all duration-300 hover:shadow-md;
    box-shadow: var(--shadow-citation);
  }
  
  .citation-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }
  
  /* Button variants */
  .btn-research {
    @apply bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] hover:bg-[hsl(var(--primary-hover))] rounded-lg px-4 py-2 font-medium transition-all duration-200;
    background: var(--gradient-primary);
  }
  
  .btn-ghost {
    @apply text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--foreground))] hover:bg-[hsl(var(--secondary-hover))] rounded-lg px-3 py-2 transition-all duration-200;
  }
  
  .btn-citation {
    @apply bg-[hsl(var(--accent-subtle))] text-[hsl(var(--accent-foreground))] hover:bg-[hsl(var(--accent))] rounded-lg px-3 py-1.5 text-sm font-medium transition-all duration-200;
  }
  
  /* Skeleton loaders */
  .skeleton {
    @apply bg-[hsl(var(--muted))] animate-pulse rounded;
  }
  
  .skeleton-text {
    @apply skeleton h-4 mb-2;
  }
  
  .skeleton-card {
    @apply skeleton h-32 rounded-xl;
  }
  
  /* Utility classes */
  .gradient-primary {
    background: var(--gradient-primary);
  }
  
  .gradient-subtle {
    background: var(--gradient-subtle);
  }
  
  .gradient-citation {
    background: var(--gradient-citation);
  }
  
  .shadow-chat {
    box-shadow: var(--shadow-chat);
  }
  
  .shadow-citation {
    box-shadow: var(--shadow-citation);
  }
  
  /* Animations */
  .slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .bounce-in {
    animation: bounceIn 0.4s ease-out;
  }
}

@layer utilities {
  /* Responsive utilities */
  .scroll-smooth {
    scroll-behavior: smooth;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

textarea{color:#0f0f0f;}