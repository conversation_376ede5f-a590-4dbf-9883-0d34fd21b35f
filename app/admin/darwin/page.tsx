'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { AnalyticsData } from '@/types/darwin';

const mockAnalytics: AnalyticsData = {
    totalSessions: 1247,
    averageResponseTime: 2.3,
    citationCounts: {
        'Nature': 156,
        'Science': 142,
        'Cell': 98,
        'PNAS': 87,
        'Nature Medicine': 76
    },
    ratingDistribution: {
        1: 23,
        2: 45,
        3: 156,
        4: 423,
        5: 600
    },
    filterUsage: {
        'Year Range': 234,
        'Impact Factor': 189,
        'Open Access': 156,
        'Novelty Score': 98
    }
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function DarwinAdminDashboard() {
    const [analytics, setAnalytics] = useState<AnalyticsData>(mockAnalytics);

    const citationData = Object.entries(analytics.citationCounts).map(([journal, count]) => ({
        name: journal,
        count
    }));

    const ratingData = Object.entries(analytics.ratingDistribution).map(([rating, count]) => ({
        rating: `${rating}★`,
        count
    }));

    const filterData = Object.entries(analytics.filterUsage).map(([filter, count]) => ({
        name: filter,
        count
    }));

    return (
        <div className="min-h-screen bg-background p-6">
            <div className="max-w-7xl mx-auto space-y-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center space-y-4"
                >
                    <h1 className="text-4xl font-bold gradient-text">Darwin Analytics Dashboard</h1>
                    <p className="text-muted-foreground">Research assistant usage and performance metrics</p>
                </motion.div>

                {/* Key Metrics */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="grid grid-cols-1 md:grid-cols-4 gap-6"
                >
                    <div className="bg-card p-6 rounded-lg border">
                        <h3 className="text-sm font-medium text-muted-foreground">Total Sessions</h3>
                        <p className="text-3xl font-bold">{analytics.totalSessions.toLocaleString()}</p>
                    </div>
                    <div className="bg-card p-6 rounded-lg border">
                        <h3 className="text-sm font-medium text-muted-foreground">Avg Response Time</h3>
                        <p className="text-3xl font-bold">{analytics.averageResponseTime}s</p>
                    </div>
                    <div className="bg-card p-6 rounded-lg border">
                        <h3 className="text-sm font-medium text-muted-foreground">Total Citations</h3>
                        <p className="text-3xl font-bold">
                            {Object.values(analytics.citationCounts).reduce((a, b) => a + b, 0).toLocaleString()}
                        </p>
                    </div>
                    <div className="bg-card p-6 rounded-lg border">
                        <h3 className="text-sm font-medium text-muted-foreground">Avg Rating</h3>
                        <p className="text-3xl font-bold">
                            {(Object.entries(analytics.ratingDistribution).reduce((acc, [rating, count]) =>
                                acc + (parseInt(rating) * count), 0) /
                                Object.values(analytics.ratingDistribution).reduce((a, b) => a + b, 0)
                            ).toFixed(1)}★
                        </p>
                    </div>
                </motion.div>

                {/* Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Citation Distribution */}
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.2 }}
                        className="bg-card p-6 rounded-lg border"
                    >
                        <h3 className="text-lg font-semibold mb-4">Top Journals by Citations</h3>
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={citationData}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="name" />
                                <YAxis />
                                <Tooltip />
                                <Bar dataKey="count" fill="#8884d8" />
                            </BarChart>
                        </ResponsiveContainer>
                    </motion.div>

                    {/* Rating Distribution */}
                    <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                        className="bg-card p-6 rounded-lg border"
                    >
                        <h3 className="text-lg font-semibold mb-4">Rating Distribution</h3>
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <Pie
                                    data={ratingData}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={({ rating, percent }) => `${rating} ${(percent ? percent * 100 : 0).toFixed(0)}%`}
                                    outerRadius={80}
                                    fill="#8884d8"
                                    dataKey="count"
                                >
                                    {ratingData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                    ))}
                                </Pie>
                                <Tooltip />
                            </PieChart>
                        </ResponsiveContainer>
                    </motion.div>
                </div>

                {/* Filter Usage */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="bg-card p-6 rounded-lg border"
                >
                    <h3 className="text-lg font-semibold mb-4">Filter Usage</h3>
                    <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={filterData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="count" fill="#00C49F" />
                        </BarChart>
                    </ResponsiveContainer>
                </motion.div>

                {/* Recent Activity */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="bg-card p-6 rounded-lg border"
                >
                    <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
                    <div className="space-y-3">
                        {[
                            { time: '2 minutes ago', action: 'User asked about CRISPR gene editing', citations: 5 },
                            { time: '5 minutes ago', action: 'User searched for quantum computing papers', citations: 3 },
                            { time: '12 minutes ago', action: 'User filtered by open access papers', citations: 8 },
                            { time: '18 minutes ago', action: 'User rated response 5 stars', citations: 4 },
                            { time: '25 minutes ago', action: 'User exported citations to BibTeX', citations: 6 }
                        ].map((activity, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                                <div>
                                    <p className="font-medium">{activity.action}</p>
                                    <p className="text-sm text-muted-foreground">{activity.time}</p>
                                </div>
                                <span className="text-sm bg-primary/10 text-primary px-2 py-1 rounded">
                                    {activity.citations} citations
                                </span>
                            </div>
                        ))}
                    </div>
                </motion.div>
            </div>
        </div>
    );
} 