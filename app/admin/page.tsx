import { redirect } from "next/navigation";
import AdminDashboard from "../component/AdminComponents/AdminDashboard";
import { getUser } from "@/lib/cache/getUser";

export default async function AdminPage() {
  const user = await getUser();

  if (!user) {
    redirect("/signin");
  }

  if (user.role !== "ADMIN") {
    redirect("/signin");
  }

  return (
    <div className="w-full mx-auto px-4 py-8 bg-[#132435] text-white w-ful">
      <div className="container mx-auto">
        <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>

        <AdminDashboard />
      </div>
    </div>
  );
}
