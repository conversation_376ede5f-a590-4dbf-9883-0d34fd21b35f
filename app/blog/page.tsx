"use client";
import Image, { StaticImageData } from "next/image";
import { useRouter } from "next/navigation";
import riskyPlay from "./content/risky-play";
// import welcome from "./content/welcome";

type BlogPost = {
  slug: string;
  title: string;
  excerpt: string;
  date: string;
  image: StaticImageData;
  contentMarkdown: string;
};

export default function BlogPage() {
  const router = useRouter();
  const handlePostClick = (post: BlogPost) => {
    console.log(post.slug);
    router.push("/blog/" + post.slug);
  };

  const blogPosts: BlogPost[] = [riskyPlay];

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-5xl font-bold mb-12 text-center text-gray-800">
          Blog
        </h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post) => (
            <div
              key={post.slug}
              className="bg-white text-black shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:-translate-y-1"
              onClick={() => handlePostClick(post)}
            >
              <div className="relative h-96">
                <Image
                  src={post.image}
                  alt={post.title}
                  layout="fill"
                  objectFit="cover"
                />
              </div>
              <div className="p-6">
                <h2 className="text-2xl font-semibold mb-3">{post.title}</h2>
                <p className="text-gray-600 mb-4">{post.excerpt}</p>
                <p className="text-sm text-gray-500">{post.date}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
