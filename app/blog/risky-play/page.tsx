"use client";

import { useRouter } from "next/navigation";
import riskyPlay from "../content/risky-play";
import Image from "next/image";
import Link from "next/link";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
export default function RiskyPlay() {
  const router = useRouter();
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <button
        onClick={() => router.push("/blog")}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors duration-300"
      >
        ← Back to Posts
      </button>
      <article className="bg-white text-black shadow-md rounded-lg overflow-hidden p-8 max-w-3xl mx-auto">
        <h1 className="text-4xl font-bold mb-4">{riskyPlay.title}</h1>
        <p className="text-sm text-gray-500 mb-6">{riskyPlay.date}</p>
        <div className="relative w-full h-96 mb-8">
          <Image
            src={riskyPlay.image}
            alt={riskyPlay.title}
            layout="fill"
            objectFit="cover"
            className="rounded-lg"
          />
        </div>
        <a
          href=" https://doi.org/10.1038/d41586-024-04215-2"
          className="text-gray-600 mb-4"
        >
          DOI : 10.1038/d41586-024-04215-2
        </a>
        <p className="text-gray-600 mb-4">Author Name : Julian Nowogrodzki </p>
        <div className="prose prose-lg max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              h1: ({ node, ...props }) => (
                <h1 className="text-3xl font-bold my-6" {...props} />
              ),
              h2: ({ node, ...props }) => (
                <h2 className="text-2xl font-semibold my-4" {...props} />
              ),
              p: ({ node, ...props }) => <p className="my-4" {...props} />,
              ul: ({ node, ...props }) => (
                <ul className="list-disc list-inside my-4" {...props} />
              ),
              ol: ({ node, ...props }) => (
                <ol className="list-decimal list-inside my-4" {...props} />
              ),
              li: ({ node, ...props }) => <li className="my-2" {...props} />,
              a: ({ node, ...props }) => (
                <a className="text-blue-500 hover:underline" {...props} />
              ),
            }}
          >
            {riskyPlay.contentMarkdown}
          </ReactMarkdown>
        </div>
      </article>
      <div className="max-w-3xl mx-auto mt-4 w-full flex items-center justify-center  text-xl font-semibold">
        <Link href="/homepage">
          <button
            className="px-8  h-8 rounded-lg text-medium bg-[#88D84D] text-black
            "
          >
            Read more research papers on Outread
          </button>
        </Link>
      </div>
    </div>
  );
}
