"use client";

import React, { useEffect, useState, useMemo } from "react";
import dynamic from "next/dynamic";
import <PERSON> from "papaparse";
import { Card, CardBody } from "@nextui-org/card";
import { Tabs, Tab } from "@nextui-org/tabs";
import { Input } from "@nextui-org/input";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from "@nextui-org/table";
import { Spinner } from "@nextui-org/spinner";
import { Chip } from "@nextui-org/chip";

const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

export default function OceanResearchersNextUI() {
  const [rows, setRows] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");

  useEffect(() => {
    fetch("/ocean_researchers_top100_each_category.csv")
      .then((res) => res.text())
      .then((text) => {
        const { data } = Papa.parse(text, {
          header: true,
          skipEmptyLines: true,
        });
        setRows(data as any[]);
      })
      .finally(() => setLoading(false));
  }, []);

  const categories = useMemo(() => {
    const set = new Set<string>();
    rows.forEach((r) =>
      r.top_in_categories.split("|").forEach((c: string) => set.add(c)),
    );
    return Array.from(set).sort();
  }, [rows]);

  const byCategory = useMemo(() => {
    const map: Record<string, any[]> = Object.fromEntries(
      categories.map((c) => [c, []]),
    );
    rows.forEach((r) => {
      r.top_in_categories.split("|").forEach((c: string) => map[c].push(r));
    });
    categories.forEach((c) =>
      map[c].sort(
        (a, b) =>
          parseFloat(b.avg_citation_velocity) -
          parseFloat(a.avg_citation_velocity),
      ),
    );
    return map;
  }, [rows, categories]);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Spinner size="lg" label="Loading data…" />
      </div>
    );
  }

  return (
    <div className="w-full mx-auto px-4 py-8 space-y-6 text-black bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">
            Ocean Research – Top 100 Authors per Topic
          </h1>
          <p className="text-gray-600 text-sm max-w-prose mx-auto">
            Rankings based on <em>average citation velocity</em> (citations ÷
            days since publication) for works published in the last 12 months.
          </p>
        </div>
        <Input
          radius="sm"
          placeholder="Search author or institution…"
          className="max-w-lg mx-auto"
          value={search}
          onChange={(e) => setSearch(e.target.value.toLowerCase())}
        />
        <Tabs
          aria-label="Ocean categories"
          placement="top"
          variant="underlined"
          color="primary"
          className="w-full"
        >
          {categories.map((cat) => (
            <Tab
              key={cat}
              title={cat
                .replace(/\bocean\s/i, "")
                .replace("pollution", "poll.")}
              className="w-full"
            >
              <CategoryView
                category={cat}
                data={byCategory[cat]}
                search={search}
              />
            </Tab>
          ))}
        </Tabs>
      </div>
    </div>
  );
}

interface CatProps {
  category: string;
  data: any[];
  search: string;
}

function CategoryView({ category, data, search }: CatProps) {
  const top20 = data.slice(0, 20);
  const maxVal =
    Math.max(...top20.map((d) => Number(d.avg_citation_velocity))) * 1.1 || 0.3;
  const series = [
    {
      name: "Velocity",
      data: top20.map((d) => Number(d.avg_citation_velocity)),
    },
  ];
  const options = {
    chart: { type: "bar", toolbar: { show: false } },
    plotOptions: { bar: { horizontal: true, borderRadius: 3 } },
    xaxis: {
      categories: top20.map((d) => d.author_name),
      min: 0,
      max: maxVal,
      tickAmount: 6,
      labels: {
        formatter: (value: string) => Number(value).toFixed(3),
      },
    },
    dataLabels: { enabled: false },
    tooltip: { y: { formatter: (v: number) => v.toFixed(4) } },
    grid: { xaxis: { lines: { show: true } } },
  } as ApexCharts.ApexOptions;

  const filtered = search
    ? data.filter(
        (r) =>
          r.author_name.toLowerCase().includes(search) ||
          r.institutions.toLowerCase().includes(search),
      )
    : data;

  return (
    <div className="space-y-6 bg-white text-black">
      <Card shadow="sm">
        <CardBody>
          <Chart type="bar" series={series} options={options} height={450} />
        </CardBody>
      </Card>

      <Table
        aria-label={`${category} authors`}
        isStriped
        removeWrapper
        className="text-sm"
      >
        <TableHeader>
          <TableColumn>#</TableColumn>
          <TableColumn>Author</TableColumn>
          <TableColumn className="max-w-[260px]">Institution(s)</TableColumn>
          <TableColumn className="max-w-[260px]">
            Lifetime Citations
          </TableColumn>
          <TableColumn align="end">Citation Velocity</TableColumn>
          <TableColumn align="center">Topics</TableColumn>
        </TableHeader>
        <TableBody items={filtered} emptyContent={"No results"}>
          {(item) => (
            <TableRow key={item.author_id + category}>
              <TableCell>{filtered.indexOf(item) + 1}</TableCell>
              <TableCell>
                <a
                  href={item.author_id}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary underline-offset-2 hover:underline"
                >
                  {item.author_name}
                </a>
              </TableCell>
              <TableCell className="truncate max-w-[260px]">
                {item.institutions}
              </TableCell>
              <TableCell className="truncate max-w-[260px]">
                {item.lifetime_citations}
              </TableCell>
              <TableCell className="text-right">
                {Number(item.avg_citation_velocity).toFixed(4)}
              </TableCell>
              <TableCell className="text-center">
                {item.top_in_categories.split("|").map((t: string) => (
                  <Chip
                    key={t}
                    size="sm"
                    variant="flat"
                    color="secondary"
                    className="m-0.5 capitalize"
                  >
                    {t}
                  </Chip>
                ))}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
