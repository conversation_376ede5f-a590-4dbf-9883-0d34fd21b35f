"use client";
import React, { useState } from "react";
import Image from "next/image";

const steps = [
  { title: "Where do you work or research?" },
  { title: "What is your role?" },
  { title: "How are you hoping to use Outread?" },
  { title: "How did you hear about Outread?" },
  { title: "Explore Collections" },
  { title: "What categories would you like to explore?" },
];

interface FormData {
  workPlace: string;
  role: string;
  useCase: string[];
  heardFrom: string;
  categories: string[];
}

const OnboardingForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    workPlace: "",
    role: "",
    useCase: [],
    heardFrom: "",
    categories: [],
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleNext = () => {
    console.log(currentStep);
    if (currentStep === 5 && formData.categories.length < 5) {
      setError("Please select at least 5 categories before proceeding.");
      return;
    }
    if (currentStep < steps.length - 2) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSubmit();
    }
  };
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async () => {
    setError(null);
    setIsLoading(true);
    try {
      const response = await fetch("/api/savePreferences", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        // Redirect to homepage
        window.location.href = "/";
      } else {
        const errorData = await response.json();
        console.error("Failed to save preferences:", errorData.error);
        setError("Failed to save preferences. Please try again.");
      }
    } catch (error) {
      console.error("Error saving preferences:", error);
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="step-container h-full">
            <h2 className="text-4xl font-bold my-8 text-center">
              Where do you work or research?
            </h2>
            <textarea
              value={formData.workPlace}
              onChange={(e) =>
                setFormData({ ...formData, workPlace: e.target.value })
              }
              className="w-full rounded-xl h-[200px] p-4 bg-gray-700 text-white"
              placeholder="Enter your workplace"
            />
          </div>
        );
      case 1:
        return (
          <div>
            <h2 className="text-4xl font-bold my-8 text-center">
              What is your role?
            </h2>
            <textarea
              value={formData.role}
              onChange={(e) =>
                setFormData({ ...formData, role: e.target.value })
              }
              className="w-full rounded-xl h-[200px] p-4 bg-gray-700 text-white"
              placeholder="Enter your role"
            />
          </div>
        );
      case 2:
        return (
          <div>
            <h2 className="text-4xl font-bold my-8 text-center">
              How are you hoping to use Outread?
            </h2>
            <div className="flex flex-col space-y-4">
              {[
                "Stay curious and explore new science",
                "Track emerging trends in your field",
                "Support professional research and analysis",
                "Search for answers to a specific question",
                "To prepare content (thought leadership posts)",
                "For literature review and assignments",
                "Still exploring how it fits into my workflow",
              ].map((option) => (
                <button
                  key={option}
                  onClick={() => {
                    const updatedUseCase = formData.useCase.includes(option)
                      ? formData.useCase.filter((item) => item !== option)
                      : [...formData.useCase, option];
                    setFormData({ ...formData, useCase: updatedUseCase });
                  }}
                  className={`py-3 px-2 rounded-lg text-md transition-colors duration-200 text-center ${
                    formData.useCase.includes(option)
                      ? "bg-[#2C3E50] text-white"
                      : "bg-[#1E293B] text-[#F5F5F5CC] hover:bg-[#2C3E50]"
                  }`}
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
        );

      case 3:
        return (
          <div>
            <h2 className="text-4xl font-bold my-8 text-center">
              How did you hear about Outread?
            </h2>
            <div className="flex flex-col space-y-4">
              {[
                "Word of mouth",
                "LinkedIn",
                "Instagram",
                "TikTok",
                "Facebook",
                "Event",
                "Blog post",
                "Newsletter",
                "Other",
              ].map((option) => (
                <button
                  key={option}
                  onClick={() =>
                    setFormData({ ...formData, heardFrom: option })
                  }
                  className={`py-3 px-2 rounded-lg text-lg transition-colors duration-200 text-center ${
                    formData.heardFrom === option
                      ? "bg-[#2C3E50] text-white"
                      : "bg-[#1E293B] text-[#F5F5F5CC] hover:bg-[#2C3E50]"
                  }`}
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
        );
      case 4:
        return (
          <div>
            <h2 className="text-4xl font-bold my-8 text-center">
              What categories would you like to explore?
            </h2>
            <span className="text-white text-center flex justify-center my-4">
              Pick at least 5 to continue...
            </span>
            <div className="flex flex-wrap justify-center">
              {[
                "Startup",
                "Business",
                "Health",
                "Education",
                "Technology",
                "Engineering",
                "Psychology",
                "Science",
                "Medicine",
                "AI",
                "Economics",
                "Environment",
                "Sociology",
                "History",
                "Law",
                "Sports",
                "Philosophy",
                "Literature",
                "Art",
                "Politics",
                "Ethics",
                "Culture",
                "Renewable Energy",
                "Astronomy",
                "Computer Science",
              ].map((category, index) => (
                <button
                  key={category}
                  onClick={() => {
                    const updatedCategories = formData.categories.includes(
                      category,
                    )
                      ? formData.categories.filter((item) => item !== category)
                      : [...formData.categories, category];
                    setFormData({
                      ...formData,
                      categories: updatedCategories,
                    });
                  }}
                  className={`m-2 py-2 px-4 rounded-lg text-lg transition-colors duration-200 ${
                    formData.categories.includes(category)
                      ? "bg-[#2C3E50] text-white"
                      : "bg-[#1E293B] text-[#F5F5F5CC] hover:bg-[#2C3E50]"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        );
      default:
        return <div>Step not implemented</div>;
    }
  };

  return (
    <div className="w-full  bg-[#132435]">
      <div className="lg:w-[800px] mx-auto p-6 text-white min-h-screen">
        <div className="w-full bg-gray-700 rounded-full h-2.5">
          <div
            className="bg-[#88D84D] h-2.5 rounded-full"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          ></div>
        </div>
        <div className="flex justify-between items-center my-4 text-[#F5F5F5CC]">
          <span>
            Step {currentStep + 1} of {steps.length}
          </span>
          {currentStep !== 4 && (
            <button onClick={handleNext} className="text-[#88D84D]">
              Skip
            </button>
          )}
        </div>

        <div className="step-container mb-8 h-full">{renderStep()}</div>

        {error && (
          <div className="mb-6 p-2 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div className="flex justify-center mt-8">
          <button
            onClick={handleNext}
            className={`bg-[#88D84D] text-black px-4 py-2 rounded text-sm w-full lg:max-w-[240px] ${
              (currentStep === 4 && formData.categories.length < 5) || isLoading
                ? "opacity-50 cursor-not-allowed"
                : ""
            }`}
            disabled={
              (currentStep === 4 && formData.categories.length < 5) || isLoading
            }
          >
            {isLoading
              ? "Taking you to DarwinAI..."
              : currentStep === steps.length - 1
                ? "Finish"
                : "Next"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OnboardingForm;
