import { ChangeEvent, KeyboardEvent, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { Button, input, Select, SelectItem } from "@nextui-org/react";
import { Textarea } from "@nextui-org/react";
import { ArticleData } from "./preloaded";
import { PreviousQuestion } from "./page";

function sanitizeInput(input: string): string {
  return input.replace(/[^a-zA-Z -123457890]/g, "").slice(0, 150);
}

export function SearchArea({
  className,
  onSubmit,
  isLoading,
  isMobile,
  previousQuestions,
  articleData,
  onSelectPreloaded,
}: {
  isMobile: boolean;
  className: string;
  onSubmit: (
    query: string,
    complexity: string,
    filters?: {
      minYear?: number;
      minCitations?: number;
      minRelevance?: number;
    },
  ) => void;
  isLoading: boolean;
  previousQuestions: PreviousQuestion[];
  articleData: ArticleData | null;
  onSelectPreloaded: (data: ArticleData) => void;
}) {
  const [value, setValue] = useState("");
  const [complexity, setComplexity] = useState("informative");
  const [minYear, setMinYear] = useState("");
  const [minCitations, setMinCitations] = useState("");
  const [minRelevance, setMinRelevance] = useState("");
  const searchParams = useSearchParams();
  const defaultSearchTrending = searchParams.get("search");

  useEffect(() => {
    const inputWrapper: any = document.querySelector(
      '[data-slot="input-wrapper"]',
    );

    if (inputWrapper) {
      inputWrapper.style.backgroundColor = "#132435"; // Add inline styles
    }
  }, []);

  const handleChange = (newValue: string) => {
    setValue(sanitizeInput(newValue));
  };

  const handleSubmit = () => {
    // Only submit if there's a value to search for
    if (value.trim()) {
      const filters = {
        minYear: minYear ? parseInt(minYear) : undefined,
        minCitations: minCitations ? parseInt(minCitations) : undefined,
        minRelevance: minRelevance ? parseFloat(minRelevance) : undefined,
      };

      onSubmit(value, complexity, filters);
    }
  };

  const handlePreloadedQuestion = (question: PreviousQuestion) => {
    // Set the value first
    setValue(sanitizeInput(question.query));
    // Then select the preloaded data
    onSelectPreloaded(question.response);
  };

  // Single useEffect to handle the default search parameter
  useEffect(() => {
    if (defaultSearchTrending) {
      const trimmedSearch = defaultSearchTrending.trim().toLowerCase();
      const match = previousQuestions.find(
        (pq) => pq.query.toLowerCase() === trimmedSearch,
      );

      if (match) {
        handlePreloadedQuestion(match);
      } else if (trimmedSearch) {
        // Only submit if the search parameter has content
        setValue(sanitizeInput(defaultSearchTrending));
        onSubmit(defaultSearchTrending, complexity);
      }
    }
  }, [defaultSearchTrending, previousQuestions, complexity, onSubmit]); // Added missing dependencies

  // Search box and filter controls
  const renderSearchControls = () => {
    if (isMobile) {
      return (
        <div className="flex flex-col gap-4 w-full mt-4">
          <div className="flex justify-between items-center">
            <details className="w-full">
              <summary className="list-none flex items-center justify-between cursor-pointer bg-[#132435] p-3 rounded-lg">
                <span className="text-white text-sm font-medium">
                  Search Filters
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-gray-400"
                >
                  <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
              </summary>

              <div className="pt-3 pb-2 px-1">
                <div className="mb-3">
                  <label className="text-xs text-gray-400 block mb-1">
                    Complexity
                  </label>
                  <Select
                    aria-label="Complexity"
                    disabled={isLoading}
                    selectedKeys={[complexity]}
                    classNames={{
                      base: "w-full",
                      trigger: "bg-[#132435] h-9 min-h-0",
                    }}
                    onChange={(e: ChangeEvent<HTMLSelectElement>) =>
                      setComplexity(e.target.value)
                    }
                    size="sm"
                  >
                    <SelectItem
                      className="text-[#132435]"
                      key="simple"
                      value="simple"
                    >
                      Simple
                    </SelectItem>
                    <SelectItem
                      className="text-[#132435]"
                      key="informative"
                      value="informative"
                    >
                      Informative
                    </SelectItem>
                  </Select>
                </div>

                <div className="grid grid-cols-3 gap-2">
                  <div className="flex flex-col">
                    <label className="text-xs text-gray-400 mb-1">Year ≥</label>
                    <input
                      type="number"
                      placeholder="2018"
                      value={minYear}
                      onChange={(e) => setMinYear(e.target.value)}
                      className="bg-[#132435] text-white p-1.5 rounded-md border border-gray-600 focus:border-[#84cc16] outline-none text-sm w-full h-9"
                      min="1900"
                    />
                  </div>
                  <div className="flex flex-col">
                    <label className="text-xs text-gray-400 mb-1">
                      Citations ≥
                    </label>
                    <input
                      type="number"
                      placeholder="100"
                      value={minCitations}
                      onChange={(e) => setMinCitations(e.target.value)}
                      className="bg-[#132435] text-white p-1.5 rounded-md border border-gray-600 focus:border-[#84cc16] outline-none text-sm w-full h-9"
                      min="0"
                    />
                  </div>
                  <div className="flex flex-col">
                    <label className="text-xs text-gray-400 mb-1">
                      Relevance ≥%
                    </label>
                    <input
                      type="number"
                      placeholder="80"
                      value={minRelevance}
                      onChange={(e) => setMinRelevance(e.target.value)}
                      className="bg-[#132435] text-white p-1.5 rounded-md border border-gray-600 focus:border-[#84cc16] outline-none text-sm w-full h-9"
                      min="0"
                    />
                  </div>
                </div>
              </div>
            </details>

            <Button
              onPress={handleSubmit}
              disabled={isLoading}
              className="bg-[#84cc16] text-[#132435] min-w-fit w-[50px] h-[50px] ml-2 p-0 rounded-full flex items-center justify-center"
            >
              {isLoading ? (
                <svg
                  className="text-2xl pointer-events-none flex-shrink-0 text-[#132435]"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="none"
                    stroke="currentColor"
                    strokeDasharray="16"
                    strokeDashoffset="16"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 3c4.97 0 9 4.03 9 9"
                  >
                    <animate
                      fill="freeze"
                      attributeName="stroke-dashoffset"
                      dur="0.2s"
                      values="16;0"
                    />
                    <animateTransform
                      attributeName="transform"
                      dur="1.5s"
                      repeatCount="indefinite"
                      type="rotate"
                      values="0 12 12;360 12 12"
                    />
                  </path>
                </svg>
              ) : (
                <svg
                  className="pointer-events-none flex-shrink-0 text-[#132435]"
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="m19.6 21l-6.3-6.3q-.75.6-1.725.95T9.5 16q-2.725 0-4.612-1.888T3 9.5t1.888-4.612T9.5 3t4.613 1.888T16 9.5q0 1.1-.35 2.075T14.7 13.3l6.3 6.3zM9.5 14q1.875 0 3.188-1.312T14 9.5t-1.312-3.187T9.5 5T6.313 6.313T5 9.5t1.313 3.188T9.5 14"
                  />
                </svg>
              )}
            </Button>
          </div>
        </div>
      );
    } else {
      return (
        <div className="flex flex-row gap-4 items-center justify-between mt-4">
          <div className="w-1/4 selectdropdown">
            <Select
              labelPlacement={"inside"}
              label="Complexity"
              disabled={isLoading}
              selectedKeys={[complexity]}
              classNames={{
                base: "bg-[#1f2937]",
                trigger: "bg-[#132435]",
              }}
              onChange={(e: ChangeEvent<HTMLSelectElement>) =>
                setComplexity(e.target.value)
              }
            >
              <SelectItem
                className="text-[#132435] w-full"
                key="simple"
                value="simple"
              >
                Simple
              </SelectItem>
              <SelectItem
                className="text-[#132435] w-full"
                key="informative"
                value="informative"
              >
                Informative
              </SelectItem>
            </Select>
          </div>

          <div className="w-full flex-1">
            <div className="grid grid-cols-3 gap-2">
              <div className="flex flex-col gap-1">
                <label className="text-xs text-gray-400">Year ≥</label>
                <input
                  type="number"
                  placeholder="2018"
                  value={minYear}
                  onChange={(e) => setMinYear(e.target.value)}
                  className="bg-[#132435] text-white p-1.5 rounded-md border border-gray-600 focus:border-[#84cc16] outline-none text-sm w-full"
                  min="1900"
                />
              </div>
              <div className="flex flex-col gap-1">
                <label className="text-xs text-gray-400">Citations ≥</label>
                <input
                  type="number"
                  placeholder="100"
                  value={minCitations}
                  onChange={(e) => setMinCitations(e.target.value)}
                  className="bg-[#132435] text-white p-1.5 rounded-md border border-gray-600 focus:border-[#84cc16] outline-none text-sm w-full"
                  min="0"
                />
              </div>
              <div className="flex flex-col gap-1">
                <label className="text-xs text-gray-400">Relevance ≥%</label>
                <input
                  type="number"
                  placeholder="80"
                  value={minRelevance}
                  onChange={(e) => setMinRelevance(e.target.value)}
                  className="bg-[#132435] text-white p-1.5 rounded-md border border-gray-600 focus:border-[#84cc16] outline-none text-sm w-full"
                  min="0"
                />
              </div>
            </div>
          </div>

          <Button
            onPress={handleSubmit}
            disabled={isLoading}
            className="w-fit mt-2 bg-[#84cc16] text-[#132435] w-[50px] min-w-fit px-[10px] py-[25px] rounded-full"
          >
            {isLoading ? (
              <svg
                className="text-2xl pointer-events-none flex-shrink-0 text-[#132435]"
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
              >
                <path
                  fill="none"
                  stroke="currentColor"
                  strokeDasharray="16"
                  strokeDashoffset="16"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 3c4.97 0 9 4.03 9 9"
                >
                  <animate
                    fill="freeze"
                    attributeName="stroke-dashoffset"
                    dur="0.2s"
                    values="16;0"
                  />
                  <animateTransform
                    attributeName="transform"
                    dur="1.5s"
                    repeatCount="indefinite"
                    type="rotate"
                    values="0 12 12;360 12 12"
                  />
                </path>
              </svg>
            ) : (
              <svg
                className="text-2xl pointer-events-none flex-shrink-0 text-[#132435]"
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
              >
                <path
                  fill="currentColor"
                  d="m19.6 21l-6.3-6.3q-.75.6-1.725.95T9.5 16q-2.725 0-4.612-1.888T3 9.5t1.888-4.612T9.5 3t4.613 1.888T16 9.5q0 1.1-.35 2.075T14.7 13.3l6.3 6.3zM9.5 14q1.875 0 3.188-1.312T14 9.5t-1.312-3.187T9.5 5T6.313 6.313T5 9.5t1.313 3.188T9.5 14"
                />
              </svg>
            )}
          </Button>
        </div>
      );
    }
  };

  // Trending questions section
  const renderTrendingQuestions = () => {
    if (previousQuestions.length > 0 && articleData == null && !isLoading) {
      return (
        <div className="mt-20 mb-20">
          <div className="flex flex-row flex-wrap justify-center gap-4">
            {previousQuestions.map((question, index) => (
              <div
                key={index}
                className="flex justify-between bg-[#1f2937] items-center drop-shadow-lg rounded-full mb-4 py-2 px-10 cursor-pointer"
                onClick={() => handlePreloadedQuestion(question)}
              >
                <p className="text-[#fff] text-lg font-normal">
                  {question.query}
                </p>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div
      className={`${className} flex flex-col items-center py-2 bg-[#132435]`}
    >
      <div className="flex flex-col gap-5 w-full justify-center text-center">
        <div className="mt-24">
          <h1
            className={`${isMobile ? "text-2xl" : "text-7xl"} font-bold mb-8 animate-fade-in text-white`}
          >
            DarwinAI
          </h1>
          <p className="text-xl text-gray-300 mb-12 animate-slide-up">
            Ask questions and get insights from peer-reviewed studies.{" "}
            <Link href="/darwinai-landing">
              <span className="underline text-gray-500 cursor-pointer">
                Learn more
              </span>
            </Link>
          </p>
        </div>

        <div className="bg-[#1C2632] w-full lg:max-w-4xl ml-[auto] mr-[auto] flex flex-col items-center drop-shadow-lg rounded-xl mb-2 py-5 px-4 cursor-default">
          <div className="flex flex-col w-full">
            <Textarea
              label="Ask a question..."
              disabled={isLoading}
              classNames={{
                inputWrapper: "!bg-white",
                input: "!text-black",
              }}
              onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit();
                }
              }}
              value={value}
              onValueChange={handleChange}
              maxLength={250}
            />

            {renderSearchControls()}
          </div>
        </div>

        {renderTrendingQuestions()}
      </div>
    </div>
  );
}
