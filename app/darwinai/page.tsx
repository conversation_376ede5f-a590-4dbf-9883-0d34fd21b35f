"use client";

import React, { useState, useRef, useEffect } from "react";
import { useClientMediaQuery } from "@/hooks/useClientMediaQuery";
import {
  question1Response,
  question2Response,
  question3Response,
} from "./preloaded";
import { ArticleData, RelevantPaper } from "./preloaded";
import { SearchArea } from "./searchArea";
import { useAuthStore } from "@/lib/zustand/zustand";
import Link from "next/link";
import SignInModal from "../component/SignInModal";

export interface PreviousQuestion {
  id: string;
  query: string;
  complexity: string;
  createdAt: string;
  response: ArticleData;
}

import { DiscoverContent } from "./discoverContent";

// Define the type for subscription options
type SubscriptionOption = {
  title: string;
  price: string;
  billing: string;
  features: string[];
  priceId: string;
  trialDays: number;
  isPopular: boolean;
};

// Define subscriptionOptions
const subscriptionOptions: SubscriptionOption[] = [
  {
    title: "Yearly",
    price: "$5 USD/Month",
    billing: "Billed as one payment of $60 USD each year",
    features: [
      "7 day free trial",
      "Access to 1000+ research paper summaries & audios",
      "Full access to DarwinAI",
      "New articles added each week",
      "Personalized content",
      "Easy cancellation",
    ],
    priceId: "price_1QWRHnDOnxS19iBl4XzOGhyU",
    trialDays: 7,
    isPopular: true,
  },
  {
    title: "Monthly",
    price: "10 USD/Month",
    billing: "Billed as monthly payments of $10 USD",
    features: [
      "7 day free trial",
      "Access to 1000+ research paper summaries & audios",
      "Full access to DarwinAI",
      "New articles added each week",
      "Personalized content",
      "Easy cancellation",
    ],
    priceId: "price_1QWRJKDOnxS19iBl9pAY8pcC",
    trialDays: 7,
    isPopular: false,
  },
];

function LoadingAnimation() {
  const messages = [
    "Processing Query",
    "Searching internal and external resources",
    "Formulating insight",
    "Linking Resources",
    "Checking Relevance",
    "Validating Results",
    "Generating summary and response",
  ];
  const [currentMessage, setCurrentMessage] = useState(0);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % messages.length);
    }, 5000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <div className={`xl:w-10/12 w-full lg:max-w-4xl`}>
      <p className="text-center text-xl font-medium text-[#fff]">
        {messages[currentMessage]}...
      </p>
    </div>
  );
}

function DiscoverPage() {
  const isMobile = useClientMediaQuery("(max-width: 768px)");
  const [articleData, setArticleData] = useState<ArticleData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);
  const { user, updateUserCredit } = useAuthStore();

  const previousQuestions: PreviousQuestion[] = [
    {
      id: "1",
      query: "Which is better brown or white rice?",
      complexity: "simple",
      createdAt: "2024-01-01T00:00:00Z",
      response: question1Response,
    },
    {
      id: "2",
      query: "Does happiness rise with income?",
      complexity: "simple",
      createdAt: "2024-01-01T00:00:00Z",
      response: question2Response,
    },
    {
      id: "3",
      query: "Where should we start looking for alien life?",
      complexity: "simple",
      createdAt: "2024-01-01T00:00:00Z",
      response: question3Response,
    },
    {
      id: "4",
      query: "Can CBT improve anxiety?",
      complexity: "simple",
      createdAt: "2024-01-01T00:00:00Z",
      response: question2Response,
    },
    {
      id: "5",
      query: "What causes déjà vu?",
      complexity: "simple",
      createdAt: "2024-01-01T00:00:00Z",
      response: question3Response,
    },
  ];

  const fetchData = async (
    query: string,
    complexity: string,
    filters?: {
      minYear?: number;
      minCitations?: number;
      minRelevance?: number;
    },
  ) => {
    const matchedPreloaded = previousQuestions.find(
      (q) => q.query.toLowerCase() === query.toLowerCase(),
    );
    if (matchedPreloaded) {
      // If we have a preloaded response for this query, show it directly without credit check
      setArticleData(matchedPreloaded.response);
      return;
    }

    // Check if user is authenticated
    if (!user) {
      setIsSignInModalOpen(true);
      return;
    }

    // Check if user is paid or has sufficient credits for a server request
    if (
      user.role === "USER" &&
      (typeof user.credit !== "number" || user.credit <= 0)
    ) {
      setArticleData(question2Response);
      setError(
        "Insufficient credits. Please purchase more credits or upgrade to continue.",
      );
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/trendAnalysis", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query,
          complexity,
          filters: {
            minYear: filters?.minYear,
            minCitations: filters?.minCitations,
            minRelevance: filters?.minRelevance,
          },
        }),
      });
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();

      const articleData = data.result;
      const sortedArticleData = articleData.relevant_papers.sort(
        (a: RelevantPaper, b: RelevantPaper) => {
          if (a.type === "outread" && b.type === "semantic_scholar") return -1;
          if (a.type === "semantic_scholar" && b.type === "outread") return 1;
          return (b.relevance_score || 0) - (a.relevance_score || 0);
        },
      );
      data.result.relevant_papers = sortedArticleData;

      setArticleData(data.result);

      // Update user credits only if they're not a paid user
      if (user.role === "USER") {
        updateUserCredit(user.credit - 1);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      setError("Failed to load article data. Please try again later." + error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectPreloaded = (data: ArticleData) => {
    // Set the preloaded article data immediately
    setArticleData(data);
  };

  const isPreloaded = !!previousQuestions.find(
    (q) => q.response === articleData,
  );

  let showBlur = user?.role === "USER" && user.credit <= 0;
  if (isPreloaded == true) {
    showBlur = false;
  }

  if (error) {
    showBlur = true;
  }

  console.log({ isPreloaded });

  const content = (
    <>
      <div className="flex flex-col items-center justify-center w-full h-full bg-[#132435]">
        {error && <div className="text-red-500 mb-4">{error}</div>}
        <SearchArea
          className={`xl:w-10/12 w-full justify-center ${
            isMobile ? "w-full" : "w-1/2"
          }   mb-${isMobile ? "4" : "8"} `}
          onSubmit={fetchData}
          isLoading={isLoading}
          isMobile={isMobile!}
          previousQuestions={previousQuestions}
          articleData={articleData}
          onSelectPreloaded={handleSelectPreloaded}
        />

        {isLoading && <LoadingAnimation />}
        {!isLoading && articleData && (
          <DiscoverContent
            isMobile={isMobile!}
            articleData={articleData}
            hasCredits={user?.credit !== undefined && user.credit > 0}
            isPreloaded={isPreloaded}
            showSignupBlur={showBlur}
            subscriptionOptions={subscriptionOptions}
          />
        )}
      </div>
      <SignInModal
        isOpen={isSignInModalOpen}
        onClose={() => setIsSignInModalOpen(false)}
      />
    </>
  );

  return (
    <div className="bg-[#132435]">
      {}
      <div className="flex w-full min-h-screen">
        <div
          className={`flex-1 flex flex-col items-center justify-start p-${
            isMobile ? "4" : "8"
          }`}
        >
          <div className="mb-4 text-lg font-semibold text-white">
            {!user
              ? "Login to trial DarwinAi for free"
              : "Credits Remaining: " +
                (user?.role !== "USER"
                  ? "Unlimited"
                  : user?.credit !== undefined
                    ? user.credit
                    : 0)}
          </div>
          {content}
        </div>
      </div>
      {previousQuestions.length > 0 && articleData == null && !isLoading && (
        <div className="flex flex-col items-center justify-start py-20 px-6 w-full bg-[#151d2c]">
          <div className="container mx-auto">
            <div className="grid md:grid-cols-3 gap-8">
              <div className="cursor-pointer p-8 rounded-2xl bg-dark-light/30 backdrop-blur-lg border border-white/5 hover:border-[#84cc16] transition-all hover:-translate-y-1">
                <div className="w-12 h-12 bg-[#202f2a] rounded-full flex items-center justify-center mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-check text-[#84cc16]"
                  >
                    <path d="M20 6 9 17l-5-5"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  Science-backed answers
                </h3>
                <p className="text-gray-400">
                  No misinformation, just accurate research-based responses
                </p>
              </div>
              <div className="cursor-pointer p-8 rounded-2xl bg-dark-light/30 backdrop-blur-lg border border-white/5 hover:border-[#84cc16] transition-all hover:-translate-y-1">
                <div className="w-12 h-12 bg-[#202f2a] rounded-full flex items-center justify-center mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-check text-[#84cc16]"
                  >
                    <path d="M20 6 9 17l-5-5"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  Individual insights
                </h3>
                <p className="text-gray-400">
                  Get relevant insights from research papers answering your
                  query
                </p>
              </div>
              <div className="cursor-pointer p-8 rounded-2xl bg-dark-light/30 backdrop-blur-lg border border-white/5 hover:border-[#84cc16] transition-all hover:-translate-y-1">
                <div className="w-12 h-12 bg-[#202f2a] rounded-full flex items-center justify-center mb-6">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-check text-[#84cc16]"
                  >
                    <path d="M20 6 9 17l-5-5"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">
                  Vast research database
                </h3>
                <p className="text-gray-400">
                  Search through over 200M research papers across every domain
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
      {!user?.isPaidUser && (
        <section className="py-20 px-6">
          <div className="container mx-auto text-center">
            <h2 className="text-4xl font-bold text-white mb-8">
              Get Unlimited Access
            </h2>
            <Link
              href="/subscription"
              className="bg-[#88D84D] hover:bg-[#65a30d] text-[#132435] font-semibold px-12 py-4 rounded-full text-lg transition-colors"
            >
              Get Premium
            </Link>
          </div>
        </section>
      )}
    </div>
  );
}

export default DiscoverPage;
