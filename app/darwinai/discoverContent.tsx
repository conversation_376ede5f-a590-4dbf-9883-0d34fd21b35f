"use client";

import {
  <PERSON>,
  CardHeader,
  CardBody,
  Progress,
  Divider,
} from "@nextui-org/react";
import { But<PERSON> } from "@nextui-org/react";
import { useState, useRef, useCallback } from "react";
import { Insight, RelevantPaper, ArticleData } from "./preloaded";
import { useAuthStore } from "@/lib/zustand/zustand";
import { loadStripe } from "@stripe/stripe-js";
import { trackCheckoutInitiated } from "@/lib/branch";
import { handleInitiateCheckout } from "../component/FacebookConversionAPI";
import toast from "react-hot-toast";
import { useEffect } from "react";

const STRIPE_PUBLIC_KEY =
  "pk_live_51K55kmDOnxS19iBlIjxFwa8OVBnqkPr0N78QHtwk7QM3F9Vx6THdZZZh8zlHVxN0QUYpPL7ihPufwsxNYIaGtHin00gq1f3UhI";

function ReferencedText({
  text,
  onReferenceClick,
}: {
  text: string;
  onReferenceClick: (index: number) => void;
}) {
  const parts = text.split(/(<reference>\d+<\/reference>)/);
  return (
    <p className="text-[#ffffff] font-normal text-lg">
      {parts.map((part, i) => {
        const match = part.match(/<reference>(\d+)<\/reference>/);
        if (match) {
          return (
            <sup
              key={i}
              className="text-[#88D84D] font-normal text-sm cursor-pointer"
              onClick={() => onReferenceClick(parseInt(match[1]))}
            >
              [{match[1]}]
            </sup>
          );
        }
        return part;
      })}
    </p>
  );
}

function InsightCard({
  isMobile,
  insight,
  relevantPaper = { type: "semantic_scholar" },
  isSelected,
}: {
  isMobile: boolean;
  insight: Insight;
  relevantPaper: RelevantPaper;
  isSelected: boolean;
}) {
  const currentYear = new Date().getFullYear();
  const citationCount = relevantPaper.citationCount || 0;
  const year = relevantPaper.year || 0;
  const publicationYear = relevantPaper.year || "N/A";
  const influentialCitations = relevantPaper.influentialCitations || 0;

  const tags = [];
  if (citationCount > 100) tags.push("Highly Cited");
  if (year >= currentYear - 3) tags.push("Recent Study");
  if (influentialCitations > 50) tags.push("Influential");

  const [expanded, setExpanded] = useState(false);

  const handleExpandClick = () => {
    if (relevantPaper.type === "semantic_scholar") {
      // Use relevantPaper's DOI first, fallback to insight.doi
      const doi = relevantPaper.doi || insight.doi;
      if (doi) {
        window.open(`https://doi.org/${doi}`, "_blank");
      }
    } else {
      setExpanded(!expanded);
    }
  };

  const relevance = relevantPaper?.relevance_score
    ? (relevantPaper.relevance_score * 100).toFixed(0)
    : "N/A";

  return (
    <Card className={` px-[10px] py-4  ${isSelected ? "bg-gray-300" : ""}`}>
      <CardHeader className="flex justify-between items-center px-[10px] pb-0">
        <h3 className="text-xl text-[#132435] font-semibold">
          {insight.insight}
        </h3>
      </CardHeader>
      <CardBody className="px-[10px]">
        <p className="text-[#686868] text-lg font-normal">
          {insight.insight_explanation}
        </p>
        <div className="flex gap-3 my-6 text-sm">
          <p className="text-[#686868] font-semibold">
            Authors: <AUTHORS>
            {Array.isArray(relevantPaper.authors)
              ? relevantPaper.authors.slice(0, 4).join(", ") +
                (relevantPaper.authors.length > 4 ? " et al." : "")
              : typeof relevantPaper.authors === "string"
                ? relevantPaper.authors
                : "Not provided"}
          </p>
          <p className="text-[#686868] font-semibold">DOI: {insight.doi}</p>
        </div>
        <div className="flex gap-2 my-4">
          <span
            className="px-2 py-1 text-xs font-medium rounded-full text-white"
            style={{
              background: "linear-gradient(135deg, #6D28D9, #8B5CF6)",
            }}
          >
            Citations: {citationCount}
          </span>
          <span
            className="px-2 py-1 text-xs font-medium rounded-full text-white"
            style={{
              background: "linear-gradient(135deg, #6D28D9, #8B5CF6)",
            }}
          >
            Year: {publicationYear}
          </span>
        </div>

        <div className="flex flex-wrap gap-2 mt-4">
          {tags.map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 text-xs font-medium rounded-full bg-[#88D84D] text-[#132435]"
            >
              {tag}
            </span>
          ))}
        </div>
        <div
          className={`flex items-center w-full h-full ${
            isMobile ? "flex-col" : ""
          }`}
        >
          <div className="mt-4 w-full flex flex-col gap-3">
            <div className="max-w-md flex justify-between">
              <p className="text-[#686868] ">Relevance Score:</p>
              <p className="text-[#686868] ">{relevance}</p>
            </div>
            <Progress
              value={relevance !== "N/A" ? parseInt(relevance, 10) : 0}
              className="max-w-md"
              color="success"
              size="sm"
              showValueLabel={false}
            />
          </div>
          <Button
            className="bg-[#88d84d] rounded-full py-2 h-auto text-white mt-4 min-w-36 shadow-md"
            onPress={handleExpandClick}
          >
            {relevantPaper.type === "semantic_scholar"
              ? "View Paper"
              : expanded
                ? "Collapse"
                : "Expand"}
          </Button>
        </div>
      </CardBody>
    </Card>
  );
}

export function DiscoverContent({
  isMobile,
  articleData,
  hasCredits,
  isPreloaded,
  showSignupBlur,
  subscriptionOptions,
}: {
  isMobile: boolean;
  articleData: ArticleData | null;
  hasCredits: boolean;
  isPreloaded: boolean;
  showSignupBlur: boolean;
  subscriptionOptions: Array<{
    title: string;
    price: string;
    billing: string;
    features: string[];
    priceId: string;
    trialDays: number;
    isPopular: boolean;
  }>;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuthStore();
  const [selectedInsightIndex, setSelectedInsightIndex] = useState<
    number | null
  >(null);
  const insightRefs = useRef<(HTMLDivElement | null)[]>([]);

  const summaryRef = useRef<HTMLHeadingElement | null>(null);

  useEffect(() => {
    // Ensure the element exists before calling scrollIntoView
    if (summaryRef.current) {
      setTimeout(() => {
        summaryRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100); // Slight delay to ensure the DOM is ready
    }
  }, []);

  const scrollToInsight = useCallback((index: number) => {
    setSelectedInsightIndex(index);
    if (insightRefs.current[index]) {
      const elementToScroll = insightRefs.current[index];
      elementToScroll?.scrollIntoView({
        behavior: "smooth",
      });
    }
  }, []);

  const handleSelect = async (
    priceId: string,
    trialDays: number,
    title: string,
    price: string,
  ) => {
    setIsLoading(true);
    try {
      // Track checkout initiated
      await handleInitiateCheckout(title);
      await trackCheckoutInitiated(
        user!.id,
        title,
        parseFloat(price.replace("$", "")),
      );

      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          priceId,
          trialDays,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }

      const { sessionId } = await response.json();
      const stripe = await loadStripe(STRIPE_PUBLIC_KEY);
      if (stripe) {
        await stripe.redirectToCheckout({
          sessionId,
        });
      }
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast.error("Failed to create checkout session. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (!articleData) {
    return null;
  }

  console.log({ showSignupBlur });

  return (
    <div className="xl:w-10/12 w-full lg:max-w-4xl  mx-auto text-black mt-5">
      {showSignupBlur && (
        <div className="relative">
          <div className="absolute inset-0 bg-white bg-opacity-90 z-10">
            <div className="lg:h-[800px] w-full flex flex-col justify-start items-center py-8 px-2 mt-40  bg-white ">
              <h3 className="text-2xl font-bold mb-2">
                Discover more insights by subscribing to Outread
              </h3>
              <p className="text-lg mb-6">
                — and our catalogue of over 1,000 articles
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                {subscriptionOptions.map((option, index) => (
                  <div
                    key={index}
                    className={`w-full max-w-sm bg-white rounded-lg  p-6 mb-4 relative ${
                      option.isPopular
                        ? "border-4 border-[#88D84D]"
                        : "border-gray-200 border-1 shadow-md"
                    }`}
                    style={{ backgroundColor: "white" }}
                  >
                    {option.isPopular && (
                      <div className="absolute top-0 right-0 bg-[#88D84D] text-[#132435] px-3 py-1 rounded-bl-xl rounded-tr-sm text-lg">
                        MOST POPULAR
                      </div>
                    )}
                    <h4 className="text-xl font-semibold mb-2">
                      {option.title}
                    </h4>
                    <p className="text-lg font-bold mb-2">{option.price}</p>
                    <p className="text-sm mb-4">{option.billing}</p>
                    <ul className="list-none space-y-2 mb-6">
                      {option.features.map((feature, idx) => (
                        <li key={idx}>✓ {feature}</li>
                      ))}
                    </ul>
                    <Button
                      className="bg-[#88d84d] rounded-full p-4 h-auto text-white w-full shadow-md"
                      onPress={() =>
                        handleSelect(
                          option.priceId,
                          option.trialDays,
                          option.title,
                          option.price,
                        )
                      }
                      disabled={isLoading}
                    >
                      {isLoading ? "Processing..." : "Get Started"}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
      <div
        className={`text-[#fff] py-4 rounded-md ${
          showSignupBlur ? "filter blur-sm" : ""
        }`}
      >
        <h2 className="text-2xl font-semibold mb-4 ">Summary</h2>
        <ReferencedText
          text={articleData.summary.summarised_response}
          onReferenceClick={scrollToInsight}
        />
        <Divider className="mt-16 h-[1.72px]  bg-[#D9D9D9]" />
      </div>
      {articleData.summary.insight.length > 0 && (
        <>
          <h2 className="text-2xl font-semibold mb-8 mt-12 text-[#ffffff]">
            Insights
          </h2>
          <div
            className={`flex flex-col gap-14 mb-14 ${
              showSignupBlur ? "filter blur-sm" : ""
            }`}
          >
            {articleData.summary.insight.map((insight, index) => (
              <div
                key={index}
                ref={(el) => {
                  if (el) insightRefs.current[index] = el;
                }}
              >
                <InsightCard
                  isMobile={isMobile}
                  insight={insight}
                  relevantPaper={
                    articleData.relevant_papers[index] || {
                      type: "semantic_scholar",
                    }
                  }
                  isSelected={index === selectedInsightIndex}
                />
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
