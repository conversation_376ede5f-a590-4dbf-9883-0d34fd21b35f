"use client";

import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faBusinessTime,
  faHeartbeat,
  faAtom,
  faFlask,
  faBook,
  faMicroscope,
  faLaptopCode,
  faGlobeAmericas,
  faEllipsisH,
} from "@fortawesome/free-solid-svg-icons";
import { useRouter } from "next/navigation";

const categories = [
  { name: "Business", icon: faBusinessTime },
  { name: "Health", icon: faHeartbeat },
  { name: "Physics", icon: faAtom },
  { name: "Chemical Science", icon: faFlask },
  { name: "Humanities", icon: faBook },
  { name: "Social Science", icon: faMicroscope },
  { name: "Technology", icon: faLaptopCode },
  {
    name: "Earth Science",
    icon: faGlobeAmericas,
  },
  { name: "Other", icon: faEllipsisH },
];

const CategoryButtons: React.FC = () => {
  const router = useRouter();

  const handleClick = (category: string) => {
    router.push(`/category/${category}`);
  };

  return (
    <div className="w-full mb-[45px] lg:mb-[60px]">
      <h1 className="w-full text-2xl"> Categories </h1>
      <div className="w-full overflow-x-auto scrollbar-hide pt-4">
        <div className="grid grid-cols-3 gap-4 mx-auto w-[800px] lg:w-[1100px]">
          {categories.map((category) => (
            <button
              key={category.name}
              className="flex flex-row items-center justify-center w-[250px] lg:w-[350px] h-[56px] rounded-lg bg-[#27394F] pointer-cursor"
              onClick={() => handleClick(category.name)}
            >
              <FontAwesomeIcon
                icon={category.icon}
                className="w-5 h-5 text-md mx-2 my-auto"
              />
              <span className="text-md font-medium text-center">
                {category.name}
              </span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CategoryButtons;
