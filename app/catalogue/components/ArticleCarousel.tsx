"use client";
import React, { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faChevronLeft,
  faChevronRight,
} from "@fortawesome/free-solid-svg-icons";
import Image from "next/image";
import { BookmarkButton } from "./BookmarkButton";
import Link from "next/link";
import { Headphones } from "lucide-react";

interface Article {
  id: string;
  slug: string;
  title: string;
  audioId: string | null;
  categories: { name: string }[];
  estimatedReadingTime: number;
  articleImage: {
    src: string;
  };
  isBookmarked: boolean;
}

interface ArticleCarouselProps {
  articles: Article[];
}

const ArticleCarousel: React.FC<ArticleCarouselProps> = ({ articles }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    slidesToScroll: 1,
    align: "start",
    containScroll: "trimSnaps",
    breakpoints: {
      "(min-width: 768px)": { slidesToScroll: 3 },
      "(min-width: 1024px)": {
        slidesToScroll: 5,
      },
    },
  });
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false);
  const [validArticles, setValidArticles] = useState<Article[]>([]);

  const scrollPrev = useCallback(
    () => emblaApi && emblaApi.scrollPrev(),
    [emblaApi],
  );
  const scrollNext = useCallback(
    () => emblaApi && emblaApi.scrollNext(),
    [emblaApi],
  );

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setPrevBtnEnabled(emblaApi.canScrollPrev());
    setNextBtnEnabled(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
  }, [emblaApi, onSelect]);

  useEffect(() => {
    setValidArticles(articles);
  }, [articles]);

  const handleImageError = (articleId: string) => {
    setValidArticles((prevArticles) =>
      prevArticles.filter((article) => article.id !== articleId),
    );
  };

  if (!validArticles || validArticles.length === 0) {
    return null;
  }

  return (
    <div className="relative w-full [350px]">
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex space-x-[8px]">
          {validArticles.map((article) => (
            <div
              key={article.id}
              className="flex-[0_0_50%] md:flex-[0_0_33.333%] lg:flex-[0_0_20%] flex flex-col items-center justify-start h-full"
            >
              <div className="bg-[#132435] text-white overflow-hidden w-full h-full">
                <Link href={`/article/${article.slug}`}>
                  <div className="w-[150px] h-[220px] lg:w-[178px] lg:h-[238px] flex flex-col items-center justify-start overflow-hidden">
                    <Image
                      src={article.articleImage.src}
                      alt={article.title}
                      width={178}
                      height={238}
                      style={{
                        width: "100%",
                        height: "auto",
                      }}
                      className="w-full h-full object-cover rounded-2xl"
                      onError={() => handleImageError(article.id)}
                    />
                  </div>
                  <div className="w-[150px] lg:w-[178px] h-full flex flex-row justify-start items-start my-2">
                    <h3 className="w-[90%] text-sm min-h-[40px] font-semibold text-wrap line-clamp-4">
                      {article.title}
                    </h3>
                    <BookmarkButton
                      articleId={article.id}
                      initialIsBookmarked={article.isBookmarked}
                    />
                  </div>
                  <div className="flex justify-start items-start w-full space-x-2">
                    <div className="text-[10px] w-auto text-center bg-gray-500 text-white p-1">
                      {article.estimatedReadingTime} min{" "}
                    </div>
                    {article.audioId && (
                      <div className="text-[10px] w-auto text-center bg-gray-500 text-white p-[4px]">
                        <Headphones size={16} />
                      </div>
                    )}
                  </div>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
      <button
        className={`absolute top-[33%] left-[-49px] transform -translate-y-1/2 ${prevBtnEnabled ? "text-[#88D84D]" : "text-white"} text-4xl rounded-full z-10`}
        onClick={scrollPrev}
        disabled={!prevBtnEnabled}
      >
        <FontAwesomeIcon icon={faChevronLeft} />
      </button>
      <button
        className={`absolute top-[33%] right-[-49px] transform -translate-y-1/2 ${nextBtnEnabled ? "text-[#88D84D]" : "text-white"} text-4xl rounded-full z-10`}
        onClick={scrollNext}
        disabled={!nextBtnEnabled}
      >
        <FontAwesomeIcon icon={faChevronRight} />
      </button>
    </div>
  );
};

export default ArticleCarousel;
