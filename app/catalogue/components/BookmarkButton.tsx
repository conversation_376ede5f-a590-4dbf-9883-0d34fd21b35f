"use client";

import React, { useState, useEffect } from "react";
import { Bookmark } from "lucide-react";
import { toggleBookmark } from "../../actions/toggleBookmark";

interface BookmarkButtonProps {
  articleId: string;
  initialIsBookmarked: boolean;
}

function getLocalStorageBookmark(articleId: string): boolean | null {
  if (typeof window === "undefined") return null;
  const itemStr = localStorage.getItem(`bookmark_${articleId}`);
  if (!itemStr) return null;

  const item = JSON.parse(itemStr);
  const now = new Date();

  if (now.getTime() > item.expiry) {
    localStorage.removeItem(`bookmark_${articleId}`);
    return null;
  }

  return item.value;
}

function setLocalStorageBookmark(articleId: string, isBookmarked: boolean) {
  const now = new Date();
  const item = {
    value: isBookmarked,
    expiry: now.getTime() + 3600000, // 1 hour from now
  };

  console.log("Setting bookmark");
  localStorage.setItem(`bookmark_${articleId}`, JSON.stringify(item));
}

export function BookmarkButton({
  articleId,
  initialIsBookmarked,
}: BookmarkButtonProps) {
  const [isBookmarked, setIsBookmarked] = useState(initialIsBookmarked);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const localStorageBookmark = getLocalStorageBookmark(articleId);
    if (localStorageBookmark !== null) {
      setIsBookmarked(localStorageBookmark);
    }
  }, [articleId]);

  const handleToggleBookmark = async (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
  ) => {
    e.preventDefault();
    e.stopPropagation();

    console.log("On clikc");

    // Optimistically update the UI and local storage
    const newBookmarkState = !isBookmarked;
    setIsBookmarked(newBookmarkState);
    setLocalStorageBookmark(articleId, newBookmarkState);
    setIsLoading(true);

    try {
      const result = await toggleBookmark(articleId);
      if (!result.success) {
        // Revert the state if the server request fails
        setIsBookmarked(!newBookmarkState);
        setLocalStorageBookmark(articleId, !newBookmarkState);
        console.error("Failed to toggle bookmark:", result.error);
        // You could add a toast notification here
      }
    } catch (error) {
      // Revert the state if there's an error
      setIsBookmarked(!newBookmarkState);
      setLocalStorageBookmark(articleId, !newBookmarkState);
      console.error("Error in toggleBookmark:", error);
      // You could add a toast notification here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleToggleBookmark}
      disabled={isLoading}
      className="hover:scale-[1.25] rounded-full transition-all duration-150"
      aria-label={isBookmarked ? "Remove bookmark" : "Add bookmark"}
    >
      {isBookmarked ? (
        <Bookmark fill="white" className="w-7 h-7" />
      ) : (
        <Bookmark className="w-7 h-7" />
      )}
    </button>
  );
}
