"use client";

import React, { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { Image as ArticleImage } from "@prisma/client";
import { Button } from "@nextui-org/react";
import Image from "next/image";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faBook,
  faChevronLeft,
  faChevronRight,
} from "@fortawesome/free-solid-svg-icons";
import { useClientMediaQuery } from "@/hooks/useClientMediaQuery";
import { useRouter } from "next/navigation";

interface Article {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  articleImage: ArticleImage;
  categories: { name: string }[];
  altMetricScore: number;
}

export default function Carousel({
  featuredArticles,
}: {
  featuredArticles: Article[];
}) {
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: false,
      align: "start",
      containScroll: "trimSnaps",
    },
    // [Autoplay()]
  );
  const router = useRouter();
  const isMobile = useClientMediaQuery("(max-width: 768px)");
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setPrevBtnEnabled(emblaApi.canScrollPrev());
    setNextBtnEnabled(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);
  }, [emblaApi, onSelect]);

  return (
    <div className="relative w-full lg:w-[1100px] mb-[45px] lg:mb-[60px]">
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex">
          {featuredArticles.map((article) => (
            <div key={article.id} className="flex-[0_0_100%] min-w-0 ">
              <div className="w-auto h-[195px] lg:h-[390px] lg:p-4">
                <Link href={`/article/${article.slug}`}>
                  <div className="flex border-medium h-full border-gray-600  bg-[url('/Featured_Article_BG_Mobile.png')] lg:bg-[url('/Featured_Article_BG_Web.png')] rounded-2xl overflow-hidden shadow-gray-700 shadow-[0_0_11.5px_5.0px]">
                    <div className="flex-shrink-0 lg:w-[250px] w-[150px] lg:ml-8 lg:mt-4 lg:mb-4 p-4">
                      <div className="relative w-full border-2 border-white rounded-lg overflow-hidden">
                        <Image
                          src={article.articleImage.src}
                          alt={article.title}
                          className="object-contain"
                          width={250}
                          height={200}
                        />
                      </div>
                    </div>
                    <div className="flex flex-col lg:ml-4 justify-center items-start p-2 lg:p-4 flex-grow w-full">
                      <h1 className="text-[14px] lg:text-4xl mb-2 font-semibold kg:mb-4 text-start">
                        {article.title}
                      </h1>
                      <p className="text-[12px] lg:text-2xl lg:mb-6 text-start">
                        {article.subtitle}
                      </p>
                      <Button
                        variant="light"
                        className="p-0 text-[#88D84D] text-sm md:text-lg lg:text-2xl"
                        startContent={<FontAwesomeIcon icon={faBook} />}
                        onPress={() => router.push(`/article/${article.slug}`)}
                      >
                        Start Reading
                      </Button>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
      {isMobile ? (
        ""
      ) : (
        <div>
          <button
            className={`absolute top-1/2 left-[-25px] transform -translate-y-1/2 ${prevBtnEnabled ? "text-[#88D84D]" : "text-white"} text-4xl rounded-full z-10`}
            onClick={scrollPrev}
            disabled={!prevBtnEnabled}
          >
            <FontAwesomeIcon icon={faChevronLeft} />
          </button>
          <button
            className={`absolute top-1/2 right-[-25px] transform -translate-y-1/2 ${nextBtnEnabled ? "text-[#88D84D]" : "text-white"} text-4xl rounded-full z-10`}
            onClick={scrollNext}
            disabled={!nextBtnEnabled}
          >
            <FontAwesomeIcon icon={faChevronRight} />
          </button>
        </div>
      )}
    </div>
  );
}
