"use client";
import React, { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faChevronLeft,
  faChevronRight,
} from "@fortawesome/free-solid-svg-icons";
import Image from "next/image";
import Link from "next/link";
import { GetPlaylistsResponse } from "../actions/getPlaylists";
import { trackPlaylistView } from "@/app/actions/trackPlaylistView";

interface PlaylistCarouselProps {
  playlists: GetPlaylistsResponse;
}

const PlaylistCarousel: React.FC<PlaylistCarouselProps> = ({ playlists }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    slidesToScroll: 1,
    align: "start",
    containScroll: "trimSnaps",
    breakpoints: {
      "(min-width: 768px)": { slidesToScroll: 3 },
      "(min-width: 1024px)": {
        slidesToScroll: 4,
      },
    },
  });

  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false);

  const scrollPrev = useCallback(
    () => emblaApi && emblaApi.scrollPrev(),
    [emblaApi],
  );
  const scrollNext = useCallback(
    () => emblaApi && emblaApi.scrollNext(),
    [emblaApi],
  );

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setPrevBtnEnabled(emblaApi.canScrollPrev());
    setNextBtnEnabled(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);

    // Cleanup listener on unmount
    return () => {
      emblaApi.off("select", onSelect);
    };
  }, [emblaApi, onSelect]);

  if (!playlists || !playlists.playlists || playlists.playlists.length === 0) {
    return null;
  }

  return (
    <div className="relative w-full h-[290px]">
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex space-x-[32px]">
          {playlists.playlists.map((playlist) => (
            <div
              key={playlist.id}
              className="flex-[0_0_50%] md:flex-[0_0_33.333%] lg:flex-[0_0_25%] flex flex-col items-center justify-start h-full"
            >
              <div className="bg-[#132435] text-white overflow-hidden w-full h-full">
                <Link
                  href={`/collections/${playlist.id}`}
                  onClick={() => trackPlaylistView(playlist.id)}
                >
                  <div className="w-[220px] h-[220px] lg:w-[240px] lg:h-[240px] flex flex-col items-center justify-start overflow-hidden">
                    <Image
                      src={playlist.imageUrl || "/default-playlist-cover.jpg"} // Add a default image fallback
                      alt={playlist.name}
                      width={240}
                      height={240}
                      style={{
                        width: "100%",
                        height: "auto",
                      }}
                      className="w-full h-full object-cover rounded-2xl"
                    />
                  </div>
                  <div className="w-[150px] lg:w-[178px] h-full flex flex-row justify-start items-start my-2">
                    <h3 className="w-[90%] text-sm min-h-[40px] font-semibold text-wrap">
                      {playlist.name}
                    </h3>
                  </div>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
      <button
        className={`absolute top-[33%] left-[-49px] transform -translate-y-1/2 ${prevBtnEnabled ? "text-[#88D84D]" : "text-white"} text-4xl rounded-full z-10 transition-colors duration-200`}
        onClick={scrollPrev}
        disabled={!prevBtnEnabled}
        aria-label="Previous slides"
      >
        <FontAwesomeIcon icon={faChevronLeft} />
      </button>
      <button
        className={`absolute top-[33%] right-[-49px] transform -translate-y-1/2 ${nextBtnEnabled ? "text-[#88D84D]" : "text-white"} text-4xl rounded-full z-10 transition-colors duration-200`}
        onClick={scrollNext}
        disabled={!nextBtnEnabled}
        aria-label="Next slides"
      >
        <FontAwesomeIcon icon={faChevronRight} />
      </button>
    </div>
  );
};

export default PlaylistCarousel;
