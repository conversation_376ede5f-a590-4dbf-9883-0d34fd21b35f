import dynamicImport from "next/dynamic";
import { Suspense } from "react";
import {
  ArticleCarouselSkeleton,
  CategoryButtonsSkeleton,
  HomeSkeleton,
} from "./loading";
import { FeaturedArticlesResponse } from "./actions/getFeaturedArticle";
import { redirect } from "next/navigation";
import { GetPlaylistsResponse } from "./actions/getPlaylists";
import { LatestArticlesResponse } from "./actions/getLatestArticles";
import { getUser } from "@/lib/cache/getUser";
import {
  getCachedFeaturedArticles,
  getCachedLatestArticles,
  getCachedTopArticles,
  getCachedPlaylists,
} from "@/lib/serverInit";
import {
  getUserBookmarks,
  mergeArticlesWithBookmarks,
} from "@/lib/userBookmarks";
import { HomepageArticleWithBookmark } from "@/types/article";
import { error } from "console";

const CategoryButtons = dynamicImport(
  () => import("./components/CategoryButtons"),
  {
    loading: () => <CategoryButtonsSkeleton />,
  },
);
const ArticleCarousel = dynamicImport(
  () => import("./components/ArticleCarousel"),
  {
    loading: () => <ArticleCarouselSkeleton />,
  },
);
const FeaturedCarousel = dynamicImport(
  () => import("./components/FeaturedCarousel"),
  {
    loading: () => <ArticleCarouselSkeleton />,
  },
);
const PlaylistCarousel = dynamicImport(
  () => import("./components/PlaylistsCarousel"),
  {
    loading: () => <ArticleCarouselSkeleton />,
  },
);

interface TopArticlesResponse {
  topArticlesByCategory: {
    category: { name: string; ranking: number };
    articles: HomepageArticleWithBookmark[];
  }[];
}

function TopArticlesByCategory({
  topArticlesByCategory,
}: {
  topArticlesByCategory: TopArticlesResponse["topArticlesByCategory"];
}) {
  return (
    <div className="w-full ">
      {topArticlesByCategory.map(({ category, articles }) =>
        category.name === "Featured" ? (
          ""
        ) : (
          <div key={category.name} className="my-4 mb-[30px] lg:mb-[45px]">
            <div className="flex items-start justify-between mb-1">
              <h2 className="text-2xl mb-5 text-white">{category.name}</h2>
              <a
                href={`/category/${category.name}`}
                className="text-2xl font-normal text-[#88D84D] "
              >
                View All
              </a>
            </div>
            <ArticleCarousel key={category.name} articles={articles} />
          </div>
        ),
      )}
    </div>
  );
}

function TopArticlesSection({
  topArticlesByCategory,
}: {
  topArticlesByCategory: TopArticlesResponse["topArticlesByCategory"];
}) {
  return (
    <TopArticlesByCategory topArticlesByCategory={topArticlesByCategory} />
  );
}

function FeaturedSection({
  featuredArticles,
}: {
  featuredArticles: HomepageArticleWithBookmark[];
}) {
  return (
    <>
      <h1 className="w-full text-2xl mb-4">Featured Summaries</h1>
      <FeaturedCarousel featuredArticles={featuredArticles} />
    </>
  );
}

const CategorySection = () => <CategoryButtons />;

function LatestArticlesSection({
  latestArticles,
}: {
  latestArticles: HomepageArticleWithBookmark[];
}) {
  return (
    <div className="w-full mb-[45px] lg:mb-[60px]">
      <div className="flex items-start justify-between mb-1">
        <h2 className="text-2xl mb-5 text-white">Latest Articles</h2>
        <a
          href={`/all-articles`}
          className="text-2xl font-normal text-[#88D84D] "
        >
          View All
        </a>
      </div>
      <ArticleCarousel articles={latestArticles} />
    </div>
  );
}

function PlaylistsSection({ playlists }: { playlists: GetPlaylistsResponse }) {
  return (
    <div className="w-full mb-[15px] lg:mb-[30px] ">
      <div className="flex items-start justify-between">
        <h2 className="text-2xl mb-5 text-white">Collections</h2>
        <a
          href={`/collections`}
          className="text-2xl font-normal text-[#88D84D] "
        >
          View All
        </a>
      </div>
      <PlaylistCarousel playlists={playlists} />
    </div>
  );
}

const getDisplayPageData = async (userId: string) => {
  try {
    const [
      featuredArticles,
      latestArticles,
      topArticles,
      playlists,
      userBookmarks,
    ] = await Promise.all([
      getCachedFeaturedArticles(),
      getCachedLatestArticles(),
      getCachedTopArticles(),
      getCachedPlaylists(),
      getUserBookmarks(userId),
    ]);

    console.log("User Bookmarks:", userBookmarks);
    console.log(
      "Featured Articles IDs:",
      featuredArticles.featuredArticles.map((a) => a.id),
    );

    const mergedFeaturedArticles = mergeArticlesWithBookmarks(
      featuredArticles.featuredArticles,
      userBookmarks,
    );
    console.log(
      "Merged Featured Articles:",
      mergedFeaturedArticles.map((a) => ({
        id: a.id,
        isBookmarked: a.isBookmarked,
      })),
    );

    const mergedLatestArticles = mergeArticlesWithBookmarks(
      latestArticles.latestArticles,
      userBookmarks,
    );

    const mergedTopArticles = {
      topArticlesByCategory: topArticles.topArticlesByCategory.map(
        (category: { category: any; articles: any }) => ({
          ...category,
          articles: mergeArticlesWithBookmarks(
            category.articles,
            userBookmarks,
          ),
        }),
      ),
    };

    return {
      featuredArticles: {
        featuredArticles: mergedFeaturedArticles,
      },
      latestArticles: {
        latestArticles: mergedLatestArticles,
      },
      topArticles: mergedTopArticles,
      playlists,
    };
  } catch (error) {
    console.error("Error in getDisplayPageData:", error);
    throw error;
  }
};

function HomeContent({
  featuredArticles,
  latestArticles,
  topArticles,
  playlists,
}: {
  featuredArticles: FeaturedArticlesResponse;
  latestArticles: LatestArticlesResponse;
  topArticles: TopArticlesResponse;
  playlists: GetPlaylistsResponse;
}) {
  return (
    <div className="w-full bg-[#132435] text-white min-h-screen mx-auto py-4 overflow-x-hidden">
      <div className="p-[20px] lg:w-[1100px] bg-[#132435] md:w-[756px] mx-auto flex flex-col items-center justify-start text-white h-full ">
        <Suspense fallback={<HomeSkeleton />}>
          <FeaturedSection
            featuredArticles={featuredArticles.featuredArticles}
          />
        </Suspense>

        <Suspense fallback={<CategoryButtonsSkeleton />}>
          <CategorySection />
        </Suspense>

        <Suspense fallback={<ArticleCarouselSkeleton />}>
          <LatestArticlesSection
            latestArticles={latestArticles.latestArticles}
          />
        </Suspense>

        <Suspense fallback={<ArticleCarouselSkeleton />}>
          <PlaylistsSection playlists={playlists} />
        </Suspense>

        <Suspense fallback={<ArticleCarouselSkeleton />}>
          <TopArticlesSection
            topArticlesByCategory={topArticles.topArticlesByCategory}
          />
        </Suspense>
      </div>
    </div>
  );
}

export default async function DisplayPage() {
  try {
    const user = await getUser();
    if (!user) {
      redirect("/login");
    }
    const { featuredArticles, latestArticles, topArticles, playlists } =
      await getDisplayPageData(user.id);

    return (
      <Suspense fallback={<HomeSkeleton />}>
        <HomeContent
          featuredArticles={featuredArticles}
          latestArticles={latestArticles}
          topArticles={topArticles}
          playlists={playlists}
        />
      </Suspense>
    );
  } catch (error) {
    console.error("Error in DisplayPage:", error);
    throw error;
  }
}
