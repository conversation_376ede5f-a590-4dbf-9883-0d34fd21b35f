import prisma from "@/lib/prisma";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Image as ArticleImage,
  BookmarkedArticle,
} from "@prisma/client";

interface Article {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  estimatedReadingTime: number;
  articleImage: ArticleImage;
  categories: { name: string }[];
  altMetricScore: number;
  isBookmarked: boolean;
}

function mapArticleToResponse(
  article: any,
  bookmarkedArticles: BookmarkedArticle[],
) {
  return {
    id: article.id,
    slug: article.slug,
    title: article.title,
    subtitle: article.subtitle,
    articleImage: article.articleImage,
    categories: article.categories,
    altMetricScore: article.altMetricScore,
    estimatedReadingTime: article.estimatedReadingTime,
    isBookmarked: bookmarkedArticles.some(
      (bookmarkedArticle) => bookmarkedArticle.articleId === article.id,
    ),
  };
}

export async function getGreenEnergyArticles(): Promise<Article[]> {
  const dois = [
    "10.1007/s10311-023-01591-5",
    "10.1016/j.renene.2023.119236",
    "10.1080/13504509.2023.2268569",
    "10.1016/j.rser.2023.113941",
    "10.1016/j.renene.2023.119417",
    "10.3390/su15021418",
    "10.1016/j.ijhydene.2022.09.061",
    "10.1038/s41560-023-01283-y",
    "10.1049/stg2.12142",
    "10.1016/j.esr.2022.100939",
    "10.1016/j.gr.2023.03.002",
  ];

  // Fetch articles from the database using the DOIs
  const articles = await prisma.article.findMany({
    where: {
      doi: {
        in: dois,
      },
    },
    include: {
      articleImage: true,
      categories: true,
    },
  });

  console.log(articles);

  return articles.map((article) => mapArticleToResponse(article, []));
}
