import prisma from "@/lib/prisma";
import { Image as ArticleImage, BookmarkedArticle } from "@prisma/client";

interface Article {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  audioId: string | null;
  estimatedReadingTime: number;
  articleImage: ArticleImage;
  categories: { name: string }[];
  altMetricScore: number;
  isBookmarked: boolean;
  createdAt: Date; // Added createdAt to the interface
}

export interface LatestArticlesResponse {
  latestArticles: Article[]; // Changed from featuredArticles to recentArticles
}

function mapArticleToResponse(
  article: any,
  bookmarkedArticles: BookmarkedArticle[],
) {
  return {
    id: article.id,
    slug: article.slug,
    title: article.title,
    subtitle: article.subtitle,
    articleImage: article.articleImage,
    audioId: article.audioId,
    categories: article.categories,
    altMetricScore: article.altMetricScore,
    estimatedReadingTime: article.estimatedReadingTime,
    createdAt: article.createdAt, // Added createdAt to the response
    isBookmarked: bookmarkedArticles.some(
      (bookmarkedArticle) => bookmarkedArticle.articleId === article.id,
    ),
  };
}

export async function getLatestArticles({
  userId,
}: {
  userId: string;
}): Promise<LatestArticlesResponse> {
  // Renamed function
  try {
    const bookmarkedArticles = await prisma.bookmarkedArticle.findMany({
      where: {
        userId: userId,
      },
    });

    // Fetch recent articles instead of featured articles
    const latestArticles = await prisma.article.findMany({
      take: 15, // Limit to 15 articles
      orderBy: {
        createdAt: "desc", // Order by creation date, most recent first
      },
      select: {
        id: true,
        title: true,
        subtitle: true,
        slug: true,
        audioId: true,
        estimatedReadingTime: true,
        articleImage: true,
        categories: {
          select: {
            name: true,
          },
        },
        altMetricScore: true,
        createdAt: true, // Added createdAt to the selection
      },
    });

    return {
      latestArticles: latestArticles.map((article) =>
        mapArticleToResponse(article, bookmarkedArticles),
      ),
    };
  } catch (error) {
    console.error("Error fetching recent articles:", error);
    throw error;
  }
}
