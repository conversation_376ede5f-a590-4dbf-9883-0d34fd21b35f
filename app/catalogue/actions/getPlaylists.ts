import prisma from "@/lib/prisma";

interface Category {
  name: string;
}

interface Article {
  categories: Category[];
}

interface Playlist {
  id: string;
  name: string;
  description: string;
  image: {
    url: string;
  } | null;
  articles: Article[];
}

interface PlaylistResponse {
  id: string;
  name: string;
  description: string;
  imageUrl: string | null;
  articleCount: number;
  categories: string[];
}

export interface GetPlaylistsResponse {
  playlists: PlaylistResponse[];
}

export async function getPlaylists(
  playlistId?: string | undefined,
): Promise<GetPlaylistsResponse> {
  try {
    let playlists;
    if (playlistId) {
      playlists = [
        await prisma.playlist.findUnique({
          where: {
            id: playlistId,
          },
          include: {
            image: true,
            articles: {
              include: {
                categories: true,
              },
            },
          },
        }),
      ];
    } else {
      playlists = await prisma.playlist.findMany({
        select: {
          id: true,
          image: true,
          name: true,
          description: true,
          articles: {
            include: {
              categories: true,
            },
          },
        },
      });
    }

    // Create a Set to store all unique category names across playlists
    const allCategoriesSet = new Set<string>();

    // Transform playlists and collect categories
    const transformedPlaylists = playlists.map((playlist: any) => {
      // Create a Set for this playlist's unique categories
      const playlistCategoriesSet = new Set<string>();

      // Process all articles in this playlist
      playlist.articles.forEach((article: Article) => {
        article.categories.forEach((category) => {
          playlistCategoriesSet.add(category.name);
          allCategoriesSet.add(category.name);
        });
      });

      return {
        id: playlist.id,
        imageUrl: playlist.image?.src ?? null,
        name: playlist.name,
        description: playlist.description,
        articleCount: playlist.articles.length,
        categories: Array.from(playlistCategoriesSet),
      };
    });

    return {
      playlists: transformedPlaylists,
    };
  } catch (error) {
    console.error("Error fetching playlists:", error);
    throw error;
  }
}
