import prisma from "@/lib/prisma";
import {
  Image as ArticleImage,
  BookmarkedArticle,
  Catergory,
} from "@prisma/client";

interface TopArticlesResponse {
  latestArticles: Article[];
  topArticlesByCategory: {
    category: { name: string; ranking: number };
    articles: Article[];
  }[];
}

interface Article {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  audioId: string | null;
  estimatedReadingTime: number;
  articleImage: ArticleImage;
  categories: { name: string }[];
  altMetricScore: number;
  isBookmarked: boolean;
}

function mapArticleToResponse(
  article: any,
  bookmarkedArticles: BookmarkedArticle[],
) {
  return {
    id: article.id,
    slug: article.slug,
    title: article.title,
    subtitle: article.subtitle,
    articleImage: article.articleImage,
    categories: article.categories,
    altMetricScore: article.altMetricScore,
    audioId: article.audioId,
    estimatedReadingTime: article.estimatedReadingTime,
    isBookmarked: bookmarkedArticles.some(
      (bookmarkedArticle) => bookmarkedArticle.articleId === article.id,
    ),
  };
}

export async function getTopArticles({
  userId,
}: {
  userId: string;
}): Promise<TopArticlesResponse> {
  try {
    // Get initial data in parallel
    const [bookmarkedArticles, latestArticles, categories] = await Promise.all([
      prisma.bookmarkedArticle.findMany({
        where: {
          userId: userId,
        },
      }),
      prisma.article.findMany({
        orderBy: {
          createdAt: "desc",
        },
        select: {
          id: true,
          title: true,
          subtitle: true,
          slug: true,
          audioId: true,
          estimatedReadingTime: true,
          altMetricScore: true,
          articleImage: true,
          categories: {
            select: {
              name: true,
            },
          },
        },
        take: 20,
      }),
      prisma.catergory.findMany(),
    ]);

    // Get today's date for seeding
    const today = new Date();
    const seed =
      today.getFullYear() * 10000 +
      (today.getMonth() + 1) * 100 +
      today.getDate();

    console.log("Seed for random offset", seed);
    // Fetch top articles for each category in parallel
    const topArticlesByCategory = await Promise.all(
      categories.map(async (category: Catergory) => {
        // Get the total count of articles for this category
        const totalArticles = await prisma.article.count({
          where: {
            categories: {
              some: {
                name: category.name,
              },
            },
          },
        });

        // Calculate a random offset, but ensure we have at least 15 articles after the offset
        const maxOffset = Math.max(0, totalArticles - 15);

        // Use deterministic random based on date and category
        const categoryBasedSeed = seed + category.id.charCodeAt(0);
        const randomValue = Math.sin(categoryBasedSeed) * 10000;
        const randomOffset = Math.abs(
          Math.floor(randomValue % (maxOffset + 1)),
        );

        const articles = await prisma.article.findMany({
          where: {
            categories: {
              some: {
                id: category.id,
              },
            },
          },
          select: {
            id: true,
            title: true,
            subtitle: true,
            slug: true,
            altMetricScore: true,
            audioId: true,
            articleImage: true,
            estimatedReadingTime: true,
            categories: {
              select: {
                name: true,
                ranking: true,
              },
            },
          },
          orderBy: {
            altMetricScore: "desc",
          },
          skip: randomOffset,
          take: 15,
        });

        return {
          category: {
            name: category.name,
            ranking: category.ranking,
          },
          articles: articles.map((article) =>
            mapArticleToResponse(article, bookmarkedArticles),
          ),
        };
      }),
    );

    return {
      latestArticles: latestArticles.map((article) =>
        mapArticleToResponse(article, bookmarkedArticles),
      ),
      topArticlesByCategory: topArticlesByCategory.sort(
        (a, b) => a.category.ranking - b.category.ranking,
      ),
    };
  } catch (error) {
    console.error("Error fetching top articles:", error);
    throw error;
  }
}
