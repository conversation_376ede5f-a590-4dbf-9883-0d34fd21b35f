import prisma from "@/lib/prisma";
import { Image as ArticleImage, BookmarkedArticle } from "@prisma/client";

interface Article {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  audioId: string | null;
  estimatedReadingTime: number;
  articleImage: ArticleImage;
  categories: { name: string }[];
  altMetricScore: number;
  isBookmarked: boolean;
}

export interface FeaturedArticlesResponse {
  featuredArticles: Article[];
}

function mapArticleToResponse(
  article: any,
  bookmarkedArticles: BookmarkedArticle[],
) {
  return {
    id: article.id,
    slug: article.slug,
    title: article.title,
    subtitle: article.subtitle,
    articleImage: article.articleImage,
    audioId: article.audioId,
    categories: article.categories,
    altMetricScore: article.altMetricScore,
    estimatedReadingTime: article.estimatedReadingTime,
    isBookmarked: bookmarkedArticles.some(
      (bookmarkedArticle) => bookmarkedArticle.articleId === article.id,
    ),
  };
}

export async function getFeaturedArticles({
  userId,
}: {
  userId: string;
}): Promise<FeaturedArticlesResponse> {
  try {
    const bookmarkedArticles = await prisma.bookmarkedArticle.findMany({
      where: {
        userId: userId,
      },
    });

    // Fetch featured articles
    const featuredArticles = await prisma.article.findMany({
      where: {
        categories: {
          some: {
            name: "Featured",
          },
        },
      },
      select: {
        id: true,
        title: true,
        subtitle: true,
        slug: true,
        estimatedReadingTime: true,
        articleImage: true,
        audioId: true,
        categories: {
          select: {
            name: true,
          },
        },
        altMetricScore: true,
      },
    });

    return {
      featuredArticles: featuredArticles.map((article) =>
        mapArticleToResponse(article, bookmarkedArticles),
      ),
    };
  } catch (error) {
    console.error("Error fetching featured articles:", error);
    throw error;
  }
}
