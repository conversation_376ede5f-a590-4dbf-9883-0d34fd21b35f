import prisma from "@/lib/prisma";
import { unstable_cache } from "next/cache";
import { Article, Catergory, Image, Playlist } from "@prisma/client";

// Define the extended article type with additional fields
export interface ArticleWithRelations extends Article {
  articleImage: Image | null;
  categories: Catergory[];
  initialIsBookmarked: boolean;
}

// Define the complete playlist response type
export interface PlaylistWithRelations extends Playlist {
  image: Image | null;
  articles: ArticleWithRelations[];
}

export async function getPlaylistWithArticles({
  userId,
  playlistId,
}: {
  userId: string;
  playlistId: string;
}): Promise<PlaylistWithRelations | null> {
  const getCachedPlaylist = unstable_cache(
    async () => {
      try {
        const playlist = await prisma.playlist.findUnique({
          where: {
            id: playlistId,
          },
          include: {
            image: true,
            articles: {
              include: {
                articleImage: true,
                categories: true,
                bookmarkedBy: {
                  where: {
                    userId: userId,
                  },
                  select: {
                    userId: true,
                  },
                  take: 1,
                },
              },
            },
          },
        });

        if (!playlist) return null;

        // Map the articles with bookmarked status
        const articlesWithBookmarks = playlist.articles.map(
          (article): ArticleWithRelations => ({
            ...article,
            articleImage: article.articleImage,
            categories: article.categories,
            initialIsBookmarked: article.bookmarkedBy.length > 0,
          }),
        );

        return {
          ...playlist,
          articles: articlesWithBookmarks,
        };
      } catch (error) {
        console.error("Error fetching playlist with articles:", error);
        throw error;
      }
    },
    ["playlist-with-articles", playlistId, userId],
    {
      revalidate: 300, // Cache for 5 minutes
      tags: [`playlist-${playlistId}`, "articles"], // Tags for cache invalidation
    },
  );

  return getCachedPlaylist();
}
