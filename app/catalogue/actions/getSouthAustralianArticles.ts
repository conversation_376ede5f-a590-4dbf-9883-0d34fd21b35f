import prisma from "@/lib/prisma";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Image as ArticleImage,
  BookmarkedArticle,
} from "@prisma/client";

interface Article {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  audioId: string | null;
  estimatedReadingTime: number;
  articleImage: ArticleImage;
  categories: { name: string }[];
  altMetricScore: number;
  isBookmarked: boolean;
}

function mapArticleToResponse(
  article: any,
  bookmarkedArticles: BookmarkedArticle[],
) {
  return {
    id: article.id,
    slug: article.slug,
    title: article.title,
    subtitle: article.subtitle,
    articleImage: article.articleImage,
    audioId: article.audioId,
    categories: article.categories,
    altMetricScore: article.altMetricScore,
    estimatedReadingTime: article.estimatedReadingTime,
    isBookmarked: bookmarkedArticles.some(
      (bookmarkedArticle) => bookmarkedArticle.articleId === article.id,
    ),
  };
}

export async function getSouthAustralianArticles(): Promise<Article[]> {
  const dois = [
    "10.1183/23120541.00739-2023",
    "10.1001/jamapediatrics.2023.6790",
    "10.1016/j.biocon.2024.110843",
    "10.1016/j.jinf.2024.106243",
    "10.1007/s00125-024-06131-6",
    "10.1073/pnas.**********",
    "10.1007/s00394-024-03513-9",
    "10.1073/pnas.**********",
    "10.1038/s41467-024-45438-1",
    "10.1002/ece3.11535",
  ];

  // Fetch articles from the database using the DOIs
  const articles = await prisma.article.findMany({
    where: {
      doi: {
        in: dois,
      },
    },
    include: {
      articleImage: true,
      categories: true,
    },
  });

  console.log(articles);

  return articles.map((article) => mapArticleToResponse(article, []));
}
