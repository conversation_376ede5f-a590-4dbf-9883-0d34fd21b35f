import React from "react";

export const FeaturedCarouselSkeleton = () => (
  <div className="w-full h-[400px] bg-gray-800 rounded-lg animate-pulse mb-8">
    <div className="w-3/4 h-8 bg-gray-700 rounded mt-4 mx-4"></div>
    <div className="w-1/2 h-6 bg-gray-700 rounded mt-2 mx-4"></div>
  </div>
);

export const CategoryButtonsSkeleton = () => (
  <div className="flex gap-2 my-4 w-full overflow-x-auto">
    {[1, 2, 3, 4, 5, 6].map((i) => (
      <div
        key={i}
        className="h-10 w-24 bg-gray-800 rounded-full animate-pulse flex-shrink-0"
      ></div>
    ))}
  </div>
);

export const ArticleCarouselSkeleton = () => (
  <div className="w-full overflow-hidden mb-[30px] lg:mb-[45px]">
    <div className="flex items-start justify-between mb-1">
      <div className="w-48 h-6 bg-gray-800 rounded mb-4 animate-pulse"></div>
      <div className="w-24 h-6 bg-gray-800 rounded mb-4 animate-pulse"></div>
    </div>
    <div className="flex gap-4 overflow-x-auto">
      {[1, 2, 3, 4, 5].map((i) => (
        <div
          key={i}
          className="flex-shrink-0 w-[300px] h-[200px] bg-gray-800 rounded-lg animate-pulse"
        >
          <div className="w-3/4 h-4 bg-gray-700 rounded mt-4 mx-4"></div>
          <div className="w-1/2 h-4 bg-gray-700 rounded mt-2 mx-4"></div>
        </div>
      ))}
    </div>
  </div>
);

export const PlaylistCarouselSkeleton = () => (
  <div className="w-full overflow-hidden mb-[15px] lg:mb-[30px]">
    <div className="flex items-start justify-between mb-1">
      <div className="w-48 h-6 bg-gray-800 rounded mb-4 animate-pulse"></div>
      <div className="w-24 h-6 bg-gray-800 rounded mb-4 animate-pulse"></div>
    </div>
    <div className="flex gap-4 overflow-x-auto">
      {[1, 2, 3, 4].map((i) => (
        <div
          key={i}
          className="flex-shrink-0 w-[250px] h-[150px] bg-gray-800 rounded-lg animate-pulse"
        >
          <div className="w-3/4 h-4 bg-gray-700 rounded mt-4 mx-4"></div>
          <div className="w-1/2 h-4 bg-gray-700 rounded mt-2 mx-4"></div>
        </div>
      ))}
    </div>
  </div>
);

export const HomeSkeleton = () => {
  return (
    <div className="w-full bg-[#132435] text-white min-h-screen mx-auto py-4 overflow-x-hidden">
      <div className="p-[20px] lg:w-[1100px] bg-[#132435] md:w-[756px] mx-auto flex flex-col items-center justify-start text-white h-full">
        <h1 className="w-full text-2xl mb-4">Featured Summaries</h1>
        <FeaturedCarouselSkeleton />
        <CategoryButtonsSkeleton />
        <ArticleCarouselSkeleton /> {/* Latest Articles */}
        <PlaylistCarouselSkeleton /> {/* Collections */}
        <div className="w-full space-y-8">
          {[1, 2, 3].map((i) => (
            <ArticleCarouselSkeleton key={i} /> /* Top Articles by Category */
          ))}
        </div>
      </div>
    </div>
  );
};

export default function Loading() {
  return <HomeSkeleton />;
}
