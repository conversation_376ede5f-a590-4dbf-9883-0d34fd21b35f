"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";

export default function PressKit() {
  const [activeSection, setActiveSection] = useState("");

  const handleNavClick = (id: any) => {
    setActiveSection(id);
  };

  const navItems = [
    { id: "about", label: "About" },
    { id: "contact", label: "Contact" },
    { id: "team", label: "Team" },
    { id: "products", label: "Products" },
    { id: "links", label: "Links" },
    { id: "press-coverage", label: "Press Coverage" },
    { id: "branding", label: "Branding" },
  ];

  return (
    <div className="flex bg-white min-h-screen text-black">
      {/* Left navigation */}
      <nav className="w-1/4 bg-gray-100 p-6 shadow-md sticky top-0 h-screen overflow-y-auto">
        <ul className="space-y-4">
          {navItems.map((item) => (
            <li key={item.id}>
              <a
                href={`#${item.id}`}
                onClick={() => handleNavClick(item.id)}
                className={`text-blue-600 hover:text-blue-800 font-medium ${
                  activeSection === item.id
                    ? "bg-blue-100 px-2 py-1 rounded"
                    : ""
                }`}
              >
                {item.label}
              </a>
            </li>
          ))}
        </ul>
      </nav>

      {/* Main content */}
      <main className="w-3/4 p-8">
        <h1 className="text-3xl font-bold mb-8 text-gray-800">Press Kit</h1>

        <section id="about" className="mb-10">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">About</h2>
          <h3 className="text-xl font-semibold mb-2 text-gray-600">
            One-line blurb
          </h3>
          <p className="text-gray-600 leading-relaxed mb-4">
            Outread allows citizen scientists and enterprises to stay updated
            with the latest and greatest research papers.
          </p>
          <h3 className="text-xl font-semibold mb-2 text-gray-600">
            About Outread
          </h3>
          <p className="text-gray-600 leading-relaxed">
            {`
            
            Staying ahead means knowing the cutting-edge ideas that will shape
            tomorrow—but who has time to read 5M research papers published every
            year? Outread delivers cutting-edge insights from influential
            research papers, in minutes instead of hours. We curate the most
            impactful research across topics like psychology, AI, physics, and
            even Nobel-award-winning studies—and deliver them as 15-minute,
            simplified summaries. Looking for something specific? Meet DarwinAI,
            our intelligent Q&A tool that answers your questions by tapping into
            a vast database of 200M research papers. Whether you're exploring
            our library or diving into tailored queries, Outread helps you stay
            informed, effortlessly.
          `}
          </p>
        </section>

        <section id="contact" className="mb-10">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Contact</h2>
          <p className="text-gray-600 leading-relaxed">
            Janhvi Sirohi (CEO and Cofounder) - <EMAIL>
          </p>
        </section>

        <section id="team" className="mb-10">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Team</h2>
          <ul className="list-disc pl-5 text-gray-600">
            <li>Janhvi Sirohi (CEO & Cofounder)</li>
            <li>Anshika Singh (COO & Cofounder)</li>
            <li>Dhruv Sirohi (CTO & Cofounder)</li>
            <li>Alex Hu (Founding engineer)</li>
          </ul>
        </section>

        <section id="products" className="mb-10">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">
            Products
          </h2>
          <ul className="space-y-2">
            <li>
              <a
                href="https://outread.ai"
                className="text-blue-600 hover:text-blue-800"
              >
                Website - outread.ai
              </a>
            </li>
            <li>
              <a
                href="https://apps.apple.com/us/app/outread-ai-powered-insights/id6503236023?ign-itscg=30200&ign-itsct=apps_box_badge&mttnsubad=6503236023"
                className="text-blue-600 hover:text-blue-800"
              >
                App - Outread: AI-Powered Insights
              </a>
            </li>
            <li>
              <a
                href="https://outread.ai/darwinai-landing"
                className="text-blue-600 hover:text-blue-800"
              >
                DarwinAI
              </a>
            </li>
          </ul>
        </section>

        <section id="branding" className="mb-10">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">
            Branding
          </h2>
          <div className="grid grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-2">Logo 1</h3>
              <Image
                src="/presskit/LOGO1.png"
                alt="Outread Logo 1"
                width={200}
                height={200}
              />
              <a
                href="/presskit/LOGO1.png"
                download
                className="block mt-2 text-blue-600 hover:text-blue-800"
              >
                Download Logo 1
              </a>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Logo 2</h3>
              <Image
                src="/presskit/LOGO2.png"
                alt="Outread Logo 2"
                width={200}
                height={200}
              />
              <a
                href="/presskit/LOGO2.png"
                download
                className="block mt-2 text-blue-600 hover:text-blue-800"
              >
                Download Logo 2
              </a>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Logo 4</h3>
              <Image
                src="/presskit/LOGO4.png"
                alt="Outread Logo 4"
                width={200}
                height={200}
              />
              <a
                href="/presskit/LOGO4.png"
                download
                className="block mt-2 text-blue-600 hover:text-blue-800"
              >
                Download Logo 4
              </a>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Blue Logo</h3>
              <Image
                src="/presskit/OutreadLogo_Blue.png"
                alt="Outread Blue Logo"
                width={200}
                height={200}
              />
              <a
                href="/presskit/OutreadLogo_Blue.png"
                download
                className="block mt-2 text-blue-600 hover:text-blue-800"
              >
                Download Blue Logo
              </a>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Educational Pieces</h3>
              <Image
                src="/presskit/Educational Pieces.png"
                alt="Educational Pieces"
                width={300}
                height={200}
              />
              <a
                href="/presskit/Educational Pieces.png"
                download
                className="block mt-2 text-blue-600 hover:text-blue-800"
              >
                Download Educational Pieces
              </a>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">App Mockup</h3>
              <Image
                src="/presskit/Phones.png"
                alt="Outread App Mockup"
                width={300}
                height={200}
              />
              <a
                href="/presskit/Phones.png"
                download
                className="block mt-2 text-blue-600 hover:text-blue-800"
              >
                Download App Mockup
              </a>
            </div>
          </div>
        </section>

        <section id="links" className="mb-10">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">Links</h2>
          <ul className="space-y-2">
            <li>
              <a
                href="https://outread.ai"
                className="text-blue-600 hover:text-blue-800"
              >
                Website
              </a>
            </li>
            <li>
              <a
                href="https://apps.apple.com/us/app/outread-ai-powered-insights/id6503236023?ign-itscg=30200&ign-itsct=apps_box_badge&mttnsubad=6503236023"
                className="text-blue-600 hover:text-blue-800"
              >
                App
              </a>
            </li>
            <li>
              <a
                href="https://www.instagram.com/outread.official/"
                className="text-blue-600 hover:text-blue-800"
              >
                Instagram
              </a>
            </li>
            <li>
              <a
                href="https://www.linkedin.com/company/outread/"
                className="text-blue-600 hover:text-blue-800"
              >
                LinkedIn
              </a>
            </li>
            <li>
              <a
                href="https://www.tiktok.com/@outread.official"
                className="text-blue-600 hover:text-blue-800"
              >
                TikTok
              </a>
            </li>
            <li>
              <a
                href="https://discord.gg/Zr9BQzaC"
                className="text-blue-600 hover:text-blue-800"
              >
                Discord
              </a>
            </li>
          </ul>
        </section>

        <section id="press-coverage" className="mb-10">
          <h2 className="text-2xl font-semibold mb-4 text-gray-700">
            Press Coverage
          </h2>
          <ul className="space-y-2">
            <li>
              <a
                href="https://www.theaustralian.com.au/subscribe/news/1/?sourceCode=TAWEB_WRE170_a_GGL&dest=https%3A%2F%2Fwww.theaustralian.com.au%2Fbusiness%2Ftechnology%2Fbig-name-investors-back-ai-startup-outread%2Fnews-story%2F28bbcfa5eaee6725e5e41c26f77ca7be&memtype=anonymous&mode=premium&v21=HIGH-Segment-1-SCORE&V21spcbehaviour=append"
                className="text-blue-600 hover:text-blue-800"
              >
                The Australian - Big name investors back AI startup Outread
              </a>
            </li>
            <li>
              <a
                href="https://www.smartcompany.com.au/exclusive/neural-notes-outread-lands-750000-enhance-academic-research-ai/"
                className="text-blue-600 hover:text-blue-800"
              >
                SmartCompany - Neural Notes: Outread lands $750,000 to enhance
                academic research with AI
              </a>
            </li>
            <li>
              <a
                href="https://www.startupdaily.net/topic/funding/recime-shared-recipes-cooking-app-menu-ideas/"
                className="text-blue-600 hover:text-blue-800"
              >
                {`
                StartupDaily - Recime's shared recipes cooking app serves up
                $750K in seed funding
                `}
              </a>
            </li>
            <li>
              <a
                href="https://www.adelaidenow.com.au/subscribe/news/1/?sourceCode=AAWEB_WRE170_a_LIN&dest=https%3A%2F%2Fwww.adelaidenow.com.au%2Flifestyle%2Fsa-weekend%2Flisted-south-australias-hottest-startups-and-scaleups-of-2023-including-pearler-deep-liquid-and-myvenue%2Fnews-story%2F2591eeb1f58c2000f143ae7f448d9e57&memtype=anonymous&mode=premium&v21=HIGH-Segment-2-SCORE"
                className="text-blue-600 hover:text-blue-800"
              >
                {`
                The Advertiser - 25 Hottest Startups and Scaleups of 2023
                `}
              </a>
            </li>
          </ul>
        </section>
      </main>
    </div>
  );
}
