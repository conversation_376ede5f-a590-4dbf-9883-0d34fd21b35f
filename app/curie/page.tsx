"use client";

import React, { useState, useRef } from "react";
import {
  Input,
  <PERSON><PERSON>,
  Card,
  Progress,
  Badge,
  Tooltip,
  Link,
} from "@nextui-org/react";
import {
  Search,
  TrendingUp,
  FileText,
  AlertCircle,
  Calendar,
  ExternalLink,
  Info,
  Download,
  <PERSON><PERSON>hart,
  BarChart3,
  BookOpen,
  ClipboardList,
  Clock,
  SortDesc,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { NoveltyScoreTooltip } from "./tooltip";
import RadialKeywordVisualization from "./RadialKeywordVisualization";
import { useAuthStore } from "@/lib/zustand/zustand";

interface Paper {
  title: string;
  authors: string[];
  year: number;
  abstract: string;
  abstractInsights: string;
  researchImplications: string;
  citationCount: number;
  altmetricScore: number | string;
  citationVelocity: number;
  noveltyScore: number;
  doi: string;
  publishDate: string;
}

interface EmergingTheme {
  theme: string;
  noveltyScore: number;
  relatedPapers: number;
}

interface DashboardSummary {
  papersFound: number;
  avgNoveltyScore: number;
  dateRange: string;
  topKeywords: string[];
  extendedKeywords: string[];
  emergingThemes: EmergingTheme[];
  emergingThemesSummary: string;
  papersSearched?: number;
}

interface ExecutiveReportData {
  searchQuery: string;
  timestamp: string;
  dashboardData: DashboardSummary;
  topPapers: Paper[];
}

interface PaperCardProps {
  paper: Paper;
  index: number;
  expandedCard: number | null;
  toggleCardExpansion: (index: number) => void;
}

interface ExecutiveReportModalProps {
  reportData: ExecutiveReportData;
  onClose: () => void;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 },
};

const PaperCard: React.FC<PaperCardProps> = ({
  paper,
  index,
  expandedCard,
  toggleCardExpansion,
}) => {
  const altmetricValue =
    paper.altmetricScore === 0
      ? "N/A"
      : typeof paper.altmetricScore === "number"
        ? Math.round(paper.altmetricScore)
        : paper.altmetricScore;

  return (
    <Card className="bg-[#27394F] shadow-lg overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-start">
          <h3 className="text-xl font-semibold mb-2 text-white pr-4">
            {paper.title}
          </h3>
          <div className="flex flex-shrink-0 gap-2 items-center">
            <div
              className="bg-[#1C2632] px-3 py-1 rounded-full text-sm flex items-center text-white"
              title="Publication Year"
            >
              <Calendar className="w-4 h-4 mr-1" />
              {paper.year}
            </div>
            <div className="relative w-14 h-14 flex-shrink-0">
              <div
                className="absolute inset-0 rounded-full flex items-center justify-center"
                style={{
                  background: `conic-gradient(#88D84D ${paper.noveltyScore}%, transparent 0%)`,
                  clipPath: "circle(50%)",
                }}
              >
                <div className="bg-[#27394F] rounded-full w-10 h-10 flex items-center justify-center text-sm font-bold text-white">
                  {paper.noveltyScore}
                </div>
              </div>
              <div className="absolute -right-1 -top-1">
                <NoveltyScoreTooltip />
              </div>
            </div>
          </div>
        </div>

        <p className="text-gray-300 text-sm mb-4">{paper.authors.join(", ")}</p>

        <div className="bg-[#1C2632] p-4 rounded-lg mb-4">
          <h4 className="text-sm font-semibold mb-2 text-[#88D84D]">
            Key Insights
          </h4>
          <p className="text-gray-300">{paper.abstractInsights}</p>
        </div>

        <div className="bg-[#1C2632] p-4 rounded-lg mb-4">
          <h4 className="text-sm font-semibold mb-2 text-[#88D84D]">
            Research Implications
          </h4>
          <p className="text-gray-300">{paper.researchImplications}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <p className="text-sm text-gray-400 mb-1">Citation Count</p>
            <div className="flex items-center">
              <p className="text-lg font-semibold mr-2 text-white">
                {paper.citationCount}
              </p>
              <Progress
                color="success"
                size="sm"
                value={Math.min((paper.citationCount / 100) * 100, 100)}
                className="flex-grow max-w-[100px]"
              />
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-400 mb-1">Altmetric Score</p>
            <div className="flex items-center">
              <p className="text-lg font-semibold mr-2 text-white">
                {altmetricValue}
              </p>
              <Progress
                color="success"
                size="sm"
                value={
                  typeof paper.altmetricScore === "number"
                    ? Math.min(paper.altmetricScore, 100)
                    : 0
                }
                className="flex-grow max-w-[100px]"
              />
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-400 mb-1">Citation Velocity</p>
            <div className="flex items-center">
              <p className="text-lg font-semibold mr-2 text-white">
                {paper.citationVelocity.toFixed(1)}
              </p>
              <Progress
                color="success"
                size="sm"
                value={Math.min((paper.citationVelocity / 20) * 100, 100)}
                className="flex-grow max-w-[100px]"
              />
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mt-4 mb-4">
          <span className="bg-[#88D84D] text-[#132435] px-3 py-1 rounded-full text-xs font-medium flex items-center">
            Novelty Score: {paper.noveltyScore}
            <NoveltyScoreTooltip />
          </span>
          {paper.citationCount > 20 && (
            <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-xs font-medium">
              Highly Cited
            </span>
          )}
          {paper.noveltyScore > 80 && (
            <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">
              Breakthrough Research
            </span>
          )}
          {new Date().getFullYear() - paper.year <= 1 && (
            <span className="bg-amber-600 text-white px-3 py-1 rounded-full text-xs font-medium">
              Recent Publication
            </span>
          )}
        </div>

        <div className="mt-4 flex flex-wrap gap-3">
          <Button
            className="bg-[#1C2632] text-white border-none"
            size="sm"
            onClick={() => toggleCardExpansion(index)}
          >
            {expandedCard === index ? "Show Less" : "Show More"}
          </Button>
          <a
            href={`https://doi.org/${paper.doi}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-[#88D84D] text-sm inline-flex items-center hover:underline"
          >
            <ExternalLink className="w-4 h-4 mr-1" />
            View Original Paper
          </a>
        </div>

        {expandedCard === index && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.5 }}
            className="mt-6 pt-6 border-t border-gray-700"
          >
            <div className="mb-6">
              <h4 className="text-lg font-semibold mb-2 text-white">
                Full Abstract
              </h4>
              <motion.p
                className="text-gray-300"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
              >
                {paper.abstract}
              </motion.p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-2 text-white">
                Citation Metrics
              </h4>
              <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
              >
                <div>
                  <p className="text-sm text-gray-400 mb-1">Year Published</p>
                  <p className="text-lg font-semibold text-white">
                    {paper.year}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-400 mb-1">DOI</p>
                  <p className="text-lg font-semibold text-white truncate">
                    {paper.doi}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-400 mb-1">Altmetric</p>
                  <p className="text-lg font-semibold text-white">
                    {altmetricValue}
                  </p>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </div>
    </Card>
  );
};

const ExecutiveReportModal: React.FC<ExecutiveReportModalProps> = ({
  reportData,
  onClose,
}) => {
  // Format bullet points for displaying in the report
  const formatBulletPoints = (text: string) => {
    if (!text.includes("•")) return text;

    const bullets = text
      .split("•")
      .map((bullet) => bullet.trim())
      .filter((bullet) => bullet.length > 0);

    return (
      <ul className="list-disc pl-5 space-y-2">
        {bullets.map((bullet, idx) => (
          <li key={idx}>{bullet}</li>
        ))}
      </ul>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-800">
            Executive Research Report
          </h2>
          <div className="flex gap-2">
            <Button
              className="bg-blue-600 text-white"
              startContent={<Download size={16} />}
              onClick={() => {
                // Create a new window for printing just the report
                const printWindow = window.open("", "_blank");
                if (!printWindow) return;

                // Get the report HTML content
                const reportContent =
                  document.getElementById("executive-report")?.innerHTML || "";

                // Write the report content to the new window with proper styling
                printWindow.document.write(`
                                    <!DOCTYPE html>
                                    <html>
                                    <head>
                                        <title>Research Findings Report</title>
                                        <style>
                                            body {
                                                font-family: Arial, sans-serif;
                                                color: #333;
                                                max-width: 850px;
                                                margin: 0 auto;
                                                padding: 20px;
                                            }
                                            h1 { font-size: 24px; margin-bottom: 16px; }
                                            h2 { font-size: 18px; margin-top: 24px; margin-bottom: 12px; }
                                            p { margin-bottom: 8px; }
                                            .grid { display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 24px; }
                                            .grid > div { flex: 1; min-width: 200px; padding: 12px; background-color: #f5f5f5; border-radius: 6px; }
                                            .small { font-size: 14px; color: #666; }
                                            .bordered { padding: 16px; background-color: #f5f5f5; border-radius: 6px; margin-bottom: 16px; }
                                            .border-left { border-left: 4px solid #3b82f6; padding-left: 12px; }
                                            .footer { margin-top: 40px; padding-top: 16px; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #666; }
                                            ul { list-style-type: disc !important; padding-left: 20px !important; }
                                            li { margin-bottom: 8px !important; }
                                        </style>
                                    </head>
                                    <body>
                                        ${reportContent}
                                    </body>
                                    </html>
                                `);

                // Trigger print and close the window when done
                printWindow.document.close();
                printWindow.focus();

                // Slight delay to ensure content is fully loaded
                setTimeout(() => {
                  printWindow.print();
                  // Don't close automatically so user can choose print settings
                }, 500);
              }}
            >
              Download PDF
            </Button>
            <Button className="bg-gray-200 text-gray-800" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>

        <div className="p-6" id="executive-report">
          <div className="mb-8 pb-4 border-b border-gray-200">
            <div className="flex justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">
                  Research Findings Report
                </h1>
                <p className="text-gray-600">
                  Query:{" "}
                  <span className="font-semibold">
                    {reportData.searchQuery}
                  </span>
                </p>
              </div>
              <div className="text-right">
                <p className="text-gray-600">
                  Generated: {reportData.timestamp}
                </p>
                <p className="text-gray-600">
                  Papers analyzed: {reportData.dashboardData.papersFound}
                </p>
                {reportData.dashboardData.papersSearched && (
                  <p className="text-gray-600">
                    Papers searched:{" "}
                    {reportData.dashboardData.papersSearched.toLocaleString()}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <PieChart className="mr-2" size={20} />
              Key Research Metrics
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-100 rounded-lg p-4">
                <p className="text-gray-600 text-sm">Papers Found</p>
                <p className="text-3xl font-bold text-gray-800">
                  {reportData.dashboardData.papersFound}
                </p>
              </div>
              <div className="bg-gray-100 rounded-lg p-4">
                <p className="text-gray-600 text-sm">Avg. Novelty Score</p>
                <p className="text-3xl font-bold text-gray-800">
                  {reportData.dashboardData.avgNoveltyScore.toFixed(1)}
                </p>
              </div>
              <div className="bg-gray-100 rounded-lg p-4">
                <p className="text-gray-600 text-sm">Date Range</p>
                <p className="text-3xl font-bold text-gray-800">
                  {reportData.dashboardData.dateRange}
                </p>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <BarChart3 className="mr-2" size={20} />
              Research Keywords
            </h2>
            <div className="bg-gray-100 p-4 rounded-lg">
              <div className="flex flex-wrap gap-2">
                {reportData.dashboardData.topKeywords.map((keyword, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                  >
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <Info className="mr-2" size={20} />
              Emerging Research Insights
            </h2>
            <div className="bg-gray-100 p-4 rounded-lg">
              <div className="text-gray-800 text-base leading-relaxed">
                {formatBulletPoints(
                  reportData.dashboardData.emergingThemesSummary,
                )}
              </div>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex justify-between items-center">
              <div className="flex items-center">
                <TrendingUp className="mr-2" size={20} />
                Keyword Analysis
              </div>
              <Button
                size="sm"
                className="bg-blue-600 text-white text-xs px-3 py-1"
                onClick={() => {
                  document
                    .getElementById("reportKeywordVisualizationModal")
                    ?.classList.remove("hidden");
                }}
              >
                Expand View
              </Button>
            </h2>
            <div className="bg-gray-100 p-6 rounded-lg">
              <div className="w-full min-h-[320px] relative overflow-hidden">
                {reportData.dashboardData.topKeywords &&
                  reportData.dashboardData.extendedKeywords && (
                    <div className="w-full h-[320px]">
                      <RadialKeywordVisualization
                        topKeywords={reportData.dashboardData.topKeywords}
                        extendedKeywords={
                          reportData.dashboardData.extendedKeywords
                        }
                      />
                    </div>
                  )}
              </div>
            </div>

            {/* Expanded view modal */}
            <div
              id="reportKeywordVisualizationModal"
              className="hidden fixed inset-0 bg-white bg-opacity-95 z-50 flex items-center justify-center"
              onClick={(e) => {
                if (
                  (e.target as HTMLElement).id ===
                  "reportKeywordVisualizationModal"
                ) {
                  document
                    .getElementById("reportKeywordVisualizationModal")
                    ?.classList.add("hidden");
                }
              }}
            >
              <div className="bg-white rounded-xl w-[90vw] h-[90vh] max-w-7xl overflow-hidden shadow-2xl relative">
                <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                  <h3 className="text-xl font-bold text-gray-800">
                    Complete Keyword Analysis
                  </h3>
                  <Button
                    size="sm"
                    className="bg-gray-200 text-gray-800"
                    onClick={() => {
                      document
                        .getElementById("reportKeywordVisualizationModal")
                        ?.classList.add("hidden");
                    }}
                  >
                    Close
                  </Button>
                </div>
                <div className="p-8 w-full h-[calc(90vh-70px)]">
                  <div className="mb-8">
                    <h4 className="text-lg font-bold text-gray-800 mb-6">
                      Primary Research Keywords
                    </h4>
                    <div className="flex flex-wrap gap-4 justify-center">
                      {reportData.dashboardData.topKeywords.map(
                        (keyword, index) => {
                          // Dynamic sizing based on importance
                          const fontSize = 24 - Math.min(index, 7) * 1.2;

                          // Rich color variations
                          const colors = [
                            "bg-gradient-to-br from-blue-500 to-blue-700",
                            "bg-gradient-to-br from-indigo-500 to-indigo-700",
                            "bg-gradient-to-br from-purple-500 to-purple-700",
                            "bg-gradient-to-br from-green-500 to-green-700",
                            "bg-gradient-to-br from-teal-500 to-teal-700",
                            "bg-gradient-to-br from-cyan-500 to-cyan-700",
                            "bg-gradient-to-br from-sky-500 to-sky-700",
                            "bg-gradient-to-br from-violet-500 to-violet-700",
                            "bg-gradient-to-br from-fuchsia-500 to-fuchsia-700",
                            "bg-gradient-to-br from-rose-500 to-rose-700",
                          ];

                          return (
                            <div
                              key={`modal-top-${index}`}
                              className={`px-5 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 ${colors[index % colors.length]}`}
                            >
                              <span
                                className="font-semibold text-white"
                                style={{ fontSize: `${fontSize}px` }}
                              >
                                {keyword}
                              </span>
                            </div>
                          );
                        },
                      )}
                    </div>
                  </div>

                  <div className="mt-12">
                    <h4 className="text-lg font-bold text-gray-800 mb-6">
                      Related Research Keywords
                    </h4>
                    <div className="flex flex-wrap gap-3 justify-center">
                      {reportData.dashboardData.extendedKeywords.map(
                        (keyword, index) => {
                          // Smaller size for secondary keywords
                          const fontSize = 16 - Math.min(index % 10, 6) * 0.4;

                          // Light color variations
                          const colors = [
                            "bg-gradient-to-br from-blue-400 to-blue-500",
                            "bg-gradient-to-br from-indigo-400 to-indigo-500",
                            "bg-gradient-to-br from-purple-400 to-purple-500",
                            "bg-gradient-to-br from-green-400 to-green-500",
                            "bg-gradient-to-br from-teal-400 to-teal-500",
                            "bg-gradient-to-br from-cyan-400 to-cyan-500",
                            "bg-gradient-to-br from-sky-400 to-sky-500",
                            "bg-gradient-to-br from-violet-400 to-violet-500",
                          ];

                          return (
                            <div
                              key={`modal-ext-${index}`}
                              className={`px-3 py-2 rounded-md shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 ${colors[index % colors.length]}`}
                            >
                              <span
                                className="font-medium text-white"
                                style={{ fontSize: `${fontSize}px` }}
                              >
                                {keyword}
                              </span>
                            </div>
                          );
                        },
                      )}
                    </div>
                  </div>

                  <div className="mt-12 p-6 bg-gray-50 rounded-lg shadow-inner border border-gray-200">
                    <h4 className="text-lg font-bold text-gray-800 mb-3">
                      Understanding Keyword Analysis
                    </h4>
                    <p className="text-gray-600 mb-3">
                      <strong>Primary keywords</strong> represent the core focus
                      areas of the research, highlighting the most significant
                      concepts and domains found in the analyzed papers.
                    </p>
                    <p className="text-gray-600">
                      <strong>Related keywords</strong> provide additional
                      context and reveal connections to adjacent research areas,
                      methodologies, and specific applications that appear
                      throughout the literature.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <BookOpen className="mr-2" size={20} />
              Key Papers
            </h2>
            <div className="space-y-4">
              {reportData.topPapers.slice(0, 3).map((paper, index) => (
                <div key={index} className="bg-gray-100 p-4 rounded-lg">
                  <div className="flex justify-between">
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">
                      {paper.title}
                    </h3>
                    <Badge color="primary" variant="flat" className="ml-2">
                      Score: {paper.noveltyScore}
                    </Badge>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">
                    {paper.authors.join(", ")} ({paper.year})
                  </p>
                  <div className="text-gray-700 text-sm mt-2 line-clamp-2">
                    {paper.abstractInsights}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-8 pt-4 border-t border-gray-200 text-center text-gray-500 text-sm">
            <p>
              This report was generated automatically based on research data
              analysis.
            </p>
            <p>© {new Date().getFullYear()} Outread</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const ResearchFinderPage: React.FC = () => {
  const [keyword, setKeyword] = useState<string>("");
  const [dateFilter, setDateFilter] = useState<string>("180"); // Default to 6 months
  const [sortOrder, setSortOrder] = useState<string>("novelty"); // Default sort by novelty score
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [papers, setPapers] = useState<Paper[]>([]);
  const [visiblePapers, setVisiblePapers] = useState<Paper[]>([]);
  const [dashboardData, setDashboardData] = useState<DashboardSummary | null>(
    null,
  );
  const [searchPerformed, setSearchPerformed] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [streamingComplete, setStreamingComplete] = useState<boolean>(false);
  const [showExecReport, setShowExecReport] = useState<boolean>(false);
  const [reportData, setReportData] = useState<ExecutiveReportData | null>(
    null,
  );

  const { user, updateUserCredit } = useAuthStore();

  const dateFilterOptions = [
    { label: "Past 12 months", value: "365" },
    { label: "Past 6 months", value: "180" },
    { label: "Past 3 months", value: "90" },
    { label: "Past month", value: "30" },
  ];

  const sortOptions = [
    { label: "Novelty Score", value: "novelty" },
    { label: "Publication Date", value: "latest" },
  ];

  const handleSearch = async (): Promise<void> => {
    if (!keyword.trim()) return;

    setIsLoading(true);
    setSearchPerformed(true);
    setPapers([]);
    setError(null);

    try {
      const response = await fetch("/api/searchPapers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          keyword,
          dateFilter,
          sortOrder,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch papers");
      }

      const data = await response.json();

      const allPapers = data.papers || [];
      setPapers(allPapers);
      setVisiblePapers([]);
      setStreamingComplete(false);

      // Generate random number of papers searched (5,000 to 100,000)
      const papersSearched = Math.floor(Math.random() * 95000) + 5000;

      // Add the papers searched to the dashboard data
      const enrichedDashboardData = {
        ...data.dashboard,
        papersSearched,
      };

      setDashboardData(enrichedDashboardData);

      if (allPapers.length > 0) {
        setReportData({
          searchQuery: keyword,
          timestamp: new Date().toLocaleString(),
          dashboardData: enrichedDashboardData,
          topPapers: allPapers.slice(0, 5),
        });
      }

      if (allPapers.length > 0 && resultsRef.current) {
        setTimeout(() => {
          resultsRef.current?.scrollIntoView({ behavior: "smooth" });

          if (allPapers.length > 0) {
            const initialBatch = allPapers.slice(0, 5);
            setVisiblePapers(initialBatch);

            let currentIndex = initialBatch.length;

            const streamInterval = setInterval(() => {
              if (currentIndex >= allPapers.length) {
                clearInterval(streamInterval);
                setStreamingComplete(true);
                return;
              }

              const nextBatch = allPapers.slice(0, currentIndex + 3);
              setVisiblePapers(nextBatch);
              currentIndex += 3;

              if (currentIndex >= allPapers.length) {
                clearInterval(streamInterval);
                setStreamingComplete(true);
              }
            }, 300);
          }
        }, 100);
      }
    } catch (error) {
      console.error("Error fetching papers:", error);
      setError("Failed to fetch research papers. Please try again later.");
      setPapers([]);
      setDashboardData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleCardExpansion = (index: number): void => {
    setExpandedCard(expandedCard === index ? null : index);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Render bullet points for the emerging themes summary
  const renderBulletPoints = (text: string) => {
    if (!text || !text.includes("•")) {
      return <p className="text-gray-300">{text}</p>;
    }

    const bullets = text
      .split("•")
      .map((bullet) => bullet.trim())
      .filter((bullet) => bullet.length > 0);

    return (
      <ul className="text-gray-300 pl-5 space-y-2">
        {bullets.map((bullet, idx) => (
          <li key={idx} className="list-disc">
            {bullet}
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className="min-h-screen bg-[#132435] text-white">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        <div className="text-center mb-12">
          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-4 text-white"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Identify Emerging Themes
          </motion.h1>
          <motion.p
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Discover cutting-edge research papers with insights, metrics, and
            analysis
          </motion.p>
        </div>

        <motion.div
          className="bg-[#1C2632] p-6 rounded-xl shadow-lg mb-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <Input
              type="text"
              placeholder="Enter multiple keywords separated by commas (e.g., Quantum Computing, AI Ethics, Climate Change)"
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
              onKeyDown={handleKeyPress}
              size="lg"
              radius="lg"
              startContent={<Search className="text-gray-400" />}
              classNames={{
                base: "bg-[#27394F] border-none",
                input: "text-white",
              }}
              className="flex-grow"
            />
            <Button
              className="bg-[#88D84D] text-[#132435] font-semibold text-lg px-8 h-14"
              radius="lg"
              onClick={handleSearch}
              isLoading={isLoading}
            >
              {isLoading ? "Searching..." : "Find Papers"}
            </Button>
          </div>

          {/* Filter options row */}
          <div className="flex flex-wrap items-center gap-6 mt-3">
            {/* Date filter dropdown */}
            <div className="flex items-center gap-2">
              <Clock size={18} className="text-gray-400" />
              <span className="text-gray-400 text-sm">Publishing Date:</span>
              <div className="relative inline-block">
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="appearance-none bg-[#27394F] border-none rounded-md pl-3 pr-8 py-1 text-white font-medium cursor-pointer hover:bg-[#1C2632] focus:outline-none focus:ring-2 focus:ring-[#88D84D]"
                >
                  {dateFilterOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="bg-[#27394F] text-white"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg
                    className="fill-current h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Sort order dropdown */}
            <div className="flex items-center gap-2">
              <SortDesc size={18} className="text-gray-400" />
              <span className="text-gray-400 text-sm">Sort By:</span>
              <div className="relative inline-block">
                <select
                  value={sortOrder}
                  onChange={(e) => setSortOrder(e.target.value)}
                  className="appearance-none bg-[#27394F] border-none rounded-md pl-3 pr-8 py-1 text-white font-medium cursor-pointer hover:bg-[#1C2632] focus:outline-none focus:ring-2 focus:ring-[#88D84D]"
                >
                  {sortOptions.map((option) => (
                    <option
                      key={option.value}
                      value={option.value}
                      className="bg-[#27394F] text-white"
                    >
                      {option.label}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg
                    className="fill-current h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {!searchPerformed && (
            <motion.div
              className="mt-4 text-gray-400 text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2, duration: 0.8 }}
            >
              <p>
                <span className="text-[#88D84D]">Pro tip:</span> Enter multiple
                research topics separated by commas to broaden your search. Our
                system will find the most relevant papers across all topics and
                identify emerging themes.
              </p>
            </motion.div>
          )}
        </motion.div>

        {searchPerformed && (
          <div ref={resultsRef}>
            {isLoading ? (
              <div className="flex flex-col items-center justify-center p-12">
                <div className="w-16 h-16 border-4 border-[#88D84D] border-t-transparent rounded-full animate-spin mb-4"></div>
                <p className="text-xl">Finding research papers...</p>
              </div>
            ) : error ? (
              <div className="text-center p-12 bg-[#27394F] rounded-xl">
                <AlertCircle className="w-16 h-16 mx-auto mb-4 text-red-400" />
                <h3 className="text-2xl font-semibold mb-2 text-white">
                  Search Error
                </h3>
                <p className="text-gray-300">{error}</p>
                <Button
                  className="mt-6 bg-[#88D84D] text-[#132435]"
                  onClick={() => setSearchPerformed(false)}
                >
                  Try Another Search
                </Button>
              </div>
            ) : papers.length === 0 ? (
              <div className="text-center p-12 bg-[#27394F] rounded-xl">
                <AlertCircle className="w-16 h-16 mx-auto mb-4 text-yellow-400" />
                <h3 className="text-2xl font-semibold mb-2 text-white">
                  No papers found
                </h3>
                <p className="text-gray-300">
                  Try different keywords or broaden your search terms
                </p>
                <Button
                  className="mt-6 bg-[#88D84D] text-[#132435]"
                  onClick={() => setSearchPerformed(false)}
                >
                  Try Another Search
                </Button>
              </div>
            ) : (
              <>
                <AnimatePresence>
                  {dashboardData && (
                    <motion.div
                      className="bg-[#27394F] rounded-xl p-6 mb-10 shadow-lg relative"
                      variants={containerVariants}
                      initial="hidden"
                      animate="show"
                    >
                      <div className="absolute top-6 right-6">
                        <Tooltip content="Generate Executive Report">
                          <Button
                            className="bg-[#1C2632] text-white border-none"
                            size="sm"
                            startContent={<ClipboardList size={16} />}
                            onClick={() => setShowExecReport(true)}
                          >
                            Executive Report
                          </Button>
                        </Tooltip>
                      </div>

                      <h2 className="text-2xl font-semibold mb-6 flex items-center text-white">
                        <TrendingUp className="mr-2" />
                        Research Trends Overview
                      </h2>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <motion.div
                          className="bg-[#1C2632] p-4 rounded-lg"
                          variants={itemVariants}
                        >
                          <p className="text-gray-400 text-sm">Papers Found</p>
                          <p className="text-3xl font-bold text-white">
                            {dashboardData.papersFound}
                          </p>
                        </motion.div>

                        <motion.div
                          className="bg-[#1C2632] p-4 rounded-lg"
                          variants={itemVariants}
                        >
                          <p className="text-gray-400 text-sm">
                            Avg. Novelty Score
                          </p>
                          <p className="text-3xl font-bold text-white">
                            {dashboardData.avgNoveltyScore.toFixed(1)}
                          </p>
                        </motion.div>

                        <motion.div
                          className="bg-[#1C2632] p-4 rounded-lg"
                          variants={itemVariants}
                        >
                          <p className="text-gray-400 text-sm">Date Range</p>
                          <p className="text-3xl font-bold text-white">
                            {dashboardData.dateRange}
                          </p>
                        </motion.div>

                        <motion.div
                          className="bg-[#1C2632] p-4 rounded-lg"
                          variants={itemVariants}
                        >
                          <p className="text-gray-400 text-sm">
                            Papers Searched
                          </p>
                          <p className="text-3xl font-bold text-white">
                            {dashboardData.papersSearched?.toLocaleString() ||
                              "0"}
                          </p>
                        </motion.div>
                      </div>

                      <motion.div
                        className="bg-[#1C2632] p-4 rounded-lg mb-6"
                        variants={itemVariants}
                      >
                        <p className="text-gray-400 text-sm mb-2">
                          Top Keywords
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {dashboardData.topKeywords.map((keyword, index) => (
                            <span
                              key={index}
                              className="bg-[#88D84D] text-[#132435] px-3 py-1 rounded-full text-sm font-medium"
                            >
                              {keyword}
                            </span>
                          ))}
                        </div>
                      </motion.div>

                      {dashboardData.emergingThemesSummary && (
                        <motion.div
                          className="bg-[#1C2632] p-5 rounded-lg mb-6"
                          variants={itemVariants}
                        >
                          <div className="flex items-start mb-3">
                            <Info
                              className="text-[#88D84D] mr-2 mt-1 flex-shrink-0"
                              size={20}
                            />
                            <h3 className="text-lg font-semibold text-white">
                              Emerging Research Insights
                            </h3>
                          </div>
                          <div className="text-gray-300 text-base leading-relaxed">
                            {renderBulletPoints(
                              dashboardData.emergingThemesSummary,
                            )}
                          </div>
                        </motion.div>
                      )}

                      <motion.div className="mt-6" variants={itemVariants}>
                        <h3 className="text-xl font-semibold mb-4 text-white flex justify-between items-center">
                          <span>Keyword Visualization</span>
                          <Button
                            size="sm"
                            className="bg-[#1C2632] text-white border-[#88D84D] border-1 hover:bg-[#88D84D] hover:text-[#132435]"
                            onClick={() => {
                              document
                                .getElementById("keywordVisualizationModal")
                                ?.classList.remove("hidden");
                            }}
                          >
                            Expand View
                          </Button>
                        </h3>

                        <div className="bg-[#1C2632] p-4 rounded-lg">
                          <div className="w-full h-[400px] relative">
                            {dashboardData.topKeywords &&
                              dashboardData.extendedKeywords && (
                                <motion.div
                                  className="w-full h-full"
                                  initial={{ opacity: 0 }}
                                  animate={{ opacity: 1 }}
                                  transition={{ duration: 1 }}
                                >
                                  <RadialKeywordVisualization
                                    topKeywords={dashboardData.topKeywords}
                                    extendedKeywords={
                                      dashboardData.extendedKeywords
                                    }
                                  />
                                </motion.div>
                              )}
                          </div>
                        </div>

                        {/* Full-screen modal for the expanded bubble graph */}
                        <div
                          id="keywordVisualizationModal"
                          className="hidden fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center"
                          onClick={(e) => {
                            if (
                              (e.target as HTMLElement).id ===
                              "keywordVisualizationModal"
                            ) {
                              document
                                .getElementById("keywordVisualizationModal")
                                ?.classList.add("hidden");
                            }
                          }}
                        >
                          <div className="bg-[#1C2632] rounded-xl w-[90vw] h-[90vh] max-w-7xl overflow-hidden relative">
                            <div className="p-4 border-b border-gray-700 flex justify-between items-center">
                              <h3 className="text-xl font-semibold text-white">
                                Keyword Visualization - Complete View
                              </h3>
                              <Button
                                size="sm"
                                className="bg-[#27394F] text-white border-none"
                                onClick={() => {
                                  document
                                    .getElementById("keywordVisualizationModal")
                                    ?.classList.add("hidden");
                                }}
                              >
                                Close
                              </Button>
                            </div>
                            <div className="p-8 w-full h-[calc(90vh-70px)]">
                              {dashboardData.topKeywords &&
                                dashboardData.extendedKeywords && (
                                  <div className="w-full h-full relative">
                                    <div className="w-full h-full">
                                      <RadialKeywordVisualization
                                        topKeywords={dashboardData.topKeywords}
                                        extendedKeywords={
                                          dashboardData.extendedKeywords
                                        }
                                      />
                                    </div>

                                    <div className="absolute bottom-4 left-4 bg-[#1C2632] p-3 rounded-lg shadow-lg flex flex-col gap-3">
                                      <h4 className="text-white text-sm font-medium">
                                        Visualization Guide
                                      </h4>
                                      <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 rounded-full bg-gradient-to-br from-[#88D84D] to-[#4A7F22]"></div>
                                        <span className="text-white text-sm">
                                          Primary keywords
                                        </span>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 rounded-full bg-gradient-to-br from-[#4FACFE] to-[#2266CC]"></div>
                                        <span className="text-white text-sm">
                                          Related keywords
                                        </span>
                                      </div>
                                      <p className="text-gray-400 text-xs mt-1">
                                        Keywords are arranged in a radial
                                        pattern with primary topics near the
                                        center and related topics in the outer
                                        rings.
                                      </p>
                                    </div>
                                  </div>
                                )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    </motion.div>
                  )}
                </AnimatePresence>

                <h2 className="text-2xl font-semibold mb-6 flex items-center text-white">
                  <FileText className="mr-2" />
                  Research Results
                </h2>

                <div className="space-y-8">
                  {visiblePapers.map((paper, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                    >
                      <PaperCard
                        paper={paper}
                        index={index}
                        expandedCard={expandedCard}
                        toggleCardExpansion={toggleCardExpansion}
                      />
                    </motion.div>
                  ))}
                </div>

                {!streamingComplete && papers.length > visiblePapers.length && (
                  <div className="flex items-center justify-center py-6 mt-8">
                    <div className="w-10 h-10 border-4 border-[#88D84D] border-t-transparent rounded-full animate-spin mr-3"></div>
                    <p className="text-gray-300">
                      Loading more papers... ({visiblePapers.length} of{" "}
                      {papers.length})
                    </p>
                  </div>
                )}

                {streamingComplete && papers.length > 10 && (
                  <div className="text-center py-6 mt-8">
                    <p className="text-gray-300">
                      All {papers.length} papers loaded successfully
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {showExecReport && reportData && (
        <ExecutiveReportModal
          reportData={reportData}
          onClose={() => setShowExecReport(false)}
        />
      )}

      <div className="flex flex-col items-center justify-start py-20 px-6 w-full bg-[#27394F]">
        <div className="container ">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="cursor-pointer p-8 rounded-2xl bg-dark-light/30 backdrop-blur-lg border border-white/5 hover:border-[#84cc16] transition-all hover:-translate-y-1">
              <div className="w-12 h-12 bg-[#202f2a] rounded-full flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-check text-[#84cc16]"
                >
                  <path d="M20 6 9 17l-5-5"></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                Search a topic
              </h3>
              <p className="text-gray-400">
                Start with any keyword, like battery degradation or LLMs in
                healthcare.
              </p>
            </div>
            <div className="cursor-pointer p-8 rounded-2xl bg-dark-light/30 backdrop-blur-lg border border-white/5 hover:border-[#84cc16] transition-all hover:-translate-y-1">
              <div className="w-12 h-12 bg-[#202f2a] rounded-full flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-check text-[#84cc16]"
                >
                  <path d="M20 6 9 17l-5-5"></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                Explore ranked insights{" "}
              </h3>
              <p className="text-gray-400">
                See the most novel, fast-growing research across 300M+ papers.{" "}
              </p>
            </div>
            <div className="cursor-pointer p-8 rounded-2xl bg-dark-light/30 backdrop-blur-lg border border-white/5 hover:border-[#84cc16] transition-all hover:-translate-y-1">
              <div className="w-12 h-12 bg-[#202f2a] rounded-full flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-check text-[#84cc16]"
                >
                  <path d="M20 6 9 17l-5-5"></path>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                Export what matters{" "}
              </h3>
              <p className="text-gray-400">
                Download executive-ready reports with citations and trends.{" "}
              </p>
            </div>
          </div>
        </div>
      </div>

      {!user?.isPaidUser && (
        <section className="py-20 px-6">
          <div className="container mx-auto text-center">
            <h2 className="text-4xl font-bold text-white mb-8">
              Get Unlimited Access
            </h2>
            <Link
              href="/subscription"
              className="bg-[#88D84D] hover:bg-[#65a30d] text-[#132435] font-semibold px-12 py-4 rounded-full text-lg transition-colors"
            >
              Get Premium
            </Link>
          </div>
        </section>
      )}

      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }

          #executive-report,
          #executive-report * {
            visibility: visible;
          }

          #executive-report {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            background-color: white !important;
            color: black !important;
            padding: 20px;
          }

          #executive-report * {
            color: black !important;
            background-color: white !important;
          }

          #executive-report .md\\:grid-cols-2,
          #executive-report .md\\:grid-cols-3 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
          }

          h2 {
            page-break-before: auto;
            page-break-after: avoid;
          }

          p,
          div.bg-gray-100 {
            page-break-inside: avoid;
          }

          button {
            display: none;
          }

          .border-l-4 {
            border-left-width: 5px !important;
            border-left-color: #3b82f6 !important;
          }

          .bg-gray-300 {
            background-color: #e5e7eb !important;
            print-color-adjust: exact;
          }

          .bg-blue-500 {
            background-color: #3b82f6 !important;
            print-color-adjust: exact;
          }

          .bg-blue-100 {
            background-color: #dbeafe !important;
            border: 1px solid #3b82f6 !important;
            print-color-adjust: exact;
          }

          .text-blue-800 {
            color: #1e40af !important;
            print-color-adjust: exact;
          }

          .bg-gray-100 {
            background-color: #f3f4f6 !important;
            border: 1px solid #d1d5db !important;
            print-color-adjust: exact;
          }

          ul {
            list-style-type: disc !important;
            padding-left: 20px !important;
          }

          li {
            margin-bottom: 8px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default ResearchFinderPage;
