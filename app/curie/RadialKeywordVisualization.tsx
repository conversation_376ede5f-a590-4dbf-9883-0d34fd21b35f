import React, { useEffect, useRef } from 'react';

interface RadialKeywordVisualizationProps {
    topKeywords?: string[];
    extendedKeywords?: string[];
}

const RadialKeywordVisualization: React.FC<RadialKeywordVisualizationProps> = ({
    topKeywords = [],
    extendedKeywords = []
}) => {
    // Use provided keywords or default to sample data if none provided
    const displayTopKeywords = topKeywords.length > 0 ? topKeywords : [
        "Quantum Computing", "Machine Learning", "Climate Change",
        "Renewable Energy", "Neural Networks", "Blockchain",
        "Genetic Algorithms", "Artificial Intelligence"
    ];

    const displayExtendedKeywords = extendedKeywords.length > 0 ? extendedKeywords : [
        "Quantum Entanglement", "Deep Learning", "Global Warming",
        "Solar Power", "Convolutional Networks", "Smart Contracts",
        "Evolutionary Computing", "Natural Language Processing",
        "Computer Vision", "Renewable Resources", "Data Mining",
        "Carbon Capture", "Distributed Ledger", "Reinforcement Learning",
        "Neural Interfaces", "Cloud Computing", "Robotics", "Big Data"
    ];

    const canvasRef = useRef < HTMLCanvasElement > (null);

    useEffect(() => {
        if (!canvasRef.current) return;

        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        const width = canvas.width;
        const height = canvas.height;
        const centerX = width / 2;
        const centerY = height / 2;

        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        // Draw background
        ctx.fillStyle = '#1C2632';
        ctx.fillRect(0, 0, width, height);

        // Draw connecting lines (subtle background effect)
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
        ctx.lineWidth = 1;

        // Draw radial lines
        const numLines = 24;
        for (let i = 0; i < numLines; i++) {
            const angle = (i / numLines) * Math.PI * 2;
            const outerRadius = Math.min(width, height) * 0.45;

            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.lineTo(
                centerX + Math.cos(angle) * outerRadius,
                centerY + Math.sin(angle) * outerRadius
            );
            ctx.stroke();
        }

        // Draw concentric circles
        const numCircles = 4;
        for (let i = 1; i <= numCircles; i++) {
            const radius = (i / numCircles) * Math.min(width, height) * 0.45;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.stroke();
        }

        // Draw primary keywords
        displayTopKeywords.forEach((keyword, index) => {
            const totalKeywords = displayTopKeywords.length;
            const angle = (index / totalKeywords) * Math.PI * 2;

            // Position keywords in inner circle
            const radius = Math.min(width, height) * 0.2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            // Size based on importance
            const fontSize = 18 - Math.min(index, 5);

            // Draw node
            ctx.beginPath();
            const nodeSize = 50 - index * 2;

            // Create gradient for node
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, nodeSize);
            gradient.addColorStop(0, '#88D84D');
            gradient.addColorStop(1, '#4A7F22');

            ctx.fillStyle = gradient;
            ctx.arc(x, y, nodeSize, 0, Math.PI * 2);
            ctx.fill();

            // Draw text
            ctx.fillStyle = 'white';
            ctx.font = `${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // Wrap text if needed
            const maxLineWidth = nodeSize * 1.5;
            const words = keyword.split(' ');
            let line = '';
            let lines = [];

            words.forEach(word => {
                const testLine = line + (line ? ' ' : '') + word;
                const metrics = ctx.measureText(testLine);
                if (metrics.width > maxLineWidth && line !== '') {
                    lines.push(line);
                    line = word;
                } else {
                    line = testLine;
                }
            });
            lines.push(line);

            // Draw each line of text
            lines.forEach((textLine, lineIndex) => {
                const lineOffset = (lineIndex - (lines.length - 1) / 2) * fontSize;
                ctx.fillText(textLine, x, y + lineOffset);
            });
        });

        // Draw extended keywords
        displayExtendedKeywords.forEach((keyword, index) => {
            const totalKeywords = displayExtendedKeywords.length;

            // Distribute in outer rings
            const ringIndex = Math.floor(index / (totalKeywords / 2)) + 1; // Start from ring 1
            const keywordsInRing = Math.ceil(totalKeywords / 2);
            const angleInRing = index % keywordsInRing;

            const angle = (angleInRing / keywordsInRing) * Math.PI * 2 + (ringIndex * 0.2); // Offset each ring
            const radius = Math.min(width, height) * (0.3 + ringIndex * 0.1);

            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            // Smaller size for extended keywords
            const fontSize = 14 - Math.min(ringIndex, 2);
            const nodeSize = 30 - ringIndex * 4;

            // Create gradient for extended keywords (blues)
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, nodeSize);
            gradient.addColorStop(0, '#4FACFE');
            gradient.addColorStop(1, '#2266CC');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, nodeSize, 0, Math.PI * 2);
            ctx.fill();

            // Draw connecting line to nearest primary keyword
            const primaryIndex = index % displayTopKeywords.length;
            const primaryAngle = (primaryIndex / displayTopKeywords.length) * Math.PI * 2;
            const primaryRadius = Math.min(width, height) * 0.2;
            const primaryX = centerX + Math.cos(primaryAngle) * primaryRadius;
            const primaryY = centerY + Math.sin(primaryAngle) * primaryRadius;

            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.beginPath();
            ctx.moveTo(primaryX, primaryY);
            ctx.lineTo(x, y);
            ctx.stroke();

            // Draw text
            ctx.fillStyle = 'white';
            ctx.font = `${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // Wrap text if needed
            const maxLineWidth = nodeSize * 1.8;
            const words = keyword.split(' ');
            let line = '';
            let lines = [];

            words.forEach(word => {
                const testLine = line + (line ? ' ' : '') + word;
                const metrics = ctx.measureText(testLine);
                if (metrics.width > maxLineWidth && line !== '') {
                    lines.push(line);
                    line = word;
                } else {
                    line = testLine;
                }
            });
            lines.push(line);

            // Draw each line of text
            lines.forEach((textLine, lineIndex) => {
                const lineOffset = (lineIndex - (lines.length - 1) / 2) * fontSize;
                ctx.fillText(textLine, x, y + lineOffset);
            });
        });

    }, [displayTopKeywords, displayExtendedKeywords]);

    return (
        <div className="flex items-center justify-center w-full h-full bg-[#1C2632] rounded-lg p-4">
            <canvas
                ref={canvasRef}
                width={800}
                height={600}
                className="w-full h-full max-h-[400px] object-contain"
            />
            <div className="absolute bottom-2 right-2 flex items-center gap-2">
                <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-gradient-to-br from-[#88D84D] to-[#4A7F22]"></div>
                    <span className="text-xs text-white opacity-70">Primary keywords</span>
                </div>
                <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-gradient-to-br from-[#4FACFE] to-[#2266CC]"></div>
                    <span className="text-xs text-white opacity-70">Related keywords</span>
                </div>
            </div>
        </div>
    );
};

export default RadialKeywordVisualization;