import React from "react";
import { Tooltip } from "@nextui-org/react";
import { Info } from "lucide-react";

export const NoveltyScoreTooltip: React.FC = () => {
  return (
    <Tooltip
      content={
        <div className="p-2 max-w-xs">
          <p className="font-semibold mb-1">Novelty Score Calculation</p>
          <p className="text-sm">
            This score is calculated using multiple metrics:
          </p>
          <ul className="text-xs list-disc pl-4 mt-1">
            <li>Altmetric </li>
            <li>Citation Velocity </li>
            <li>H10 index</li>
            <li>i10 index</li>
            <li>Other bibliometric indicators</li>
          </ul>
          <p className="text-xs mt-1">
            Higher scores indicate potentially groundbreaking research. We're also conducting topic analysis to identify breakthrough topics.
          </p>
        </div>
      }
      placement="top"
      classNames={{
        content: "bg-[#27394F] text-white border border-[#88D84D]"
      }}
    >
      <Info className="w-4 h-4 text-[#88D84D] cursor-help ml-1" />
    </Tooltip>
  );
};