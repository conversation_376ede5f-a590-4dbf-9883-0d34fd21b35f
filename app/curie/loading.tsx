import React from "react";
import { Card, Skeleton } from "@nextui-org/react";

export default function Loading() {
  return (
    <div className="min-h-screen bg-[#132435] text-white">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        <div className="text-center mb-12">
          <Skeleton className="h-10 w-1/2 rounded-lg mx-auto mb-4" />
          <Skeleton className="h-6 w-3/4 rounded-lg mx-auto" />
        </div>

        <div className="bg-[#1C2632] p-6 rounded-xl shadow-lg mb-10">
          <div className="flex flex-col md:flex-row gap-4">
            <Skeleton className="h-14 w-full rounded-lg" />
            <Skeleton className="h-14 w-48 rounded-lg" />
            <Skeleton className="h-14 w-64 rounded-lg" />
            <Skeleton className="h-14 w-36 rounded-lg" />
          </div>

          {/* B2B Feature: Additional filters */}
          <div className="flex gap-3 mt-3">
            <Skeleton className="h-5 w-32 rounded-lg" />
            <Skeleton className="h-5 w-32 rounded-lg" />
            <Skeleton className="h-5 w-32 rounded-lg" />
          </div>
        </div>

        <div className="space-y-8">
          <div className="bg-[#27394F] rounded-xl p-6 mb-10 shadow-lg">
            {/* B2B Feature: Dashboard tabs */}
            <div className="flex border-b border-gray-700 mb-6">
              <Skeleton className="h-8 w-32 rounded-lg mr-4" />
              <Skeleton className="h-8 w-32 rounded-lg mr-4" />
              <Skeleton className="h-8 w-32 rounded-lg mr-4" />
              <Skeleton className="h-8 w-32 rounded-lg" />
            </div>

            <Skeleton className="h-8 w-64 rounded-lg mb-6" />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-[#1C2632] p-4 rounded-lg">
                  <Skeleton className="h-4 w-24 rounded-lg mb-2" />
                  <Skeleton className="h-8 w-16 rounded-lg" />
                </div>
              ))}
            </div>

            <div className="bg-[#1C2632] p-4 rounded-lg mb-6">
              <Skeleton className="h-4 w-24 rounded-lg mb-2" />
              <div className="flex flex-wrap gap-2">
                {[1, 2, 3, 4].map((i) => (
                  <Skeleton key={i} className="h-6 w-20 rounded-full" />
                ))}
              </div>
            </div>

            <div className="mt-6">
              <Skeleton className="h-6 w-48 rounded-lg mb-4" />

              <div className="bg-[#1C2632] p-4 rounded-lg mb-4">
                <Skeleton className="h-5 w-64 rounded-lg mb-3" />
                <Skeleton className="h-4 w-full rounded-lg mb-2" />
                <Skeleton className="h-4 w-full rounded-lg mb-2" />
                <Skeleton className="h-4 w-3/4 rounded-lg" />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[1, 2].map((i) => (
                  <div
                    key={i}
                    className="bg-[#1C2632] p-4 rounded-lg border-l-4 border-[#88D84D]"
                  >
                    <Skeleton className="h-5 w-40 rounded-lg mb-3" />
                    <div className="flex justify-between mb-2">
                      <Skeleton className="h-4 w-24 rounded-lg" />
                      <Skeleton className="h-4 w-8 rounded-lg" />
                    </div>
                    <Skeleton className="h-2 w-full rounded-full mb-3" />
                    <Skeleton className="h-4 w-32 rounded-lg" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* B2B Feature: Paper selection actions bar */}
          <div className="bg-[#1C2632] p-4 rounded-lg mb-6 flex gap-4">
            <Skeleton className="h-6 w-32 rounded-lg" />
            <Skeleton className="h-6 w-32 rounded-lg" />
            <Skeleton className="h-6 w-32 rounded-lg" />
            <Skeleton className="h-6 w-32 rounded-lg" />
          </div>

          <Skeleton className="h-8 w-48 rounded-lg mb-6" />

          {[1, 2, 3].map((i) => (
            <Card key={i} className="bg-[#27394F] shadow-lg overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-start">
                  <div className="flex">
                    {/* B2B Feature: Selection checkbox */}
                    <Skeleton className="h-5 w-5 rounded mr-3 mt-1" />
                    <Skeleton className="h-6 w-3/4 rounded-lg mb-2" />
                  </div>
                  <div className="flex flex-shrink-0 gap-2 items-center">
                    <Skeleton className="h-6 w-20 rounded-full" />
                    <Skeleton className="h-6 w-20 rounded-full" />
                    <Skeleton className="w-14 h-14 rounded-full" />
                  </div>
                </div>

                <Skeleton className="h-4 w-1/2 rounded-lg mb-4" />

                <div className="bg-[#1C2632] p-4 rounded-lg mb-4">
                  <Skeleton className="h-4 w-32 rounded-lg mb-2" />
                  <Skeleton className="h-4 w-full rounded-lg mb-1" />
                  <Skeleton className="h-4 w-full rounded-lg mb-1" />
                </div>

                <div className="bg-[#1C2632] p-4 rounded-lg mb-6">
                  <Skeleton className="h-4 w-32 rounded-lg mb-2" />
                  <Skeleton className="h-4 w-full rounded-lg mb-1" />
                  <Skeleton className="h-4 w-3/4 rounded-lg" />
                </div>

                {/* B2B Feature: Industry relevance section */}
                <div className="bg-[#1C2632] p-4 rounded-lg mb-6">
                  <Skeleton className="h-4 w-32 rounded-lg mb-3" />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {[1, 2, 3, 4].map((j) => (
                      <div key={j}>
                        <div className="flex justify-between mb-1">
                          <Skeleton className="h-3 w-20 rounded-lg" />
                          <Skeleton className="h-3 w-8 rounded-lg" />
                        </div>
                        <Skeleton className="h-2 w-full rounded-full" />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  {[1, 2, 3].map((j) => (
                    <div key={j}>
                      <Skeleton className="h-4 w-24 rounded-lg mb-1" />
                      <div className="flex items-center">
                        <Skeleton className="h-6 w-8 rounded-lg mr-2" />
                        <Skeleton className="h-2 w-24 rounded-lg" />
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex flex-wrap gap-2 mt-4 mb-4">
                  {[1, 2, 3, 4].map((j) => (
                    <Skeleton key={j} className="h-6 w-24 rounded-full" />
                  ))}
                </div>

                <div className="mt-4 flex justify-between">
                  <Skeleton className="h-8 w-24 rounded-lg" />
                  <div className="flex gap-2">
                    <Skeleton className="h-8 w-20 rounded-lg" />
                    <Skeleton className="h-8 w-20 rounded-lg" />
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
