"use server";

import { createClient } from "@/lib/supabase/server";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";

export async function toggleBookmark(articleId: string) {
  const supabase = createClient();
  const {
    data: { user: supabaseUser },
  } = await supabase.auth.getUser();

  if (!supabaseUser) throw new Error("Unauthorized");

  const user = await prisma.user.findFirst({
    where: {
      email: supabaseUser!.email as string,
    },
  });

  if (!user) throw new Error("User not found");

  try {
    const existingBookmark = await prisma.bookmarkedArticle.findFirst({
      where: {
        AND: [{ userId: user.id }, { articleId: articleId }],
      },
    });

    let isBookmarked: boolean;

    if (existingBookmark) {
      await prisma.bookmarkedArticle.delete({
        where: { id: existingBookmark.id },
      });
      isBookmarked = false;
    } else {
      await prisma.bookmarkedArticle.create({
        data: {
          userId: user.id,
          articleId: articleId,
        },
      });
      isBookmarked = true;
    }

    revalidatePath("/");
    return { success: true, isBookmarked };
  } catch (error) {
    console.error("Error toggling bookmark:", error);
    return { error: "Failed to toggle bookmark" };
  }
}
