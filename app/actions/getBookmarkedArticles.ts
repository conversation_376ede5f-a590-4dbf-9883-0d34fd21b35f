import { getUser } from "@/lib/cache/getUser";
import prisma from "@/lib/prisma";

export async function getBookmarkedArticles(
  page: number = 1,
  pageSize: number = 25,
) {
  const user = await getUser();
  if (!user) {
    throw new Error("User not found");
  }

  const skip = (page - 1) * pageSize;

  const [articles, totalCount] = await Promise.all([
    prisma.article.findMany({
      where: {
        bookmarkedBy: {
          some: {
            userId: user.id,
          },
        },
      },
      select: {
        id: true,
        title: true,
        slug: true,
        audioId: true,
        imageId: true,
        estimatedReadingTime: true,
        categories: {
          select: {
            name: true,
          },
        },
        bookmarkedBy: {
          select: {
            userId: true,
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
      skip,
      take: pageSize,
    }),
    prisma.article.count({
      where: {
        bookmarkedBy: {
          some: {
            userId: user.id,
          },
        },
      },
    }),
  ]);

  const mappedArticles = await Promise.all(
    articles.map(async (article) => {
      const image = article.imageId
        ? await prisma.image.findUnique({
            where: { id: article.imageId },
            select: { src: true, alt: true },
          })
        : null;

      return {
        id: article.id,
        title: article.title,
        estimatedReadingTime: article.estimatedReadingTime,
        slug: article.slug,
        imageUrl: image?.src || "",
        audioId: article.audioId,
        imageAlt: image?.alt || article.title,
        category: article.categories[0]?.name || "Uncategorized",
        initialIsBookmarked: true, // All articles here are bookmarked
      };
    }),
  );

  return {
    articles: mappedArticles,
    totalPages: Math.ceil(totalCount / pageSize),
    currentPage: page,
  };
}
