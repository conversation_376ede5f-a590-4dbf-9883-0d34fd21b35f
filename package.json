{"name": "outread-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.670.0", "@aws-sdk/client-sns": "^3.716.0", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^5.1.1", "@nextui-org/react": "^2.4.6", "@nextui-org/theme": "^2.2.9", "@pinecone-database/pinecone": "^3.0.3", "@prisma/client": "^6.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.25", "@react-email/render": "^1.0.1", "@stripe/react-stripe-js": "^2.7.3", "@stripe/stripe-js": "^4.2.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.4.1", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-query": "^5.59.16", "@types/react-google-recaptcha": "^2.1.9", "@types/ua-parser-js": "^0.7.39", "@vercel/node": "^3.2.23", "@xenova/transformers": "^2.17.2", "axios": "^1.8.2", "branch-sdk": "^2.86.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "csv-parse": "^5.6.0", "csv-parser": "^3.1.0", "d3": "^7.8.5", "dotenv": "^16.4.5", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.6.0", "express-rate-limit": "^7.5.0", "framer-motion": "^11.3.12", "lucide-react": "^0.445.0", "mailersend": "^2.3.0", "next": "14.2.5", "next-themes": "^0.3.0", "nodemailer": "^6.9.15", "openai": "^4.28.0", "papaparse": "^5.5.3", "pdfjs-dist": "^4.9.155", "pinecone": "^0.1.0", "react": "^18", "react-apexcharts": "^1.7.0", "react-day-picker": "^9.8.0", "react-dom": "^18", "react-google-button": "^0.8.0", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.3", "recharts": "^3.1.0", "remark-gfm": "^4.0.0", "shadcn": "^2.8.0", "sonner": "^2.0.6", "stripe": "^16.7.0", "swiper": "^11.1.15", "tailwind-merge": "^3.3.1", "tsx": "^4.16.5", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.25.76", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@types/branch-sdk": "^2.53.7", "@types/d3": "^7.4.3", "@types/facebook-pixel": "^0.0.31", "@types/node": "^20", "@types/nodemailer": "^6.4.16", "@types/papaparse": "^5.3.16", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^4.6.2", "postcss": "^8", "prettier": "^3.4.2", "prisma": "^5.17.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}