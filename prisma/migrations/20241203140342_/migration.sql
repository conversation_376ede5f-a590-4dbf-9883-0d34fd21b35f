/*
  Warnings:

  - You are about to drop the column `playlistId` on the `Article` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "Article" DROP CONSTRAINT "Article_playlistId_fkey";

-- AlterTable
ALTER TABLE "Article" DROP COLUMN "playlistId";

-- CreateTable
CREATE TABLE "_ArticleToPlaylist" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_ArticleToPlaylist_AB_unique" ON "_ArticleToPlaylist"("A", "B");

-- CreateIndex
CREATE INDEX "_ArticleToPlaylist_B_index" ON "_ArticleToPlaylist"("B");

-- AddForeignKey
ALTER TABLE "_ArticleToPlaylist" ADD CONSTRAINT "_ArticleToPlaylist_A_fkey" FOREIGN KEY ("A") REFERENCES "Article"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ArticleToPlaylist" ADD CONSTRAINT "_ArticleToPlaylist_B_fkey" FOREIGN KEY ("B") REFERENCES "Playlist"("id") ON DELETE CASCADE ON UPDATE CASCADE;
