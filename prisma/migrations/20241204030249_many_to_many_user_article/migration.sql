/*
  Warnings:

  - You are about to drop the column `assignedAt` on the `BookmarkedArticle` table. All the data in the column will be lost.
  - You are about to drop the column `assignedBy` on the `BookmarkedArticle` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,articleId]` on the table `BookmarkedArticle` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "BookmarkedArticle" DROP CONSTRAINT "BookmarkedArticle_articleId_fkey";

-- DropIndex
DROP INDEX "BookmarkedArticle_articleId_key";

-- DropIndex
DROP INDEX "BookmarkedArticle_userId_key";

-- AlterTable
ALTER TABLE "BookmarkedArticle" DROP COLUMN "assignedAt",
DROP COLUMN "assignedBy",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- CreateIndex
CREATE UNIQUE INDEX "BookmarkedArticle_userId_articleId_key" ON "BookmarkedArticle"("userId", "articleId");

-- AddForeignKey
ALTER TABLE "BookmarkedArticle" ADD CONSTRAINT "BookmarkedArticle_articleId_fkey" FOREIGN KEY ("articleId") REFERENCES "Article"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
