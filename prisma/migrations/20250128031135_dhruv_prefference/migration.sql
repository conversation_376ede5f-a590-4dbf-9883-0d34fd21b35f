/*
  Warnings:

  - Made the column `group2UnlockDate` on table `Preference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `group3UnlockDate` on table `Preference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `group4UnlockDate` on table `Preference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `group5UnlockDate` on table `Preference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `group6UnlockDate` on table `Preference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `group7UnlockDate` on table `Preference` required. This step will fail if there are existing NULL values in that column.
  - Changed the type of `onboardingAnswers` on the `Preference` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "Preference" ALTER COLUMN "preference" DROP DEFAULT,
ALTER COLUMN "group2UnlockDate" SET NOT NULL,
ALTER COLUMN "group2UnlockDate" SET DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "group3UnlockDate" SET NOT NULL,
ALTER COLUMN "group3UnlockDate" SET DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "group4UnlockDate" SET NOT NULL,
ALTER COLUMN "group4UnlockDate" SET DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "group5UnlockDate" SET NOT NULL,
ALTER COLUMN "group5UnlockDate" SET DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "group6UnlockDate" SET NOT NULL,
ALTER COLUMN "group6UnlockDate" SET DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "group7UnlockDate" SET NOT NULL,
ALTER COLUMN "group7UnlockDate" SET DEFAULT CURRENT_TIMESTAMP,
DROP COLUMN "onboardingAnswers",
ADD COLUMN     "onboardingAnswers" JSONB NOT NULL,
ALTER COLUMN "onboardingCategories" DROP DEFAULT;
