/*
  Warnings:

  - Made the column `createdAt` on table `Article` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `Article` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `BookmarkedArticle` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `Comment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `Comment` required. This step will fail if there are existing NULL values in that column.
  - Made the column `assignedAt` on table `FavouriteArticle` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `Image` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `Image` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `NewsletterSubscriber` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `NewsletterSubscriber` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `Paper` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `Paper` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `Playlist` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `Playlist` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `Purchases` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `Search` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `Search` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `TrendAnalysisResponse` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `TrendAnalysisResponse` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `User` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `User` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Article" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "BookmarkedArticle" ALTER COLUMN "createdAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "Comment" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "FavouriteArticle" ALTER COLUMN "assignedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "Image" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "NewsletterSubscriber" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "Paper" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "Playlist" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "Purchases" ALTER COLUMN "createdAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "Search" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "TrendAnalysisResponse" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "User" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;
