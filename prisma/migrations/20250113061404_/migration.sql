/*
  Warnings:

  - You are about to drop the column `count` on the `Search` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId]` on the table `Search` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `userId` to the `Search` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Search" DROP COLUMN "count",
ADD COLUMN     "userId" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Search_userId_key" ON "Search"("userId");

-- AddForeignKey
ALTER TABLE "Search" ADD CONSTRAINT "Search_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
