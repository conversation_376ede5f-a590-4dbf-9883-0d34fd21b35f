/*
  Warnings:

  - Added the required column `group2UnlockDate` to the `Preference` table without a default value. This is not possible if the table is not empty.
  - Added the required column `group3UnlockDate` to the `Preference` table without a default value. This is not possible if the table is not empty.
  - Added the required column `group4UnlockDate` to the `Preference` table without a default value. This is not possible if the table is not empty.
  - Added the required column `group5UnlockDate` to the `Preference` table without a default value. This is not possible if the table is not empty.
  - Added the required column `group6UnlockDate` to the `Preference` table without a default value. This is not possible if the table is not empty.
  - Added the required column `group7UnlockDate` to the `Preference` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Preference" ADD COLUMN     "group1" TEXT[],
ADD COLUMN     "group1UnlockDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "group2" TEXT[],
ADD COLUMN     "group2UnlockDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "group3" TEXT[],
ADD COLUMN     "group3UnlockDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "group4" TEXT[],
ADD COLUMN     "group4UnlockDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "group5" TEXT[],
ADD COLUMN     "group5UnlockDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "group6" TEXT[],
ADD COLUMN     "group6UnlockDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "group7" TEXT[],
ADD COLUMN     "group7UnlockDate" TIMESTAMP(3) NOT NULL;
