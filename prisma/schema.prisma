generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = "postgresql://postgres.cnducrozrpvuuztqkxhz:<EMAIL>:6543/postgres?pgbouncer=true"
  directUrl = "postgresql://postgres.cnducrozrpvuuztqkxhz:<EMAIL>:5432/postgres"
}

model User {
  id                     String                  @id @unique @default(cuid())
  username               String?                 @unique
  supabaseUserId         String                  @unique
  profilePictureSrc      String?
  email                  String                  @unique
  role                   Role                    @default(USER)
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @default(now()) @updatedAt
  credit                 Int                     @default(5)
  bookmarkedArticles     BookmarkedArticle[]
  Comments               Comment?
  FavouriteArticle       FavouriteArticle?
  trendAnalysisResponses TrendAnalysisResponse[]
  purchases              Purchases[]
  preference             Preference?
  isWebsite              Boolean                 @default(false)
}

model Purchases {
  id                    String   @id @unique @default(cuid())
  userId                String?  @map("userId")
  userEmail             String?
  price                 Float?
  currency              String?
  receipt               String?
  expirationDate        String?
  createdAt             DateTime @default(now())
  transactionId         String
  originalTransactionId String?
  productId             String?
  user                  User?    @relation(fields: [userId], references: [id])
}

model NewsletterSubscriber {
  id        String   @id @default(cuid())
  email     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model Image {
  id        String    @id @default(cuid())
  src       String
  name      String
  alt       String
  articles  Article?
  playlists Playlist?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model Catergory {
  id       String    @id @default(cuid())
  name     String    @unique
  ranking  Int       @default(0)
  articles Article[] @relation("ArticleToCatergory")
}

model Comment {
  id        String   @id @default(cuid())
  articleId String   @unique
  userId    String   @unique
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Article   Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)
  User      User     @relation(fields: [userId], references: [id])
}

model Article {
  id                   String              @id @default(cuid())
  doi                  String              @unique
  originalPaperTitle   String              @default("")
  authorName           String              @default("")
  slug                 String              @unique
  title                String
  subtitle             String
  altMetricScore       Int
  simpleSummary        Json
  defaultSummary       Json
  oneCardSummary       Json
  estimatedReadingTime Int                 @default(8)
  favouritedCount      Int                 @default(0)
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @default(now()) @updatedAt
  generatedWith        ArticleType         @default(TEXT_CONTENT)
  audioId              String?             @unique
  articleImage         Image?              @relation(fields: [imageId], references: [id])
  imageId              String?             @unique
  bookmarkedBy         BookmarkedArticle[]
  views                Int                 @default(0)
  comments             Comment?
  FavouriteArticle     FavouriteArticle?
  categories           Catergory[]         @relation("ArticleToCatergory")
  playlists            Playlist[]          @relation("ArticleToPlaylist")
  paperId              Int?                @unique
  paper                Paper?              @relation(fields: [paperId], references: [id], onDelete: Cascade)
  preferences          Preference[]
  publishDate          DateTime?
}

model BookmarkedArticle {
  id        String   @id @default(cuid())
  userId    String
  articleId String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])
  article   Article  @relation(fields: [articleId], references: [id])

  @@unique([userId, articleId])
}

model FavouriteArticle {
  id         String   @id @default(cuid())
  userId     String   @unique
  articleId  String   @unique
  assignedAt DateTime @default(now())
  assignedBy String
  Article    Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)
  User       User     @relation(fields: [userId], references: [id])
}

model Playlist {
  id          String    @id @default(cuid())
  views       Int       @default(0)
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  image       Image?    @relation(fields: [imageId], references: [id])
  imageId     String?   @unique
  articles    Article[] @relation("ArticleToPlaylist")
}

model TrendAnalysisResponse {
  id         String   @id @default(cuid())
  userId     String
  query      String
  complexity String
  response   Json
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id])
}

model Paper {
  id              Int      @id @default(autoincrement())
  title           String
  doi             String   @unique
  publishDate     String
  authorName      String
  journal         String
  ISSN            String
  altmetricsScore Int
  citations       Int
  pdfUrl          String?
  abstractTree    Json?
  metrics         Json?
  Article         Article?
  vectorId        String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model Preference {
  id                   String    @id @default(cuid())
  preference           String?   @default("")
  createdAt            DateTime  @default(now())
  updatedAt            DateTime? @updatedAt
  user                 User      @relation(fields: [userId], references: [id])
  userId               String    @unique
  group1               String[]
  group1UnlockDate     DateTime  @default(now())
  group2               String[]
  group2UnlockDate     DateTime  @default(now())
  group3               String[]
  group3UnlockDate     DateTime  @default(now())
  group4               String[]
  group4UnlockDate     DateTime  @default(now())
  group5               String[]
  group5UnlockDate     DateTime  @default(now())
  group6               String[]
  group6UnlockDate     DateTime  @default(now())
  group7               String[]
  group7UnlockDate     DateTime  @default(now())
  onboardingCategories String
  onboardingAnswers    Json
  articles             Article[]
}

enum Role {
  USER
  PAID_USER
  ADMIN
}

enum ArticleType {
  RAW_PDF
  COMPRESSED_PDF
  TEXT_CONTENT
}

model Search {
  id        String   @id @default(cuid())
  userId    String?
  query     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
