import { Image as ArticleImage } from "@prisma/client";

export interface HomePageArticle {
  id: string;
  slug: string;
  title: string;
  audioId: string | null;
  subtitle: string;
  estimatedReadingTime: number;
  articleImage: ArticleImage;
  categories: { name: string }[];
  altMetricScore: number;
}

export interface HomepageArticleWithBookmark extends HomePageArticle {
  isBookmarked: boolean;
}
