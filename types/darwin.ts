// Darwin Research Chat - Core Types

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  citations?: Citation[];
  isStreaming?: boolean;
}

export interface Citation {
  id: string;
  title: string;
  authors: string[];
  url: string;
  journal?: string;
  year?: number;
  impactFactor?: number;
  noveltyScore?: number;
  isOpenAccess?: boolean;
  abstract?: string;
  doi?: string;
  relevanceScore?: number;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  rating?: number;
  mode: 'deep' | 'summary';
}

export interface FilterOptions {
  yearRange?: [number, number];
  minImpactFactor?: number;
  minNoveltyScore?: number;
  openAccessOnly?: boolean;
  journals?: string[];
}

export interface UserPreferences {
  defaultMode: 'deep' | 'summary';
  autoSuggestFollowUps: boolean;
  showCitationPreviews: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface StreamingResponse {
  content: string;
  citations: Citation[];
  isComplete: boolean;
}

export interface AnalyticsData {
  totalSessions: number;
  averageResponseTime: number;
  citationCounts: Record<string, number>;
  ratingDistribution: Record<number, number>;
  filterUsage: Record<string, number>;
}

export interface ChatRequest {
  message: string;
  mode: 'deep' | 'summary';
  sessionId?: string;
  context?: ChatMessage[];
  conversationHistory?: { role: string; content: string }[];
  existingCitations?: Citation[];
}

export interface FeedbackRequest {
  messageId: string;
  rating: number;
  sessionId?: string;
} 