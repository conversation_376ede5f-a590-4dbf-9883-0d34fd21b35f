import { createClient } from "@/lib//supabase/server";
import prisma from "@/lib//prisma";

export const getUser = async () => {
  const supabase = createClient();
  const user = (await supabase.auth.getUser()).data.user;

  if (!user) {
    return null;
  }

  const prismaUser = await prisma.user.findUnique({
    where: {
      email: user?.email,
    },
  });

  if (!prismaUser) {
    throw new Error("User not found in superbase");
  }

  return prismaUser;
};
