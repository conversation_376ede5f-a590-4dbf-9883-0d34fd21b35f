import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { unstable_cache } from "next/cache";

export const getAllArticles = async (
  page: number = 1,
  pageSize: number = 25,
  userId: string,
  startYear: number | null,
  startMonth: number | null,
  endYear: number | null,
  endMonth: number | null,
) => {
  const getCachedArticles = unstable_cache(
    async () => {
      const skip = (page - 1) * pageSize;

      const dateFilter: {
        publishDate: { gte?: Date; lte?: Date; not: null };
      } = {
        publishDate: { not: null },
      };
      if (startYear && startMonth) {
        dateFilter.publishDate = {
          ...dateFilter.publishDate,
          gte: new Date(startYear, startMonth - 1, 1),
        };
      }
      if (endYear && endMonth) {
        dateFilter.publishDate = {
          ...dateFilter.publishDate,
          lte: new Date(endYear, endMonth, 0),
        };
      }

      const [articles, totalCount] = await Promise.all([
        prisma.article.findMany({
          where: dateFilter,
          select: {
            id: true,
            title: true,
            slug: true,
            imageId: true,
            audioId: true,
            estimatedReadingTime: true,
            publishDate: true,
            categories: {
              select: {
                name: true,
              },
            },
            articleImage: {
              select: {
                src: true,
                alt: true,
              },
            },
            bookmarkedBy: {
              where: {
                userId: userId,
              },
              select: {
                userId: true,
              },
              take: 1,
            },
          },
          orderBy: {
            publishDate: "desc",
          },
          skip,
          take: pageSize,
        }),
        prisma.article.count({ where: dateFilter }),
      ]);

      const mappedArticles = articles.map((article) => ({
        id: article.id,
        title: article.title,
        slug: article.slug,
        estimatedReadingTime: article.estimatedReadingTime,
        audioId: article.audioId || null,
        imageUrl: article.articleImage?.src || "",
        imageAlt: article.articleImage?.alt || article.title,
        category: article.categories[0]?.name || "Uncategorized",
        initialIsBookmarked: article.bookmarkedBy.length > 0,
        publishDate: article.publishDate,
      }));

      return {
        articles: mappedArticles,
        totalPages: Math.ceil(totalCount / pageSize),
        currentPage: page,
      };
    },
    ["all-articles", page.toString(), pageSize.toString(), userId],
    {
      revalidate: 300, // Cache for 5 min
      tags: ["articles"], // Tag for cache invalidation
    },
  );

  const result = await getCachedArticles();

  // Revalidate the cache
  revalidatePath("/all-articles");

  return result;
};
