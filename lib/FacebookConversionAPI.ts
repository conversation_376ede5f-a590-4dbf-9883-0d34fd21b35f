const FB_ACCESS_TOKEN =
  "EAAG7kExauk0BO5IQAMoGbh9K6wZB8lJn7BkMxvq132ZBEGvaEmxGFv6qhNaVXudQZC1F8Ja19W9f1ryIQuhmiY0hetjMxI0Dq9w7RedqCkdU0KEJswxT7tbvHZBYXDh74fAwZAuNjgajMUi9khV9Tvd7ahvHoZARGQBGGzCRJmTCeURAVHoETz3M65bva7IxZB3";
const FB_PIXEL_ID = "1788816715206755"; // You need to add your Facebook Pixel ID here
import crypto from "crypto";

const hashData = (data: string): string => {
  if (!data) return "";
  return crypto
    .createHash("sha256")
    .update(data.toLowerCase().trim())
    .digest("hex");
};

const sendFBEvent = async (
  eventName: string,
  eventData: any,
  userData: any,
  eventSource: string,
) => {
  const url = `https://graph.facebook.com/v13.0/${FB_PIXEL_ID}/events?access_token=${FB_ACCESS_TOKEN}`;

  const data = {
    data: [
      {
        event_name: eventName,
        event_time: Math.floor(Date.now() / 1000),
        action_source: "website",
        event_source_url: eventSource,
        user_data: {
          // Hash all user data
          em: eventData.email ? hashData(eventData.email) : "",
          client_user_agent: userData.userAgent,
          client_ip_address: userData.userIp,
        },
        custom_data: eventData,
      },
    ],
  };
  const result = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (result.status !== 200) {
    console.log("Failed to send Facebook event");
    throw result;
  } else {
    console.log(result);
    console.log("Facebook event sent successfully");
  }
};

export const handleViewContent = (
  pageLink: string,
  userData: any,
  eventSource: any,
) => {
  sendFBEvent("ViewContent", { pageLink: pageLink }, userData, eventSource);
};

// Search event
export const handleSearch = (
  searchQuery: string,
  userData: any,
  eventSource: any,
) => {
  sendFBEvent("Search", { searchQuery }, userData, eventSource);
};

// Purchase event
export const handlePurchase = (
  value: number,
  currency: string,
  userData: any,
  eventSource: any,
) => {
  sendFBEvent("Purchase", { value, currency }, userData, eventSource);
};

// InitiateCheckout event
export const handleInitiateCheckout = async (
  plan: string,
  email: string,
  userData: any,
  eventSource: any,
) => {
  sendFBEvent(
    "InitiateCheckout",
    { plan: plan, email: email },
    userData,
    eventSource,
  );
};
