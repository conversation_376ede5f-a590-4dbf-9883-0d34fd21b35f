import { unstable_cache } from "next/cache";
import { getFeaturedArticles } from "../app/catalogue/actions/getFeaturedArticle";
import { getLatestArticles } from "../app/catalogue/actions/getLatestArticles";
import { getTopArticles } from "../app/catalogue/actions/getTopArticles";
import { getPlaylists } from "../app/catalogue/actions/getPlaylists";

const CACHE_REVALIDATE_TIME = 300; // 5 minutes
const DUMMY_USER_ID = "server-cache";

export async function initializeServerCache() {
  console.log("Starting cache initialization...");
  try {
    await Promise.all([
      getCachedFeaturedArticles(),
      getCachedLatestArticles(),
      getCachedTopArticles(),
      getCachedPlaylists(),
    ]);
    console.log("Cache initialization completed");
    return true;
  } catch (error) {
    console.error("Cache initialization failed:", error);
    return false;
  }
}

export const getCachedFeaturedArticles = unstable_cache(
  async () => {
    console.log("🔴 Fetching featured articles");
    const result = await getFeaturedArticles({ userId: DUMMY_USER_ID });
    return {
      featuredArticles: result.featuredArticles.map((article) => ({
        ...article,
        isBookmarked: false,
      })),
    };
  },
  ["featured-articles"],
  { revalidate: CACHE_REVALIDATE_TIME, tags: ["articles"] },
);

export const getCachedLatestArticles = unstable_cache(
  async () => {
    console.log("🔴 Fetching latest articles");
    const result = await getLatestArticles({ userId: DUMMY_USER_ID });
    return {
      latestArticles: result.latestArticles.map((article) => ({
        ...article,
        isBookmarked: false,
      })),
    };
  },
  ["latest-articles"],
  { revalidate: CACHE_REVALIDATE_TIME, tags: ["articles"] },
);

export const getCachedTopArticles = unstable_cache(
  async () => {
    console.log("🔴 Fetching top articles");
    const result = await getTopArticles({ userId: DUMMY_USER_ID });
    return {
      topArticlesByCategory: result.topArticlesByCategory.map((category) => ({
        ...category,
        articles: category.articles.map((article) => ({
          ...article,
          isBookmarked: false,
        })),
      })),
    };
  },
  ["top-articles"],
  { revalidate: CACHE_REVALIDATE_TIME, tags: ["articles"] },
);

export const getCachedPlaylists = unstable_cache(
  async () => {
    console.log("🔴 Fetching playlists");
    return getPlaylists();
  },
  ["playlists"],
  { revalidate: CACHE_REVALIDATE_TIME, tags: ["playlists"] },
);
