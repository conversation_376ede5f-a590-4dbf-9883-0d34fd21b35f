import OpenAI from "openai";

if (!process.env.OPENAI_API_KEY) {
  throw new Error("Missing OPENAI_API_KEY");
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Create an embedding for the provided text using OpenAI Embeddings API
 */
export async function createEmbedding(text: string): Promise<number[]> {
  const response = await openai.embeddings.create({
    model: "text-embedding-3-small",
    input: text,
  });
  return response.data[0].embedding;
}

/**
 * Stream a chat completion from GPT-4o (or specified model)
 */
export function streamChatCompletion({
  messages,
  model = "gpt-4o-mini",
  temperature = 0.3,
}: {
  messages: { role: "system" | "user" | "assistant"; content: string }[];
  model?: string;
  temperature?: number;
}) {
  // Returns an async iterable of tokens
  return openai.chat.completions.create({
    model,
    temperature,
    stream: true,
    messages,
  });
}
