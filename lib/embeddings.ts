import { createEmbedding } from "./openai";

/**
 * Generate an embedding vector for the given query using OpenAI only
 * @param query The search query to embed
 * @returns A normalized embedding vector
 */
export async function getEmbedding(query: string) {
  // Preprocess the query for better embedding quality
  const processedQuery = preprocessQuery(query);
  
  try {
    // Use OpenAI embeddings directly
    const embedding = await createEmbedding(processedQuery);
    
    // Normalize the vector for better cosine similarity calculation
    return normalizeVector(embedding);
  } catch (error) {
    console.error("Error getting embedding from OpenAI:", error);
    throw new Error(`Failed to get embedding: ${error}`);
  }
}

/**
 * Normalize a vector to unit length for consistent similarity scoring
 * @param vector The input vector
 * @returns The normalized vector
 */
function normalizeVector(vector: number[]): number[] {
  // Calculate the magnitude (L2 norm)
  const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
  
  // Prevent division by zero
  if (magnitude === 0) {
    return vector;
  }
  
  // Normalize each value by dividing by magnitude
  return vector.map(val => val / magnitude);
}

/**
 * Preprocess a query to improve embedding quality
 * @param query The raw search query
 * @returns A processed query for better embedding
 */
function preprocessQuery(query: string): string {
  // Trim whitespace and ensure non-empty
  const trimmed = query.trim();
  if (!trimmed) {
    return "research paper";
  }
  
  // For comma-separated lists, enhance the prompt structure
  if (trimmed.includes(',')) {
    const terms = trimmed
      .split(',')
      .map(term => term.trim())
      .filter(term => term.length > 0);
    
    return `research papers about ${terms.join(", ")}`;
  }
  
  // For single terms, add context if very short
  if (trimmed.length < 5) {
    return `research about ${trimmed}`;
  }
  
  return trimmed;
}
