import { NextResponse, type NextRequest } from "next/server";
import { createClient } from "./server";
import { ROUTE_CONFIG } from "../types/middleware";

function redirectToSignIn(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const redirectUrl = new URL("/signin", requestUrl.origin);
  redirectUrl.search = requestUrl.search;
  const response = NextResponse.redirect(redirectUrl);

  response.cookies.set("originalUrl", requestUrl.pathname + requestUrl.search, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 60 * 60, // 1 hour
  });

  return response;
}

function redirectToHome(request: NextRequest) {
  const originalUrl = request.cookies.get("originalUrl");
  const redirectUrl = new URL(
    originalUrl?.value || "/homepage",
    new URL(request.url).origin,
  );
  return NextResponse.redirect(redirectUrl);
}

export async function updateSession(request: NextRequest) {
  const supabase = createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  const requestUrl = new URL(request.url);
  const path = requestUrl.pathname;
  console.log("Path requested", path);
  const response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  if (user) {
    response.headers.set("x-superbase-user-id", user.id || "");
    response.headers.set("x-user-email", user.email || "");
  }
  // Handle authentication states
  if (!user) {
    // Check if the current path requires authentication
    const isProtectedRoute = ROUTE_CONFIG.protected.some(
      (route) => path.startsWith(route) || path.endsWith("/darwinai"),
    );

    if (isProtectedRoute) {
      return redirectToSignIn;
    }
  } else {
    // User is authenticated
    const isAuthRoute = ROUTE_CONFIG.auth.some((route) =>
      path.startsWith(route),
    );

    if (isAuthRoute) {
      return redirectToHome(request);
    }
  }
  return response;
}
