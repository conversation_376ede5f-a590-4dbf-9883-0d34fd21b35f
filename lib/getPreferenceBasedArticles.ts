import { unstable_cache } from "next/cache";
import prisma from "@/lib/prisma";
import { shuffleArray, getDailySeed } from "@/lib/utils";

export const getPreferenceBasedArticles = unstable_cache(
  async (userId: string) => {
    const dailySeed = getDailySeed();
    const userPreferences = await prisma.preference.findUnique({
      where: { userId },
    });
    if (!userPreferences) return [];

    const groups = [
      userPreferences.group1,
      userPreferences.group2,
      userPreferences.group3,
      userPreferences.group4,
      userPreferences.group5,
      userPreferences.group6,
      userPreferences.group7,
    ].filter((group) => group && group.length > 0);

    const selectedArticles = await Promise.all(
      groups.map(async (group) => {
        const randomArticles = shuffleArray(group, dailySeed).slice(0, 2);
        return prisma.article.findMany({
          where: { id: { in: randomArticles } },
          include: {
            categories: {
              select: {
                name: true,
              },
            },
            articleImage: true,
          },
        });
      }),
    );

    return shuffleArray(selectedArticles.flat(), dailySeed);
  },
  ["preference-based-articles"],
  { revalidate: 86400 }, // 24 hours in seconds
);
