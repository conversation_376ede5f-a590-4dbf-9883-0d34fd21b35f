"use client";

import { useEffect } from "react";

let branch: any;
let isBranchInitialized = false;
let isBranchBlocked = false;

export const initBranch = async () => {
  if (
    typeof window !== "undefined" &&
    !isBranchInitialized &&
    !isBranchBlocked
  ) {
    try {
      const branchModule = await import("branch-sdk");
      branch = branchModule.default;
      await new Promise<void>((resolve, reject) => {
        branch.init(
          "key_live_nvp2zgOhHPRyHi1B11uS3ckeFzg9AED2",
          { no_journeys: true },
          (err: Error | null, data: any) => {
            if (err) {
              console.error("Branch initialization error:", err);
              isBranchBlocked = true;
              reject(err);
            } else {
              isBranchInitialized = true;
              console.log("Branch initialized");
              resolve();
            }
          },
        );
      });
    } catch (error) {
      console.error("Branch initialization failed:", error);
      isBranchBlocked = true;
    }
  }
};

const logEvent = async (eventName: string, eventData: Record<string, any>) => {
  if (!isBranchInitialized) {
    await initBranch();
  }

  if (isBranchInitialized && branch) {
    // Creating a proper Branch event object
    const eventObject = {
      ...eventData,
      developer_identity: "Alex Hu", // Using userId as developer_identity
    };

    branch.logEvent(eventName, eventObject, (err: Error | null) => {
      if (err) {
        console.error(`Error logging ${eventName} event:`, err);
      } else {
        console.log(`Successfully logged ${eventName} event`);
      }
    });
  }
};

export const useBranchInit = () => {
  useEffect(() => {
    initBranch();
  }, []);
};

export const trackSignUp = async (userId: string) => {
  await logEvent("COMPLETE_REGISTRATION", {
    userId,
  });
};

export const trackCheckoutInitiated = async (
  userId: string,
  planName: string,
  amount: number,
) => {
  await logEvent("INITIATE_PURCHASE", {
    userId,
    planName,
  });
};

export const trackCheckoutSuccess = async (
  userId: string,
  planName: string,
  amount: number,
) => {
  await logEvent("PURCHASE", {
    userId,
    planName,
    amount,
    currency: "USD", // Assuming USD, adjust if needed
  });
};

export const trackUnsubscribe = async (userId: string, planName: string) => {
  await logEvent("UNSUBSCRIBE", {
    userId,
    planName,
  });
};
