import { createClient } from "@supabase/supabase-js";
import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error("Missing Supabase URL or service role key");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

const prisma = new PrismaClient();

async function syncUsers() {
  console.log("Starting user sync...");

  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 180);

  try {
    let page = 0;
    const perPage = 1000;
    let allUsers: any[] = [];
    let hasMore = true;
    const maxPages = 100; // Safety check to prevent infinite loops

    while (hasMore && page < maxPages) {
      const { data: authUsers, error } = await supabase.auth.admin.listUsers({
        page: page,
        perPage: perPage,
      });

      if (error) {
        throw error;
      }

      if (authUsers.users.length === 0) {
        hasMore = false;
      } else {
        allUsers = allUsers.concat(authUsers.users);
        page++;
      }
    }

    if (page === maxPages) {
      console.warn(
        `Reached maximum number of pages (${maxPages}). There might be more users.`,
      );
    }

    console.log(`Found ${allUsers.length} total users`);

    const recentUsers = allUsers.filter(
      (user) => new Date(user.created_at) >= oneWeekAgo,
    );

    console.log(`Found ${recentUsers.length} users created in the last week`);

    let addedUsers = 0;

    await Promise.all(
      recentUsers.map(async (authUser: { email: string; id: string }) => {
        const existingEmail = await prisma.user.findUnique({
          where: { email: authUser.email },
        });

        const existingId = await prisma.user.findUnique({
          where: { supabaseUserId: authUser.id },
        });

        if (!existingId && !existingEmail && authUser.email) {
          console.log({ email: authUser.email });
          try {
            await prisma.user.create({
              data: {
                email: authUser.email,
                supabaseUserId: authUser.id.toUpperCase(),
                isWebsite: true,
              },
            });
            addedUsers++;
          } catch {
            console.log("Couldnt create user");
            console.log({ email: authUser.email, id: authUser.id });
          }
        }
      }),
    );

    console.log(
      `Sync complete. Added ${addedUsers} new users to the users table.`,
    );
  } catch (error) {
    console.error("Error during user sync:", error);
  } finally {
    await prisma.$disconnect();
  }
}

syncUsers();
