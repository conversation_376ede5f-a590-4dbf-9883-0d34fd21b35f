import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Function to create a URL-safe slug
function createUrlSafeSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)+/g, "");
}

// Function to check if a slug is valid (contains only lowercase alphanumeric and hyphens)
function isValidSlug(slug: string): boolean {
  return /^[a-z0-9-]+$/.test(slug);
}

async function updateSlugs(dryRun: boolean = false) {
  try {
    // Fetch all articles
    const articles = await prisma.article.findMany({
      select: {
        id: true,
        title: true,
        slug: true,
      },
    });

    if (articles.length === 0) {
      console.log("No articles found");
      return;
    }

    const updates = [];

    // Prepare updates
    for (const article of articles) {
      let newSlug = article.slug;

      if (!isValidSlug(article.slug)) {
        newSlug = createUrlSafeSlug(article.title);
      }

      if (newSlug !== article.slug) {
        updates.push({
          id: article.id,
          oldSlug: article.slug,
          newSlug: newSlug,
        });
      }
    }

    if (dryRun) {
      console.log("Dry run: The following changes would be made:");
      updates.forEach((update) => {
        console.log(
          `Article ${update.id}: ${update.oldSlug} -> ${update.newSlug}`,
        );
      });
    } else {
      // Batch update
      await prisma.$transaction(
        updates.map((update) =>
          prisma.article.update({
            where: { id: update.id },
            data: { slug: update.newSlug },
          }),
        ),
      );
      console.log(`Updated ${updates.length} article slugs`);
    }

    console.log("Slug update process completed");
  } catch (error) {
    console.error("Error updating slugs:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the update function
// Set to true for a dry run, false to apply changes
updateSlugs(true).catch(console.error);
