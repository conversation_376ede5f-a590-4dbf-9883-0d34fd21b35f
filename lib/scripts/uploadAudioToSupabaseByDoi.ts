import { createClient } from "@supabase/supabase-js";
import { PrismaClient } from "@prisma/client";
import fs from "fs";
import path from "path";

// Supabase configuration (for file storage)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE;

if (!supabaseUrl || !supabaseKey) {
  throw new Error("Missing Supabase URL or key in environment variables");
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Prisma client initialization
const prisma = new PrismaClient();

// Function to read audio files from a folder
async function getAudioFiles(folderPath: string): Promise<string[]> {
  return new Promise((resolve, reject) => {
    fs.readdir(folderPath, (err, files) => {
      if (err) reject(err);
      const audioFiles = files.filter((file) => {
        const ext = path.extname(file).toLowerCase();
        return [".mp3", ".wav", ".ogg", ".m4a"].includes(ext);
      });
      resolve(audioFiles);
    });
  });
}

// Function to upload file to Supabase
async function uploadFileToSupabase(
  filePath: string,
  fileName: string,
): Promise<string | null> {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    const { data, error } = await supabase.storage
      .from("audio")
      .upload(fileName, fileBuffer, { upsert: true });

    if (error) {
      console.error("Error uploading file to Supabase:", error);
      return null;
    }

    const { data: publicUrlData } = supabase.storage
      .from("audio")
      .getPublicUrl(fileName);

    return publicUrlData.publicUrl;
  } catch (error) {
    console.error("Error in uploadFileToSupabase:", error);
    return null;
  }
}
// Main function to process audio files
async function processAudioFiles(folderPath: string) {
  try {
    console.log(`Processing audio files in ${folderPath}`);
    const audioFiles = await getAudioFiles(folderPath);
    console.log(`Found ${audioFiles.length} audio files`);

    for (let file of audioFiles) {
      const filePath = path.join(folderPath, file);
      file = file.replace("_", "/");
      console.log(`Processing file: ${file}`);
      const publicUrl = await uploadFileToSupabase(filePath, file);
      if (!publicUrl) {
        console.log(`Failed to upload ${file} to Supabase. Skipping...`);
        continue;
      }

      const doi = file.split(".wav")[0];
      console.log(doi);

      try {
        // Update the article with the audio URL using Prisma
        await prisma.article.update({
          where: { doi: doi },
          data: {
            audioId: publicUrl,
            categories: {
              connect: {
                name: "Audio",
              },
            },
          },
        });

        console.log(`Updated article ${doi} with audio URL: ${publicUrl}`);
      } catch (error) {
        console.error(`Error updating article ${doi}:`, error);
      }
    }
    console.log("Finished processing all audio files");
  } catch (error) {
    console.error("Error processing audio files:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Usage
const audioFolderPath =
  process.argv[2] || path.join(process.cwd(), "audio_files");
console.log(`Starting audio processing script`);
processAudioFiles(audioFolderPath);
