import { createClient } from "@supabase/supabase-js";
import { PrismaClient } from "@prisma/client";
import { Pinecone } from "@pinecone-database/pinecone";
import fs from "fs";
import path from "path";

// Supabase configuration (for file storage)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE;

if (!supabaseUrl || !supabaseKey) {
  throw new Error("Missing Supabase URL or key in environment variables");
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Prisma client initialization
const prisma = new PrismaClient();

// Pinecone client initialization
const pc = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY!,
});

// Function to read audio files from a folder
async function getAudioFiles(folderPath: string): Promise<string[]> {
  return new Promise((resolve, reject) => {
    fs.readdir(folderPath, (err, files) => {
      if (err) reject(err);
      const audioFiles = files.filter((file) => {
        const ext = path.extname(file).toLowerCase();
        return [".mp3", ".wav", ".ogg", ".m4a"].includes(ext);
      });
      resolve(audioFiles);
    });
  });
}

// Function to upload file to Supabase
async function uploadFileToSupabase(
  filePath: string,
  fileName: string,
): Promise<string | null> {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    const { data, error } = await supabase.storage
      .from("audio")
      .upload(fileName, fileBuffer);

    if (error) {
      console.error("Error uploading file to Supabase:", error);
      return null;
    }

    const { data: publicUrlData } = supabase.storage
      .from("audio")
      .getPublicUrl(fileName);

    return publicUrlData.publicUrl;
  } catch (error) {
    console.error("Error in uploadFileToSupabase:", error);
    return null;
  }
}

// Function to vectorize the search query
async function vectorise(query: string): Promise<number[] | null> {
  try {
    const url = "https://api.openai.com/v1/embeddings";
    const headers = {
      "Content-Type": "application/json",
      Authorization: "Bearer " + process.env.OPENAI_API_KEY,
    };
    const data = {
      input: query,
      model: "text-embedding-ada-002",
    };

    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();
    return result.data[0].embedding;
  } catch (error) {
    console.error("Error in vectorise:", error);
    return null;
  }
}

// Function to search for article and get its ID
async function getArticleId(fileName: string): Promise<string | null> {
  const searchTerm = path.parse(fileName).name; // Use filename without extension as search term
  console.log(`Searching for article: ${searchTerm}`);
  try {
    const vectorisedQuery = await vectorise(searchTerm);

    if (!vectorisedQuery) {
      console.error("Failed to vectorize the search term");
      return null;
    }

    const pineconeIndex = pc.Index("research-articles");

    const queryResponse = await pineconeIndex.query({
      topK: 1,
      vector: vectorisedQuery,
      includeMetadata: true,
    });

    console.log({ queryResponse });

    if (queryResponse.matches.length > 0) {
      return queryResponse.matches[0].id;
    }

    return null;
  } catch (error) {
    console.error(`Error searching for article: ${error}`);
    return null;
  }
}

// Main function to process audio files
async function processAudioFiles(folderPath: string) {
  try {
    console.log(`Processing audio files in ${folderPath}`);
    const audioFiles = await getAudioFiles(folderPath);
    console.log(`Found ${audioFiles.length} audio files`);

    for (const file of audioFiles) {
      console.log(`Processing file: ${file}`);
      const filePath = path.join(folderPath, file);
      const publicUrl = await uploadFileToSupabase(filePath, file);

      if (!publicUrl) {
        console.log(`Failed to upload ${file} to Supabase. Skipping...`);
        continue;
      }

      console.log(`Uploaded ${file} to Supabase`);
      const articleId = await getArticleId(file);

      if (articleId) {
        try {
          // Update the article with the audio URL using Prisma
          await prisma.article.update({
            where: { id: articleId },
            data: { audioId: publicUrl },
          });

          console.log(
            `Updated article ${articleId} with audio URL: ${publicUrl}`,
          );
        } catch (error) {
          console.error(`Error updating article ${articleId}:`, error);
        }
      } else {
        console.log(`No matching article found for file: ${file}`);
      }
    }
    console.log("Finished processing all audio files");
  } catch (error) {
    console.error("Error processing audio files:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Usage
const audioFolderPath =
  process.argv[2] || path.join(process.cwd(), "audio_files");
console.log(`Starting audio processing script`);
processAudioFiles(audioFolderPath);
