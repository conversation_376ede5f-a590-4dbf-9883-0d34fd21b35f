import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

const articlesWithAudio = [
  {
    doi: "10.3389/fpsyg.2024.1414499",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//Mental%20Toughness%20and%20Choking%20Susceptibility%20in%20Athletes.wav",
  },
  {
    doi: "10.1257/aer.20230515",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//Measuring%20Science_%20Performance%20Metrics%20and%20Talent%20Allocation.wav",
  },
  {
    doi: "10.53383/100118",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//Hedging%20Residential%20Price%20Dynamics%20in%20Australia_s%20Capital%20Cities.wav",
  },
  {
    doi: "10.1093/mnras/stae1672",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//GALAXYFLOW_%20Upsampling%20Milky%20Way%20Simulations.wav",
  },
  {
    doi: "10.1093/oso/9780198753858.003.0001",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//Fellow%20Creatures_%20Obligations%20to%20Other%20Animals.wav",
  },
  {
    doi: "10.1093/acprof:oso/9780195311105.003.0001",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//Facing%20Up%20to%20the%20Problem%20of%20Consciousness.wav",
  },
  {
    doi: "10.1007/s11357-018-0036-9",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//Defining%20and%20Measuring%20Healthspan.wav",
  },
  {
    doi: "10.1038/s41591-024-03218-w",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//Advancing%20Global%20Antibiotic%20R&D%20and%20Access.wav",
  },
  {
    doi: "10.1257/aer.20220639",
    audioId:
      "https://cnducrozrpvuuztqkxhz.supabase.co/storage/v1/object/public/audio//Beliefs%20and%20Strategies%20in%20Repeated%20Games.wav",
  },
];

async function updateArticleAudio() {
  for (const article of articlesWithAudio) {
    try {
      const updatedArticle = await prisma.article.update({
        where: { doi: article.doi },
        data: { audioId: article.audioId },
      });
      console.log(`Successfully updated article with DOI ${article.doi}`);
    } catch (error) {
      console.error(`Error updating article with DOI ${article.doi}:`, error);
    }
  }
}

updateArticleAudio()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
