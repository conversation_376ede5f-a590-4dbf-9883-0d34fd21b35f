import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
async function main() {
  const img = {
    src: "",
    name: "",
    alt: "",
  };

  const image = await prisma.image.create({
    data: img,
  });

  const playlist = {
    name: "test",
    description: "test",
    image: {
      connect: {
        id: image.id,
      },
    },
    articles: {
      connect: [
        { id: "cm4tbr4iv000n10a42kvpv6hj" },
        { id: "cm4tbpygu000k10a4awl9ws73" },
        { id: "cm4tboq8u000h10a4f4jdcu4w" },
        { id: "cm4tbnmcb000e10a4bfeuvgwn" },
        { id: "cm4tbmbsq000b10a4z2eq8oip" },
        { id: "cm4tbkrb5000810a41fda34bd" },
        { id: "cm4tbja22000510a4x078tf3v" },
        { id: "cm4tbi4l1000210a4agxdpdpd" },
      ],
    },
  };

  const newPlaylist = await prisma.playlist.create({
    data: playlist,
  });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
