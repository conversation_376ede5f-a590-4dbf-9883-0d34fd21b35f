import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function deleteFeaturedArticle(articleId: string) {
  try {
    // First, find the specific categories
    const physics = await prisma.catergory.findUnique({
      where: {
        name: "Physics",
      },
    });

    const chemical = await prisma.catergory.findUnique({
      where: {
        name: "Chemical Science",
      },
    });

    if (!physics || !chemical) {
      throw new Error("Required categories not found");
    }

    // Update the article to only be in these two categories
    await prisma.article.update({
      where: { id: articleId },
      data: {
        categories: {
          set: [{ id: physics.id }, { id: chemical.id }],
        },
      },
    });

    // Then, fetch the updated article
    const updatedArticle = await prisma.article.findUnique({
      where: { id: articleId },
      include: { categories: true },
    });

    if (!updatedArticle) {
      throw new Error("Article not found after update");
    }

    console.log(`Successfully updated article: ${updatedArticle.title}`);
    console.log(
      "New categories:",
      updatedArticle.categories.map((c) => c.name).join(", "),
    );

    return updatedArticle;
  } catch (error) {
    console.error("Error updating featured article:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Example usage:
deleteFeaturedArticle("article_id_here")
  .then(() => console.log("Deletion complete"))
  .catch(console.error);

export { deleteFeaturedArticle };
