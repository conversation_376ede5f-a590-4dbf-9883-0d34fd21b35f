// import { createClient } from '@supabase/supabase-js'

// async function resetPassword() {

//     const id = "64eba24f-a1e4-406e-82f0-8f61c8f78763"
//     const { data, error } = await supabase.auth.admin.updateUserById(id, { password: "Outread@1234" });

//     if (error) {
//         console.error(error);
//         return { error: "Failed to reset password" };
//     }

//     if (!data) {
//         console.error("Failed to reset password");
//         return { error: "Failed to reset password" };
//     }
// }

// (async () => {
//     try {
//         await resetPassword();
//         console.log("Password reset successfully");
//     } catch (error) {
//         console.error(error);
//     }
// })();
