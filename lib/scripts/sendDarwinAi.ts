import dotenv from "dotenv";
import path from "path";
import { PrismaClient } from "@prisma/client";
import { MailerS<PERSON>, Email<PERSON>ara<PERSON>, Sender, Recipient } from "mailersend";

const alreadyEmailed = [
  "dhruvsing<PERSON><PERSON><EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

dotenv.config({
  path: path.join(__dirname, "../../", ".env"),
});

function createDarwinAiEmail(email: string) {
  const sentFrom = new Sender("<EMAIL>", "Janhvi from Outread");

  const emailParams = new EmailParams()
    .setFrom(sentFrom)
    .setTo([new Recipient(email)])
    .setReplyTo(sentFrom)
    .setSubject("We'd love your feedback on DarwinAI").setHtml(`
      <p>Hey 👋</p>

      <p>I'm Janhvi, CEO and Co-founder of Outread. We would love your feedback on our product, DarwinAI.</p>

      <p>We're continuously looking to improve the user experience at Outread. We'd love to hear your thoughts as a valued user! Could you answer two quick questions?</p>
      <ol>
        <li>What problem are you trying to solve with Outread? (e.g., discovering new papers or answering specific questions)</li>
        <li>What did you hope to find but didn't?</li>
      </ol>
      <p>Thanks for helping us improve!</p>

      <p>Cheers,<br>Janhvi</p>
    `);

  return emailParams;
}

(async function sendDarwinAiEmails(): Promise<void> {
  const mailerSend = new MailerSend({
    apiKey: process.env.MAILERSEND_API_KEY as string,
  });
  const prisma = new PrismaClient();

  try {
    // Find all unique users who have used DarwinAI (have a TrendAnalysisResponse)
    const users = await prisma.user.findMany({
      where: {
        trendAnalysisResponses: {
          some: {},
        },
      },
      select: {
        email: true,
      },
      distinct: ["email"],
    });

    const emails: string[] = users
      .map((user) => user.email)
      .filter((email) => alreadyEmailed.includes(email) == false);

    console.log(emails.length, "users found who have used DarwinAI");

    const bulkEmails = [];
    for (let i = 0; i < emails.length; i++) {
      const email = emails[i];
      console.log(`Preparing email ${i + 1}/${emails.length} for:`, email);
      bulkEmails.push(createDarwinAiEmail(email));
    }

    const result = await mailerSend.email.sendBulk(bulkEmails);
    console.log("Emails sent. Result:", result);
  } catch (error) {
    console.error("Error in sendDarwinAiEmails:", error);
  } finally {
    await prisma.$disconnect();
  }
})();
