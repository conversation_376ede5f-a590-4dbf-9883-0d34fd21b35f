import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export const getDailySeed = (): number => {
  const now = new Date();
  return now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate();
};

export const shuffleArray = <T>(array: T[], seed: number): T[] => {
  const shuffled = [...array];
  const rng = seedRandom(seed);

  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(rng() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Simple seeded random number generator
function seedRandom(seed: number) {
  let state = seed;
  return () => {
    state = (state * 1664525 + 1013904223) % 2 ** 32;
    return state / 2 ** 32;
  };
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
