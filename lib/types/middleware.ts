// lib/types/middleware.ts
export type RouteConfig = {
  protected: readonly string[];
  auth: readonly string[];
};

export const ROUTE_CONFIG: RouteConfig = {
  protected: [
    "/settings",
    "/subscription",
    "/playlists",
    "/category",
    "/homepage",
    "/article",
    "/all-articles",
    "/search",
    "/client",
    "/curie",
  ],
  auth: ["/signin", "/reset-password", "send-reset-email"],
} as const;

export type RedirectConfig = {
  destination: string;
  permanent: boolean;
};
