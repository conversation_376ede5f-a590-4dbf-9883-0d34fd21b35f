import { unstable_cache } from "next/cache";
import prisma from "./prisma";
import { HomePageArticle, HomepageArticleWithBookmark } from "../types/article";

export const getUserBookmarks = unstable_cache(
  async (userId: string) => {
    const bookmarks = await prisma.bookmarkedArticle.findMany({
      where: { userId },
      select: { articleId: true },
    });

    return bookmarks.map((b) => b.articleId);
  },
  ["user-bookmarks"],
  { revalidate: 60, tags: ["bookmarks"] },
);

export function mergeArticlesWithBookmarks<T extends HomePageArticle>(
  articles: T[],
  bookmarks: string[],
): (T & HomepageArticleWithBookmark)[] {
  const bookmarkSet = new Set(bookmarks);

  return articles.map((article) => {
    const isBookmarked = bookmarkSet.has(article.id);
    return {
      ...article,
      isBookmarked,
    };
  });
}
