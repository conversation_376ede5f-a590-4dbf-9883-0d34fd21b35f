/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ["@node-rs/argon2"],
    // ppr: true, // Partial Prerendering (Next.js 14)
  },
  images: {
    unoptimized: true, // For local images
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cnducrozrpvuuztqkxhz.supabase.co",
      },
    ],
  },
  eslint: {
    dirs: ["app"], // Keep scanning app directory
    ignoreDuringBuilds: true, // Only ignore in production
    // Only warn for development, error for production
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  // Optimize build performance
  swcMinify: true, // Use SWC for minification
  poweredByHeader: false, // Remove X-Powered-By header
  reactStrictMode: true,
  // Improve build caching
  generateBuildId: async () => {
    return "build-" + Date.now();
  },
};

export default nextConfig;
