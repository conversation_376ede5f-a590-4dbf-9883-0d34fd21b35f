# Darwin Research Q&A Chat Tool

## Overview

Darwin is a research Q&A chat tool integrated into the Outread website. It provides an AI-powered research assistant that can answer questions about scientific papers with citations from the latest research literature.

## Features

### 1. Streaming Chat
- Real-time streaming responses with token-by-token output
- Integration with Pinecone for semantic search
- Citation parsing with inline tags: `<|INSIGHT|>id||title||authors||url`

### 2. Insight Cards Sidebar
- Animated citation cards with paper details
- Progressive slide-in animations via Framer Motion
- Hover effects and interactive elements

### 3. Conversational Intelligence
- Short query detection with clarification modal
- Follow-up question suggestions
- Full chat context preservation
- Two answer modes: "Deep Dive" vs "Executive Summary"

### 4. Rich Citation Controls
- Sidebar filters for year, impact factor, novelty score, open-access
- Dynamic re-ranking of citations and context
- "Source-Only" toggle for citation excerpts
- Export functionality (BibTeX/EndNote/CSV)
- Per-card highlighting and annotations

### 5. Enhanced UI/UX
- Fully responsive design (desktop + mobile)
- Light/Dark mode toggle
- Skeleton loaders for streaming content
- Mini visual graphs in citation cards

### 6. Quality Metrics & Feedback
- 1-5 star rating system after each response
- Admin dashboard with usage analytics
- Response time tracking
- Citation count analytics

## Architecture

### Frontend Components
- `Darwin.tsx` - Main component orchestrating the chat interface
- `ChatInterface.tsx` - Chat messages and input handling
- `InsightSidebar.tsx` - Citation display and filtering
- `InitialLanding.tsx` - Welcome screen with suggested queries

### API Endpoints
- `/api/chat` - Streaming chat responses with citations
- `/api/feedback` - Rating and feedback collection

### Key Technologies
- **Next.js 14** with TypeScript
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **Pinecone** for vector search
- **OpenAI** for text generation
- **Recharts** for analytics visualization

## Usage

### Accessing Darwin
Navigate to `/insight` to access the Darwin research assistant.

### Example Queries
- "What are the latest advances in CRISPR gene editing?"
- "How does machine learning impact drug discovery?"
- "What are the environmental impacts of renewable energy?"

### Features in Action
1. **Ask a Question**: Type your research question in the chat interface
2. **View Citations**: See relevant papers in the right sidebar
3. **Filter Results**: Use the filter panel to refine citations
4. **Rate Responses**: Provide feedback on answer quality
5. **Export Data**: Download citations in various formats

## Development

### Setup
1. Ensure all dependencies are installed:
   ```bash
   npm install
   ```

2. Set up environment variables:
   ```
   PINECONE_API_KEY=your_pinecone_key
   OPENAI_API_KEY=your_openai_key
   ```

3. Run the development server:
   ```bash
   npm run dev
   ```

### File Structure
```
app/
├── insight/
│   └── page.tsx                 # Main Darwin page
├── api/
│   ├── chat/
│   │   └── route.ts             # Chat API endpoint
│   └── feedback/
│       └── route.ts             # Feedback API endpoint
├── component/
│   └── Darwin/
│       ├── Darwin.tsx           # Main Darwin component
│       ├── ChatInterface.tsx    # Chat interface
│       ├── InsightSidebar.tsx   # Citation sidebar
│       ├── InitialLanding.tsx   # Welcome screen
│       └── darwin.css           # Darwin-specific styles
└── admin/
    └── darwin/
        └── page.tsx             # Analytics dashboard
```

### Customization

#### Adding New Citation Sources
1. Update the Pinecone index configuration in `/api/chat/route.ts`
2. Modify citation parsing logic in `ChatInterface.tsx`
3. Update citation display in `InsightSidebar.tsx`

#### Styling
- Main styles: `app/component/Darwin/darwin.css`
- Tailwind classes for responsive design
- Dark mode support via CSS variables

#### Analytics
- Admin dashboard: `/admin/darwin`
- Real-time metrics tracking
- Export functionality for data analysis

## Integration with Outread

Darwin is fully integrated with the existing Outread website:
- Uses existing authentication system
- Shares styling with main site
- Leverages existing Pinecone setup
- Integrates with current analytics

## Future Enhancements

1. **Advanced Search**: Multi-modal search with images and documents
2. **Collaboration**: Shared research sessions and annotations
3. **Personalization**: User-specific research preferences and history
4. **Advanced Analytics**: More detailed usage patterns and insights
5. **Mobile App**: Native mobile application for research on-the-go

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository. 